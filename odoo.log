2025-10-07 19:56:47,553 25176 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 19:56:47,554 25176 INFO ? odoo: Odoo version 19.0 
2025-10-07 19:56:47,554 25176 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 19:56:47,554 25176 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 19:56:47,555 25176 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 19:56:47,579 25176 CRITICAL ? odoo.modules.module: Couldn't load module base 
2025-10-07 19:56:47,580 25176 ERROR ? odoo.service.server: Failed to load server-wide module `base`. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 3, in <module>
    from . import assetsbundle
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\assetsbundle.py", line 14, in <module>
    from rjsmin import jsmin as rjsmin
ModuleNotFoundError: No module named 'rjsmin'
2025-10-07 19:56:47,886 25176 CRITICAL ? odoo.modules.module: Couldn't load module web 
2025-10-07 19:56:47,887 25176 ERROR ? odoo.service.server: Failed to load server-wide module `web`.
    The `web` module is provided by the addons found in the `openerp-web` project.
    Maybe you forgot to add those addons in your addons_path configuration. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "d:\odoo_19.0_new\odoo_src\addons\web\__init__.py", line 4, in <module>
    from . import controllers
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\__init__.py", line 4, in <module>
    from . import binary
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\binary.py", line 20, in <module>
    from odoo.addons.base.models.assetsbundle import ANY_UNIQUE
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 3, in <module>
    from . import assetsbundle
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\assetsbundle.py", line 14, in <module>
    from rjsmin import jsmin as rjsmin
ModuleNotFoundError: No module named 'rjsmin'
2025-10-07 19:56:48,102 25176 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 19:59:13,212 4900 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 19:59:13,213 4900 INFO ? odoo: Odoo version 19.0 
2025-10-07 19:59:13,213 4900 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 19:59:13,213 4900 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 19:59:13,213 4900 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 19:59:13,872 4900 CRITICAL ? odoo.modules.module: Couldn't load module base 
2025-10-07 19:59:13,872 4900 ERROR ? odoo.service.server: Failed to load server-wide module `base`. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 44, in <module>
    pypdf = importlib.import_module(SUBMOD, __spec__.name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\_pypdf2_2.py", line 1, in <module>
    from PyPDF2 import errors, filters, generic, PdfReader, PdfWriter as _Writer
ModuleNotFoundError: No module named 'PyPDF2'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 50, in <module>
    raise ImportError("pypdf implementation not found") from error
ImportError: pypdf implementation not found
2025-10-07 19:59:13,959 4900 CRITICAL ? odoo.modules.module: Couldn't load module web 
2025-10-07 19:59:13,959 4900 ERROR ? odoo.service.server: Failed to load server-wide module `web`.
    The `web` module is provided by the addons found in the `openerp-web` project.
    Maybe you forgot to add those addons in your addons_path configuration. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 44, in <module>
    pypdf = importlib.import_module(SUBMOD, __spec__.name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\_pypdf2_2.py", line 1, in <module>
    from PyPDF2 import errors, filters, generic, PdfReader, PdfWriter as _Writer
ModuleNotFoundError: No module named 'PyPDF2'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "d:\odoo_19.0_new\odoo_src\addons\web\__init__.py", line 4, in <module>
    from . import controllers
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\__init__.py", line 5, in <module>
    from . import database
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 19, in <module>
    from odoo.addons.base.models.ir_qweb import render as qweb_render
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 50, in <module>
    raise ImportError("pypdf implementation not found") from error
ImportError: pypdf implementation not found
2025-10-07 19:59:14,142 4900 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:01:27,769 11372 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:01:27,770 11372 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:01:27,770 11372 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:01:27,770 11372 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:01:27,770 11372 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:01:28,253 11372 CRITICAL ? odoo.modules.module: Couldn't load module base 
2025-10-07 20:01:28,254 11372 ERROR ? odoo.service.server: Failed to load server-wide module `base`. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 22, in <module>
    from . import ir_mail_server
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_mail_server.py", line 17, in <module>
    from OpenSSL import crypto as SSLCrypto
ModuleNotFoundError: No module named 'OpenSSL'
2025-10-07 20:01:28,286 11372 CRITICAL ? odoo.modules.module: Couldn't load module web 
2025-10-07 20:01:28,286 11372 ERROR ? odoo.service.server: Failed to load server-wide module `web`.
    The `web` module is provided by the addons found in the `openerp-web` project.
    Maybe you forgot to add those addons in your addons_path configuration. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "d:\odoo_19.0_new\odoo_src\addons\web\__init__.py", line 4, in <module>
    from . import controllers
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\__init__.py", line 5, in <module>
    from . import database
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 19, in <module>
    from odoo.addons.base.models.ir_qweb import render as qweb_render
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 22, in <module>
    from . import ir_mail_server
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_mail_server.py", line 17, in <module>
    from OpenSSL import crypto as SSLCrypto
ModuleNotFoundError: No module named 'OpenSSL'
2025-10-07 20:01:28,480 11372 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:01:28,595 11372 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:01:28,675 11372 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:18:15,976 22172 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:18:15,978 22172 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:18:15,978 22172 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:18:15,978 22172 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:18:15,978 22172 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:18:16,398 22172 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:18:19,474 22172 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:18:19,551 22172 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:18:19,725 22172 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:06,672 26332 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:21:06,673 26332 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:21:06,673 26332 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:21:06,673 26332 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:21:06,673 26332 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:21:06,932 26332 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:21:07,438 26332 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-10-07 20:21:07,445 26332 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:21:07,507 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:07,576 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:36,938 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:36,975 26332 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:21:36] "GET / HTTP/1.1" 303 - 0 0.000 0.163
2025-10-07 20:21:37,152 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:37,248 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:37,249 26332 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:21:37] "GET /odoo HTTP/1.1" 303 - 0 0.000 0.210
2025-10-07 20:21:37,350 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:38,284 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:38,431 26332 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:21:38] "GET /web/database/selector HTTP/1.1" 200 - 0 0.000 1.151
2025-10-07 20:22:57,193 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:00,326 26332 INFO None odoo.service.db: Create database `school`. 
2025-10-07 20:23:00,491 26332 INFO None odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:00,491 26332 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 439, in dispatch_rpc
    return dispatch(method, params)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 519, in dispatch
    return g[exp_method_name](*params)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 52, in if_db_mgt_enabled
    return func(*args, **kwargs)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 180, in exp_create_database
    _create_empty_database(db_name)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 130, in _create_empty_database
    with closing(db.cursor()) as cr:
                 ~~~~~~~~~^^
  File "D:\odoo_19.0_new\odoo_src\odoo\sql_db.py", line 756, in cursor
    return Cursor(self.__pool, self.__dbname, self.__dsn)
  File "D:\odoo_19.0_new\odoo_src\odoo\sql_db.py", line 358, in __init__
    self._cnx: PsycoConnection = pool.borrow(dsn)
                                 ~~~~~~~~~~~^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\func.py", line 88, in locked
    return func(inst, *args, **kwargs)
  File "D:\odoo_19.0_new\odoo_src\odoo\sql_db.py", line 680, in borrow
    result = psycopg2.connect(
        connection_factory=PsycoConnection,
        **connection_info)
  File "D:\odoo_19.0_new\venv\Lib\site-packages\psycopg2\__init__.py", line 135, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
psycopg2.OperationalError: connection to server at "localhost" (::1), port 5432 failed: FATAL:  password authentication failed for user "odoo"

2025-10-07 20:23:01,468 26332 INFO None odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:01,531 26332 INFO None werkzeug: 127.0.0.1 - - [07/Oct/2025 20:23:01] "POST /web/database/create HTTP/1.1" 200 - 0 0.000 4.405
2025-10-07 20:23:35,038 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:36,664 26332 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 439, in dispatch_rpc
    return dispatch(method, params)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 518, in dispatch
    check_super(passwd)
    ~~~~~~~~~~~^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 63, in check_super
    raise odoo.exceptions.AccessDenied()
odoo.exceptions.AccessDenied: Access Denied
2025-10-07 20:23:37,500 26332 INFO None odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:37,566 26332 INFO None werkzeug: 127.0.0.1 - - [07/Oct/2025 20:23:37] "POST /web/database/create HTTP/1.1" 200 - 0 0.000 2.624
2025-10-07 20:25:07,452 26332 WARNING ? odoo.service.server: Thread <Thread(odoo.service.http.request.13252, started 13252)> virtual real time limit (130/120s) reached. 
2025-10-07 20:25:07,453 26332 INFO ? odoo.service.server: Dumping stacktrace of limit exceeding threads before reloading 
2025-10-07 20:25:07,538 26332 INFO ? odoo.tools.misc: 
# Thread: <Thread(odoo.service.http.request.13252, started 13252)> (db:n/a) (uid:n/a) (url:n/a) (qc:n/a qt:n/a pt:n/a)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1014, in _bootstrap
  self._bootstrap_inner()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1043, in _bootstrap_inner
  self.run()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 994, in run
  self._target(*self._args, **self._kwargs)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socketserver.py", line 697, in process_request_thread
  self.finish_request(request, client_address)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socketserver.py", line 362, in finish_request
  self.RequestHandlerClass(request, client_address, self)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socketserver.py", line 766, in __init__
  self.handle()
File: "D:\odoo_19.0_new\venv\Lib\site-packages\werkzeug\serving.py", line 398, in handle
  super().handle()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\server.py", line 436, in handle
  self.handle_one_request()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\server.py", line 404, in handle_one_request
  self.raw_requestline = self.rfile.readline(65537)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
  return self._sock.recv_into(b) 
2025-10-07 20:33:42,834 6860 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:34:57,177 14512 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:34:57,178 14512 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:34:57,178 14512 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:34:57,178 14512 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:34:57,178 14512 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:34:57,449 14512 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:34:57,993 14512 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:34:58,053 14512 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:34:58,122 14512 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:35:54,997 9800 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:35:54,998 9800 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:35:54,998 9800 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:35:54,998 9800 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:35:54,998 9800 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:35:55,275 9800 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:35:55,769 9800 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:35:55,829 9800 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:35:56,028 9800 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:36:40,927 16656 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:36:40,928 16656 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:36:40,928 16656 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:36:40,928 16656 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:36:40,928 16656 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:36:41,195 16656 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:36:41,680 16656 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:36:41,749 16656 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:36:41,854 16656 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:38:27,268 16656 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:38:27,289 16656 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:38:27] "GET / HTTP/1.1" 303 - 0 0.000 0.103
2025-10-07 20:38:27,347 16656 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:38:27,524 16656 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:38:27,525 16656 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:38:27] "GET /odoo HTTP/1.1" 303 - 0 0.000 0.230
2025-10-07 20:38:27,582 16656 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:38:28,408 16656 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:38:28,443 16656 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:38:28] "GET /web/database/selector HTTP/1.1" 200 - 0 0.000 0.913
2025-10-07 20:45:40,417 11480 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:45:40,418 11480 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:45:40,418 11480 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:45:40,418 11480 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:45:40,418 11480 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:45:40,692 11480 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:45:41,250 11480 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:45:41,296 11480 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:45:41,388 11480 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:46:43,744 11480 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:46:43,844 11480 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:46:43] "GET / HTTP/1.1" 303 - 0 0.000 0.181
2025-10-07 20:46:44,092 11480 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:46:44,135 11480 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:46:44,137 11480 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:46:44] "GET /odoo HTTP/1.1" 303 - 0 0.000 0.254
2025-10-07 20:46:44,190 11480 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:46:45,282 11480 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:46:45,339 11480 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:46:45] "GET /web/database/selector HTTP/1.1" 200 - 0 0.000 1.197
2025-10-07 20:46:59,626 23196 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:46:59,629 23196 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:46:59,629 23196 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:46:59,629 23196 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:46:59,629 23196 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:46:59,900 23196 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:47:00,437 23196 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:47:00,499 23196 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:47:00,581 23196 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:47:47,347 23196 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:47:50,636 23196 INFO None odoo.service.db: Create database `school`. 
2025-10-07 20:47:50,690 23196 INFO None odoo.sql_db: Connection to the database failed 
2025-10-07 20:47:50,691 23196 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 439, in dispatch_rpc
    return dispatch(method, params)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 519, in dispatch
    return g[exp_method_name](*params)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 52, in if_db_mgt_enabled
    return func(*args, **kwargs)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 180, in exp_create_database
    _create_empty_database(db_name)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 130, in _create_empty_database
    with closing(db.cursor()) as cr:
                 ~~~~~~~~~^^
  File "D:\odoo_19.0_new\odoo_src\odoo\sql_db.py", line 756, in cursor
    return Cursor(self.__pool, self.__dbname, self.__dsn)
  File "D:\odoo_19.0_new\odoo_src\odoo\sql_db.py", line 358, in __init__
    self._cnx: PsycoConnection = pool.borrow(dsn)
                                 ~~~~~~~~~~~^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\func.py", line 88, in locked
    return func(inst, *args, **kwargs)
  File "D:\odoo_19.0_new\odoo_src\odoo\sql_db.py", line 680, in borrow
    result = psycopg2.connect(
        connection_factory=PsycoConnection,
        **connection_info)
  File "D:\odoo_19.0_new\venv\Lib\site-packages\psycopg2\__init__.py", line 135, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
psycopg2.OperationalError: connection to server at "localhost" (::1), port 5432 failed: FATAL:  password authentication failed for user "odoo"

2025-10-07 20:47:51,525 23196 INFO None odoo.sql_db: Connection to the database failed 
2025-10-07 20:47:51,561 23196 INFO None werkzeug: 127.0.0.1 - - [07/Oct/2025 20:47:51] "POST /web/database/create HTTP/1.1" 200 - 0 0.000 4.285
2025-10-07 20:48:38,298 2780 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:48:38,299 2780 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:48:38,299 2780 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:48:38,299 2780 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:48:38,299 2780 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:48:38,562 2780 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:48:39,087 2780 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:48:39,143 2780 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:48:39,230 2780 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:51:58,279 22632 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:51:58,280 22632 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:51:58,281 22632 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:51:58,281 22632 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:51:58,281 22632 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:51:58,565 22632 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:51:59,069 22632 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:56:05,625 22632 INFO None odoo.service.db: Create database `school`. 
2025-10-07 20:56:06,348 22632 INFO None odoo.modules.loading: Initializing database school 
2025-10-07 20:56:06,759 22632 INFO None odoo.modules.loading: skipping reset_modules_state, ir_module_module table does not exists 
2025-10-07 20:56:06,760 22632 ERROR None odoo.registry: Failed to load registry 
2025-10-07 20:56:06,760 22632 ERROR None odoo.service.db: CREATE DATABASE failed: 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 71, in _initialize_db
    registry = odoo.modules.registry.Registry.new(db_name, update_module=True, new_db_demo=demo)
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\func.py", line 88, in locked
    return func(inst, *args, **kwargs)
  File "D:\odoo_19.0_new\odoo_src\odoo\orm\registry.py", line 185, in new
    load_modules(
    ~~~~~~~~~~~~^
        registry,
        ^^^^^^^^^
    ...<4 lines>...
        new_db_demo=new_db_demo,
        ^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\loading.py", line 361, in load_modules
    modules_db.initialize(cr)
    ~~~~~~~~~~~~~~~~~~~~~^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\db.py", line 47, in initialize
    for info in odoo.modules.Manifest.all_addon_manifests():
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 321, in all_addon_manifests
    for file_name in os.listdir(adp):
                     ~~~~~~~~~~^^^^^
PermissionError: [WinError 5] Access is denied: 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0'
2025-10-07 20:56:06,776 22632 ERROR None odoo.modules.loading: Database school not initialized, you can force it with `-i base` 
2025-10-07 20:56:06,776 22632 INFO None odoo.registry: Registry loaded in 0.013s 
2025-10-07 20:56:06,777 22632 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 85, in create
    request.session.authenticate(env, credential)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 1213, in authenticate
    auth_info = env['res.users'].authenticate(credential, wsgienv)
                ~~~^^^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\orm\environments.py", line 107, in __getitem__
    return self.registry[model_name](self, (), ())
           ~~~~~~~~~~~~~^^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\orm\registry.py", line 320, in __getitem__
    return self.models[model_name]
           ~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'res.users'
2025-10-07 20:56:07,632 22632 INFO None odoo.sql_db: ConnectionPool(read/write;used=2/count=2/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=school host=localhost port=5432 application_name=odoo-22632 sslmode=prefer' 
2025-10-07 20:56:07,685 22632 INFO None werkzeug: 127.0.0.1 - - [07/Oct/2025 20:56:07] "POST /web/database/create HTTP/1.1" 200 - 35 0.676 3.135
2025-10-07 20:56:54,266 22632 ERROR school odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 2800, in __call__
    response = request._serve_db()
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 2237, in _serve_db
    rule, args = self.registry['ir.http']._match(self.httprequest.path)
                 ~~~~~~~~~~~~~^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\orm\registry.py", line 320, in __getitem__
    return self.models[model_name]
           ~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'ir.http'
2025-10-07 20:56:54,268 22632 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 20:56:54] "POST /web/database/create HTTP/1.1" 500 - 2 0.022 0.181
2025-10-07 20:56:59,463 22632 ERROR school odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 2800, in __call__
    response = request._serve_db()
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 2237, in _serve_db
    rule, args = self.registry['ir.http']._match(self.httprequest.path)
                 ~~~~~~~~~~~~~^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\orm\registry.py", line 320, in __getitem__
    return self.models[model_name]
           ~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'ir.http'
2025-10-07 20:56:59,465 22632 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 20:56:59] "POST /web/database/create HTTP/1.1" 500 - 2 0.008 0.235
2025-10-07 20:56:59,714 22632 ERROR school odoo.sql_db: bad query: b"\n            SELECT latest_version\n            FROM ir_module_module\n             WHERE name='base'\n        "
ERROR: relation "ir_module_module" does not exist
LINE 3:             FROM ir_module_module
                         ^
 
2025-10-07 20:56:59,714 22632 WARNING school odoo.addons.base.models.ir_cron: Tried to poll an undefined table on database school. 
2025-10-07 20:57:04,774 22632 ERROR school odoo.sql_db: bad query: b"\n            SELECT latest_version\n            FROM ir_module_module\n             WHERE name='base'\n        "
ERROR: relation "ir_module_module" does not exist
LINE 3:             FROM ir_module_module
                         ^
 
2025-10-07 20:57:04,774 22632 WARNING school odoo.addons.base.models.ir_cron: Tried to poll an undefined table on database school. 
2025-10-07 20:57:59,810 22632 ERROR school odoo.sql_db: bad query: b"\n            SELECT latest_version\n            FROM ir_module_module\n             WHERE name='base'\n        "
ERROR: relation "ir_module_module" does not exist
LINE 3:             FROM ir_module_module
                         ^
 
2025-10-07 20:57:59,810 22632 WARNING school odoo.addons.base.models.ir_cron: Tried to poll an undefined table on database school. 
2025-10-07 20:58:05,873 22632 ERROR school odoo.sql_db: bad query: b"\n            SELECT latest_version\n            FROM ir_module_module\n             WHERE name='base'\n        "
ERROR: relation "ir_module_module" does not exist
LINE 3:             FROM ir_module_module
                         ^
 
2025-10-07 20:58:05,873 22632 WARNING school odoo.addons.base.models.ir_cron: Tried to poll an undefined table on database school. 
2025-10-07 20:58:37,362 19636 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:58:37,364 19636 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:58:37,364 19636 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:58:37,364 19636 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'd:\\odoo_19.0_new\\data\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:58:37,364 19636 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:58:37,823 19636 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:58:38,490 19636 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:59:52,503 19636 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 439, in dispatch_rpc
    return dispatch(method, params)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 518, in dispatch
    check_super(passwd)
    ~~~~~~~~~~~^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 63, in check_super
    raise odoo.exceptions.AccessDenied()
odoo.exceptions.AccessDenied: Access Denied
2025-10-07 20:59:53,373 19636 INFO None werkzeug: 127.0.0.1 - - [07/Oct/2025 20:59:53] "POST /web/database/create HTTP/1.1" 200 - 2 0.012 2.583
2025-10-07 21:01:20,881 14232 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 21:01:20,882 14232 INFO ? odoo: Odoo version 19.0 
2025-10-07 21:01:20,882 14232 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 21:01:20,882 14232 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'd:\\odoo_19.0_new\\data\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 21:01:20,882 14232 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 21:01:21,139 14232 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 21:01:21,659 14232 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 21:02:29,531 14232 INFO None odoo.service.db: Create database `school`. 
2025-10-07 21:02:30,110 14232 INFO None odoo.modules.loading: Initializing database school 
2025-10-07 21:02:38,779 14232 INFO None odoo.modules.loading: loading 1 modules... 
2025-10-07 21:02:38,779 14232 INFO None odoo.modules.loading: Loading module base (1/1) 
2025-10-07 21:02:38,814 14232 INFO None odoo.registry: module base: creating or updating database tables 
2025-10-07 21:02:39,407 14232 INFO None odoo.models: Prepare computation of ir.module.module.menus_by_module 
2025-10-07 21:02:39,408 14232 INFO None odoo.models: Prepare computation of ir.module.module.reports_by_module 
2025-10-07 21:02:39,408 14232 INFO None odoo.models: Prepare computation of ir.module.module.views_by_module 
2025-10-07 21:02:39,615 14232 INFO None odoo.models: Prepare computation of res.partner.user_id 
2025-10-07 21:02:39,615 14232 INFO None odoo.models: Prepare computation of res.partner.commercial_partner_id 
2025-10-07 21:02:39,615 14232 INFO None odoo.models: Prepare computation of res.partner.complete_name 
2025-10-07 21:02:39,616 14232 INFO None odoo.models: Prepare computation of res.partner.lang 
2025-10-07 21:02:39,616 14232 INFO None odoo.models: Prepare computation of res.partner.company_registry 
2025-10-07 21:02:39,616 14232 INFO None odoo.models: Prepare computation of res.partner.commercial_company_name 
2025-10-07 21:02:39,616 14232 INFO None odoo.models: Prepare computation of res.partner.properties 
2025-10-07 21:02:39,616 14232 INFO None odoo.models: Prepare computation of res.partner.partner_share 
2025-10-07 21:02:39,687 14232 INFO None odoo.models: Prepare computation of res.currency.decimal_places 
2025-10-07 21:02:39,742 14232 INFO None odoo.models: Prepare computation of res.company.uses_default_logo 
2025-10-07 21:02:39,742 14232 INFO None odoo.models: Prepare computation of res.company.logo_web 
2025-10-07 21:02:39,747 14232 INFO None odoo.models: Computing parent_path for table res_company... 
2025-10-07 21:02:39,814 14232 INFO None odoo.models: Prepare computation of res.users.signature 
2025-10-07 21:02:39,815 14232 INFO None odoo.models: Prepare computation of res.users.share 
2025-10-07 21:02:42,375 14232 INFO None odoo.modules.loading: loading base/data/res_bank.xml 
2025-10-07 21:02:42,408 14232 INFO None odoo.modules.loading: loading base/data/res.lang.csv 
2025-10-07 21:02:42,461 14232 INFO None odoo.modules.loading: loading base/data/res_lang_data.xml 
2025-10-07 21:02:42,564 14232 INFO None odoo.modules.loading: loading base/data/res_partner_data.xml 
2025-10-07 21:02:42,674 14232 INFO None odoo.modules.loading: loading base/data/res_currency_data.xml 
2025-10-07 21:02:43,428 14232 INFO None odoo.modules.loading: loading base/data/res_company_data.xml 
2025-10-07 21:02:43,448 14232 INFO None odoo.modules.loading: loading base/data/res_users_data.xml 
2025-10-07 21:02:45,046 14232 INFO None odoo.modules.loading: loading base/data/report_paperformat_data.xml 
2025-10-07 21:02:45,072 14232 INFO None odoo.modules.loading: loading base/data/res_country_data.xml 
2025-10-07 21:02:45,934 14232 INFO None odoo.modules.loading: loading base/data/ir_demo_data.xml 
2025-10-07 21:02:45,963 14232 INFO None odoo.modules.loading: loading base/security/base_groups.xml 
2025-10-07 21:02:46,176 14232 INFO None odoo.modules.loading: loading base/security/base_security.xml 
2025-10-07 21:02:46,400 14232 INFO None odoo.modules.loading: loading base/wizard/wizard_ir_model_menu_create_views.xml 
2025-10-07 21:02:46,427 14232 INFO None odoo.modules.loading: loading base/views/base_menus.xml 
2025-10-07 21:02:46,610 14232 INFO None odoo.modules.loading: loading base/views/decimal_precision_views.xml 
2025-10-07 21:02:46,675 14232 INFO None odoo.modules.loading: loading base/views/res_config_views.xml 
2025-10-07 21:02:46,695 14232 INFO None odoo.modules.loading: loading base/data/res.country.state.csv 
2025-10-07 21:02:47,175 14232 INFO None odoo.modules.loading: loading base/views/ir_actions_views.xml 
2025-10-07 21:02:47,429 14232 INFO None odoo.modules.loading: loading base/views/ir_asset_views.xml 
2025-10-07 21:02:47,468 14232 INFO None odoo.modules.loading: loading base/views/ir_config_parameter_views.xml 
2025-10-07 21:02:47,506 14232 INFO None odoo.modules.loading: loading base/views/ir_cron_views.xml 
2025-10-07 21:02:47,575 14232 INFO None odoo.modules.loading: loading base/views/ir_cron_trigger_views.xml 
2025-10-07 21:02:47,605 14232 INFO None odoo.modules.loading: loading base/views/ir_filters_views.xml 
2025-10-07 21:02:47,655 14232 INFO None odoo.modules.loading: loading base/views/ir_mail_server_views.xml 
2025-10-07 21:02:47,703 14232 INFO None odoo.modules.loading: loading base/views/ir_model_views.xml 
2025-10-07 21:02:47,924 14232 INFO None odoo.modules.loading: loading base/views/ir_attachment_views.xml 
2025-10-07 21:02:47,967 14232 INFO None odoo.modules.loading: loading base/views/ir_rule_views.xml 
2025-10-07 21:02:48,007 14232 INFO None odoo.modules.loading: loading base/views/ir_sequence_views.xml 
2025-10-07 21:02:48,050 14232 INFO None odoo.modules.loading: loading base/views/ir_ui_menu_views.xml 
2025-10-07 21:02:48,091 14232 INFO None odoo.modules.loading: loading base/views/ir_ui_view_views.xml 
2025-10-07 21:02:48,174 14232 INFO None odoo.modules.loading: loading base/views/ir_default_views.xml 
2025-10-07 21:02:48,213 14232 INFO None odoo.modules.loading: loading base/data/ir_config_parameter_data.xml 
2025-10-07 21:02:48,227 14232 INFO None odoo.modules.loading: loading base/data/ir_cron_data.xml 
2025-10-07 21:02:48,257 14232 INFO None odoo.modules.loading: loading base/report/ir_model_report.xml 
2025-10-07 21:02:48,273 14232 INFO None odoo.modules.loading: loading base/report/ir_model_templates.xml 
2025-10-07 21:02:48,297 14232 INFO None odoo.modules.loading: loading base/views/ir_logging_views.xml 
2025-10-07 21:02:48,338 14232 INFO None odoo.modules.loading: loading base/views/ir_qweb_widget_templates.xml 
2025-10-07 21:02:48,369 14232 INFO None odoo.modules.loading: loading base/views/ir_module_views.xml 
2025-10-07 21:02:48,459 14232 INFO None odoo.modules.loading: loading base/data/ir_module_category_data.xml 
2025-10-07 21:02:48,525 14232 INFO None odoo.modules.loading: loading base/data/ir_module_module.xml 
2025-10-07 21:02:48,625 14232 INFO None odoo.modules.loading: loading base/report/ir_module_reports.xml 
2025-10-07 21:02:48,641 14232 INFO None odoo.modules.loading: loading base/report/ir_module_report_templates.xml 
2025-10-07 21:02:48,661 14232 INFO None odoo.modules.loading: loading base/wizard/base_module_update_views.xml 
2025-10-07 21:02:48,691 14232 INFO None odoo.modules.loading: loading base/wizard/base_language_install_views.xml 
2025-10-07 21:02:48,723 14232 INFO None odoo.modules.loading: loading base/wizard/base_import_language_views.xml 
2025-10-07 21:02:48,756 14232 INFO None odoo.modules.loading: loading base/wizard/base_module_upgrade_views.xml 
2025-10-07 21:02:48,801 14232 INFO None odoo.modules.loading: loading base/wizard/base_module_uninstall_views.xml 
2025-10-07 21:02:48,829 14232 INFO None odoo.modules.loading: loading base/wizard/base_export_language_views.xml 
2025-10-07 21:02:48,870 14232 INFO None odoo.modules.loading: loading base/wizard/base_partner_merge_views.xml 
2025-10-07 21:02:48,912 14232 INFO None odoo.modules.loading: loading base/data/ir_demo_failure_data.xml 
2025-10-07 21:02:48,962 14232 INFO None odoo.modules.loading: loading base/views/ir_profile_views.xml 
2025-10-07 21:02:49,015 14232 INFO None odoo.modules.loading: loading base/views/res_company_views.xml 
2025-10-07 21:02:49,061 14232 INFO None odoo.modules.loading: loading base/views/res_lang_views.xml 
2025-10-07 21:02:49,108 14232 INFO None odoo.modules.loading: loading base/views/res_partner_views.xml 
2025-10-07 21:02:49,253 14232 INFO None odoo.modules.loading: loading base/views/res_bank_views.xml 
2025-10-07 21:02:49,318 14232 INFO None odoo.modules.loading: loading base/views/res_country_views.xml 
2025-10-07 21:02:49,401 14232 INFO None odoo.modules.loading: loading base/views/res_currency_views.xml 
2025-10-07 21:02:49,475 14232 INFO None odoo.modules.loading: loading base/views/res_groups_views.xml 
2025-10-07 21:02:49,555 14232 INFO None odoo.modules.loading: loading base/views/res_users_views.xml 
2025-10-07 21:02:49,704 14232 INFO None odoo.modules.loading: loading base/views/res_users_apikeys_views.xml 
2025-10-07 21:02:49,723 14232 INFO None odoo.modules.loading: loading base/views/res_device_views.xml 
2025-10-07 21:02:49,767 14232 INFO None odoo.modules.loading: loading base/views/res_users_identitycheck_views.xml 
2025-10-07 21:02:49,790 14232 INFO None odoo.modules.loading: loading base/views/res_config_settings_views.xml 
2025-10-07 21:02:49,811 14232 INFO None odoo.modules.loading: loading base/views/report_paperformat_views.xml 
2025-10-07 21:02:49,860 14232 INFO None odoo.modules.loading: loading base/security/ir.model.access.csv 
2025-10-07 21:02:50,166 14232 INFO None odoo.modules.loading: Module base loaded in 11.39s, 8370 queries (+8370 other) 
2025-10-07 21:02:50,166 14232 INFO None odoo.modules.loading: 1 modules loaded in 11.39s, 8370 queries (+8370 extra) 
2025-10-07 21:02:50,192 14232 INFO None odoo.modules.loading: updating modules list 
2025-10-07 21:02:50,196 14232 INFO None odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-10-07 21:02:51,818 14232 INFO None odoo.modules.loading: loading 14 modules... 
2025-10-07 21:02:51,818 14232 INFO None odoo.modules.loading: Loading module rpc (2/14) 
2025-10-07 21:02:51,844 14232 INFO None odoo.modules.loading: Module rpc loaded in 0.03s, 9 queries (+9 other) 
2025-10-07 21:02:51,845 14232 INFO None odoo.modules.loading: Loading module web (3/14) 
2025-10-07 21:02:51,898 14232 INFO None odoo.registry: module web: creating or updating database tables 
2025-10-07 21:02:52,732 14232 INFO None odoo.modules.loading: loading web/security/ir.model.access.csv 
2025-10-07 21:02:52,760 14232 INFO None odoo.modules.loading: loading web/security/web_security.xml 
2025-10-07 21:02:52,793 14232 INFO None odoo.modules.loading: loading web/views/webclient_templates.xml 
2025-10-07 21:02:53,100 14232 INFO None odoo.modules.loading: loading web/views/report_templates.xml 
2025-10-07 21:02:53,323 14232 INFO None odoo.modules.loading: loading web/views/base_document_layout_views.xml 
2025-10-07 21:02:53,359 14232 INFO None odoo.modules.loading: loading web/views/partner_view.xml 
2025-10-07 21:02:53,382 14232 INFO None odoo.modules.loading: loading web/views/speedscope_template.xml 
2025-10-07 21:02:53,437 14232 INFO None odoo.modules.loading: loading web/views/memory_template.xml 
2025-10-07 21:02:53,484 14232 INFO None odoo.modules.loading: loading web/views/speedscope_config_wizard.xml 
2025-10-07 21:02:53,526 14232 INFO None odoo.modules.loading: loading web/views/neutralize_views.xml 
2025-10-07 21:02:53,549 14232 INFO None odoo.modules.loading: loading web/views/ir_ui_view_views.xml 
2025-10-07 21:02:53,575 14232 INFO None odoo.modules.loading: loading web/data/ir_attachment.xml 
2025-10-07 21:02:53,591 14232 INFO None odoo.modules.loading: loading web/data/report_layout.xml 
2025-10-07 21:02:53,670 14232 INFO None odoo.modules.loading: Module web loaded in 1.83s, 1225 queries (+1225 other) 
2025-10-07 21:02:53,670 14232 INFO None odoo.modules.loading: Loading module api_doc (4/14) 
2025-10-07 21:02:53,693 14232 INFO None odoo.registry: module api_doc: creating or updating database tables 
2025-10-07 21:02:53,718 14232 INFO None odoo.modules.loading: loading api_doc/security/res_groups.xml 
2025-10-07 21:02:53,770 14232 INFO None odoo.modules.loading: loading api_doc/views/docclient.xml 
2025-10-07 21:02:53,804 14232 INFO None odoo.modules.loading: Module api_doc loaded in 0.13s, 63 queries (+63 other) 
2025-10-07 21:02:53,805 14232 INFO None odoo.modules.loading: Loading module auth_totp (5/14) 
2025-10-07 21:02:53,867 14232 INFO None odoo.registry: module auth_totp: creating or updating database tables 
2025-10-07 21:02:54,018 14232 INFO None odoo.modules.loading: loading auth_totp/security/security.xml 
2025-10-07 21:02:54,075 14232 INFO None odoo.modules.loading: loading auth_totp/security/ir.model.access.csv 
2025-10-07 21:02:54,098 14232 INFO None odoo.modules.loading: loading auth_totp/data/ir_action_data.xml 
2025-10-07 21:02:54,123 14232 INFO None odoo.modules.loading: loading auth_totp/views/res_users_views.xml 
2025-10-07 21:02:54,180 14232 INFO None odoo.modules.loading: loading auth_totp/views/templates.xml 
2025-10-07 21:02:54,204 14232 INFO None odoo.modules.loading: loading auth_totp/wizard/auth_totp_wizard_views.xml 
2025-10-07 21:02:54,242 14232 INFO None odoo.modules.loading: Module auth_totp loaded in 0.44s, 214 queries (+214 other) 
2025-10-07 21:02:54,242 14232 INFO None odoo.modules.loading: Loading module base_import (6/14) 
2025-10-07 21:02:54,320 14232 INFO None odoo.registry: module base_import: creating or updating database tables 
2025-10-07 21:02:55,067 14232 INFO None odoo.modules.loading: loading base_import/security/ir.model.access.csv 
2025-10-07 21:02:55,102 14232 INFO None odoo.modules.loading: Module base_import loaded in 0.86s, 953 queries (+953 other) 
2025-10-07 21:02:55,102 14232 INFO None odoo.modules.loading: Loading module base_import_module (7/14) 
2025-10-07 21:02:55,141 14232 INFO None odoo.registry: module base_import_module: creating or updating database tables 
2025-10-07 21:02:55,245 14232 INFO None odoo.modules.loading: loading base_import_module/security/ir.model.access.csv 
2025-10-07 21:02:55,266 14232 INFO None odoo.modules.loading: loading base_import_module/views/base_import_module_view.xml 
2025-10-07 21:02:55,319 14232 INFO None odoo.modules.loading: loading base_import_module/views/ir_module_views.xml 
2025-10-07 21:02:55,433 14232 INFO None odoo.modules.loading: Module base_import_module loaded in 0.33s, 172 queries (+172 other) 
2025-10-07 21:02:55,433 14232 INFO None odoo.modules.loading: Loading module base_setup (8/14) 
2025-10-07 21:02:55,471 14232 INFO None odoo.registry: module base_setup: creating or updating database tables 
2025-10-07 21:02:55,554 14232 INFO None odoo.modules.loading: loading base_setup/data/base_setup_data.xml 
2025-10-07 21:02:55,570 14232 INFO None odoo.modules.loading: loading base_setup/views/res_config_settings_views.xml 
2025-10-07 21:02:55,641 14232 INFO None odoo.modules.loading: loading base_setup/views/res_partner_views.xml 
2025-10-07 21:02:55,685 14232 INFO None odoo.modules.loading: Module base_setup loaded in 0.25s, 152 queries (+152 other) 
2025-10-07 21:02:55,685 14232 INFO None odoo.modules.loading: Loading module bus (9/14) 
2025-10-07 21:02:55,759 14232 INFO None odoo.registry: module bus: creating or updating database tables 
2025-10-07 21:02:55,891 14232 INFO None odoo.modules.loading: loading bus/security/ir.model.access.csv 
2025-10-07 21:02:55,926 14232 INFO None odoo.modules.loading: Module bus loaded in 0.24s, 152 queries (+152 other) 
2025-10-07 21:02:55,927 14232 INFO None odoo.modules.loading: Loading module web_tour (10/14) 
2025-10-07 21:02:55,996 14232 INFO None odoo.registry: module web_tour: creating or updating database tables 
2025-10-07 21:02:56,005 14232 INFO None odoo.models: Prepare computation of res.users.tour_enabled 
2025-10-07 21:02:56,156 14232 INFO None odoo.modules.loading: loading web_tour/security/ir.model.access.csv 
2025-10-07 21:02:56,182 14232 INFO None odoo.modules.loading: loading web_tour/views/tour_views.xml 
2025-10-07 21:02:56,259 14232 INFO None odoo.modules.loading: Module web_tour loaded in 0.33s, 176 queries (+176 other) 
2025-10-07 21:02:56,259 14232 INFO None odoo.modules.loading: Loading module auth_passkey (11/14) 
2025-10-07 21:02:56,474 14232 INFO None odoo.registry: module auth_passkey: creating or updating database tables 
2025-10-07 21:02:56,575 14232 INFO None odoo.modules.loading: loading auth_passkey/views/auth_passkey_key_views.xml 
2025-10-07 21:02:56,620 14232 INFO None odoo.modules.loading: loading auth_passkey/views/auth_passkey_login_templates.xml 
2025-10-07 21:02:56,655 14232 INFO None odoo.modules.loading: loading auth_passkey/views/res_users_identitycheck_views.xml 
2025-10-07 21:02:56,685 14232 INFO None odoo.modules.loading: loading auth_passkey/views/res_users_views.xml 
2025-10-07 21:02:56,735 14232 INFO None odoo.modules.loading: loading auth_passkey/security/ir.model.access.csv 
2025-10-07 21:02:56,761 14232 INFO None odoo.modules.loading: loading auth_passkey/security/security.xml 
2025-10-07 21:02:56,824 14232 INFO None odoo.modules.loading: Module auth_passkey loaded in 0.56s, 218 queries (+218 other) 
2025-10-07 21:02:56,824 14232 INFO None odoo.modules.loading: Loading module html_editor (12/14) 
2025-10-07 21:02:59,744 14232 INFO None odoo.registry: module html_editor: creating or updating database tables 
2025-10-07 21:03:00,649 14232 INFO None odoo.modules.loading: loading html_editor/security/ir.model.access.csv 
2025-10-07 21:03:00,678 14232 INFO None odoo.modules.loading: Module html_editor loaded in 3.85s, 1031 queries (+1031 other) 
2025-10-07 21:03:00,678 14232 INFO None odoo.modules.loading: Loading module iap (13/14) 
2025-10-07 21:03:00,683 14232 INFO None odoo.registry: module iap: creating or updating database tables 
2025-10-07 21:03:00,796 14232 INFO None odoo.modules.loading: loading iap/data/services.xml 
2025-10-07 21:03:00,813 14232 INFO None odoo.modules.loading: loading iap/security/ir.model.access.csv 
2025-10-07 21:03:00,834 14232 INFO None odoo.modules.loading: loading iap/security/ir_rule.xml 
2025-10-07 21:03:00,854 14232 INFO None odoo.modules.loading: loading iap/views/iap_views.xml 
2025-10-07 21:03:00,904 14232 INFO None odoo.modules.loading: loading iap/views/res_config_settings.xml 
2025-10-07 21:03:00,949 14232 INFO None odoo.modules.loading: Module iap loaded in 0.27s, 182 queries (+182 other) 
2025-10-07 21:03:00,949 14232 INFO None odoo.modules.loading: Loading module web_unsplash (14/14) 
2025-10-07 21:03:01,064 14232 INFO None odoo.registry: module web_unsplash: creating or updating database tables 
2025-10-07 21:03:01,139 14232 INFO None odoo.modules.loading: loading web_unsplash/views/res_config_settings_view.xml 
2025-10-07 21:03:01,196 14232 INFO None odoo.modules.loading: Module web_unsplash loaded in 0.25s, 102 queries (+102 other) 
2025-10-07 21:03:01,196 14232 INFO None odoo.modules.loading: 14 modules loaded in 9.38s, 4649 queries (+4649 extra) 
2025-10-07 21:03:01,564 14232 INFO None odoo.modules.loading: Modules loaded. 
2025-10-07 21:03:01,570 14232 INFO None odoo.registry: Registry changed, signaling through the database 
2025-10-07 21:03:01,571 14232 INFO None odoo.registry: Registry loaded in 31.507s 
2025-10-07 21:03:03,260 14232 INFO None odoo.addons.base.models.res_users: Login successful for login:<EMAIL> from 127.0.0.1 
2025-10-07 21:03:03,281 14232 INFO None werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:03] "POST /web/database/create HTTP/1.1" 303 - 19313 11.934 25.230
2025-10-07 21:03:03,306 14232 INFO school odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-10-07 21:03:03,325 14232 INFO school odoo.addons.base.models.res_device: User 2 inserts device log (zXvIK2BOIQhhITV_LWPsl6wlhVpzKvOHDvBX2Km8pv) 
2025-10-07 21:03:03,936 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:03] "GET /odoo HTTP/1.1" 200 - 55 0.044 0.606
2025-10-07 21:03:04,450 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:04] "GET /web/webclient/load_menus HTTP/1.1" 200 - 12 0.042 0.149
2025-10-07 21:03:16,060 14232 INFO school odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/bdd292a/web.assets_web.min.css (id:14) 
2025-10-07 21:03:16,174 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:16] "GET /web/assets/bdd292a/web.assets_web.min.css HTTP/1.1" 200 - 15 0.084 11.832
2025-10-07 21:03:23,963 14232 INFO school odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/29d3171/web.assets_web_print.min.css (id:15) 
2025-10-07 21:03:23,987 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:23] "GET /web/assets/29d3171/web.assets_web_print.min.css HTTP/1.1" 200 - 8 0.042 7.455
2025-10-07 21:03:24,158 14232 INFO school odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-10-07 21:03:24,170 14232 INFO school odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) starting 
2025-10-07 21:03:24,429 14232 INFO school odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) fully done (#loop 1; done 0; remaining 0; duration 0.26s) 
2025-10-07 21:03:24,592 14232 INFO school odoo.addons.base.models.ir_attachment: filestore gc 12 checked, 0 removed 
2025-10-07 21:03:24,630 14232 INFO school odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-10-07 21:03:24,637 14232 INFO school odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-10-07 21:03:24,645 14232 INFO school odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-10-07 21:03:24,688 14232 INFO school odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-10-07 21:03:24,702 14232 INFO school odoo.addons.api_doc.models.ir_attachment: GC'd 0 /doc cached index 
2025-10-07 21:03:24,715 14232 INFO school odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-10-07 21:03:24,757 14232 INFO school odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) fully done (#loop 1; done 44; remaining 0; duration 0.60s) 
2025-10-07 21:03:42,239 14232 INFO school odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/82ce1db/web.assets_web.min.js (id:16) 
2025-10-07 21:03:43,255 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:43] "GET /web/assets/82ce1db/web.assets_web.min.js HTTP/1.1" 200 - 11 0.010 39.297
2025-10-07 21:03:43,890 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:43] "GET /web/webclient/translations?hash=&lang=en_US HTTP/1.1" 200 - 2 0.012 0.018
2025-10-07 21:03:43,962 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:43] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 6 0.014 0.006
2025-10-07 21:03:44,135 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.021
2025-10-07 21:03:44,240 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 200 - 0 0.000 0.014
2025-10-07 21:03:44,257 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "POST /web/action/load HTTP/1.1" 200 - 10 0.018 0.013
2025-10-07 21:03:44,353 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "GET /web/image/res.partner/3/avatar_128?unique=1759852981000 HTTP/1.1" 200 - 11 0.047 0.018
2025-10-07 21:03:44,361 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 15 0.040 0.036
2025-10-07 21:03:44,422 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "POST /web/dataset/call_kw/ir.module.module/get_views#ir.module.module.get_views HTTP/1.1" 200 - 45 0.066 0.070
2025-10-07 21:03:44,447 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range#ir.module.module.search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.004
2025-10-07 21:03:44,653 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range#ir.module.module.search_panel_select_range HTTP/1.1" 200 - 45 0.030 0.028
2025-10-07 21:03:44,753 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "POST /web/dataset/call_kw/ir.module.module/check_module_update#ir.module.module.check_module_update HTTP/1.1" 200 - 2 0.004 0.005
2025-10-07 21:03:44,838 14232 INFO school odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/942969f/bus.websocket_worker_assets.min.js (id:17) 
2025-10-07 21:03:44,883 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.072
2025-10-07 21:03:44,885 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:44] "GET /bus/websocket_worker_bundle?v=saas-18.5-1 HTTP/1.1" 200 - 8 0.013 0.126
2025-10-07 21:03:45,268 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:45] "GET /websocket?version=saas-18.5-1 HTTP/1.1" 101 - 1 0.002 0.022
2025-10-07 21:03:45,274 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:45] "GET /odoo HTTP/1.1" 200 - 9 0.011 0.021
2025-10-07 21:03:45,286 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:45] "GET /odoo/offline HTTP/1.1" 200 - 6 0.013 0.029
2025-10-07 21:03:45,601 14232 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-10-07 21:03:45,679 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:45] "POST /web/dataset/call_kw/ir.module.module/web_search_read#ir.module.module.web_search_read HTTP/1.1" 200 - 3 0.006 0.929
2025-10-07 21:03:46,119 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "POST /web/dataset/call_kw/ir.module.module/check_module_update#ir.module.module.check_module_update HTTP/1.1" 200 - 2 0.002 0.006
2025-10-07 21:03:46,388 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /point_of_sale/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.009
2025-10-07 21:03:46,496 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /base/static/img/icons/web_studio.png HTTP/1.1" 200 - 0 0.000 0.019
2025-10-07 21:03:46,497 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /project/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.021
2025-10-07 21:03:46,499 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /base/static/img/icons/knowledge.png HTTP/1.1" 200 - 0 0.000 0.018
2025-10-07 21:03:46,502 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /purchase/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.023
2025-10-07 21:03:46,510 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /mrp/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.025
2025-10-07 21:03:46,714 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /mass_mailing/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.004
2025-10-07 21:03:46,810 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /website_sale/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-10-07 21:03:46,820 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /base/static/img/icons/account_accountant.png HTTP/1.1" 200 - 0 0.000 0.004
2025-10-07 21:03:46,823 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /stock/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-10-07 21:03:46,829 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /hr_expense/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.010
2025-10-07 21:03:46,831 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:46] "GET /base/static/img/icons/timesheet_grid.png HTTP/1.1" 200 - 0 0.000 0.006
2025-10-07 21:03:47,027 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /sale_management/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-10-07 21:03:47,118 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /pos_restaurant/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-10-07 21:03:47,138 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /account/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:47,140 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /crm/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-10-07 21:03:47,146 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /base/static/img/icons/mrp_workorder.png HTTP/1.1" 200 - 0 0.000 0.010
2025-10-07 21:03:47,150 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /website/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:47,348 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /hr_holidays/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:47,446 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /hr_recruitment/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:47,470 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /base/static/img/icons/industry_fsm.png HTTP/1.1" 200 - 0 0.000 0.012
2025-10-07 21:03:47,490 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /hr/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.018
2025-10-07 21:03:47,491 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /maintenance/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.014
2025-10-07 21:03:47,494 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /data_recycle/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.019
2025-10-07 21:03:47,677 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /marketing_card/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-10-07 21:03:47,773 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /base/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-10-07 21:03:47,794 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /base/static/img/icons/sign.png HTTP/1.1" 200 - 0 0.000 0.001
2025-10-07 21:03:47,812 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /base/static/img/icons/helpdesk.png HTTP/1.1" 200 - 0 0.000 0.004
2025-10-07 21:03:47,814 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /base/static/img/icons/sale_subscription.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:47,816 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /base/static/img/icons/quality_control.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:47,991 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:47] "GET /website_slides/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-10-07 21:03:48,093 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /base/static/img/icons/planning.png HTTP/1.1" 200 - 0 0.000 0.002
2025-10-07 21:03:48,100 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /website_event/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-10-07 21:03:48,129 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /mail/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.004
2025-10-07 21:03:48,131 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /contacts/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:48,133 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /base/static/img/icons/mrp_plm.png HTTP/1.1" 200 - 0 0.000 0.006
2025-10-07 21:03:48,308 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /calendar/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:48,421 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /base/static/img/icons/social.png HTTP/1.1" 200 - 0 0.000 0.010
2025-10-07 21:03:48,426 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /base/static/img/icons/hr_appraisal.png HTTP/1.1" 200 - 0 0.000 0.013
2025-10-07 21:03:48,456 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /fleet/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.010
2025-10-07 21:03:48,457 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /base/static/img/icons/marketing_automation.png HTTP/1.1" 200 - 0 0.000 0.009
2025-10-07 21:03:48,460 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /im_livechat/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.010
2025-10-07 21:03:48,630 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /base/static/img/icons/appointment.png HTTP/1.1" 200 - 0 0.000 0.006
2025-10-07 21:03:48,747 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /survey/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.007
2025-10-07 21:03:48,759 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /base/static/img/icons/web_mobile.png HTTP/1.1" 200 - 0 0.000 0.006
2025-10-07 21:03:48,771 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /repair/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-10-07 21:03:48,774 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /hr_attendance/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:48,776 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /mass_mailing_sms/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-10-07 21:03:48,958 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:48] "GET /base/static/img/icons/stock_barcode.png HTTP/1.1" 200 - 0 0.000 0.003
2025-10-07 21:03:49,075 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:49] "GET /project_todo/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.004
2025-10-07 21:03:49,077 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:49] "GET /hr_skills/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-10-07 21:03:49,088 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:49] "GET /base/static/img/icons/voip.png HTTP/1.1" 200 - 0 0.000 0.009
2025-10-07 21:03:49,098 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:49] "GET /lunch/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.011
2025-10-07 21:03:49,099 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:49] "GET /website_hr_recruitment/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.007
2025-10-07 21:03:49,274 14232 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 21:03:49] "GET /base/static/img/icons/sale_amazon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-10-07 21:04:24,855 14232 INFO school odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-10-07 21:04:24,989 14232 INFO school odoo.addons.base.models.ir_attachment: filestore gc 2 checked, 0 removed 
2025-10-07 21:04:25,035 14232 INFO school odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-10-07 21:04:25,184 14232 INFO school odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-10-07 21:04:25,195 14232 INFO school odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-10-07 21:04:25,212 14232 INFO school odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-10-07 21:04:25,229 14232 INFO school odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-10-07 21:04:25,235 14232 INFO school odoo.addons.api_doc.models.ir_attachment: GC'd 0 /doc cached index 
2025-10-07 21:04:25,239 14232 INFO school odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) fully done (#loop 1; done 44; remaining 0; duration 0.38s) 
2025-10-07 21:05:21,392 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:21] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range#ir.module.module.search_panel_select_range HTTP/1.1" 200 - 41 0.037 0.046
2025-10-07 21:05:21,612 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:21] "POST /web/dataset/call_kw/ir.module.module/web_search_read#ir.module.module.web_search_read HTTP/1.1" 200 - 3 0.010 0.026
2025-10-07 21:05:21,717 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:21] "POST /web/dataset/call_kw/ir.module.module/check_module_update#ir.module.module.check_module_update HTTP/1.1" 200 - 2 0.005 0.009
2025-10-07 21:05:31,904 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:31] "POST /web/action/load HTTP/1.1" 200 - 6 0.006 0.011
2025-10-07 21:05:57,635 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:57] "POST /web/action/load HTTP/1.1" 200 - 10 0.005 0.009
2025-10-07 21:05:57,890 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:57] "POST /web/dataset/call_kw/ir.module.module/get_views#ir.module.module.get_views HTTP/1.1" 200 - 2 0.003 0.016
2025-10-07 21:05:57,973 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:57] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range#ir.module.module.search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.004
2025-10-07 21:05:58,250 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:58] "POST /web/dataset/call_kw/ir.module.module/check_module_update#ir.module.module.check_module_update HTTP/1.1" 200 - 2 0.003 0.018
2025-10-07 21:05:58,261 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:58] "POST /web/dataset/call_kw/ir.module.module/check_module_update#ir.module.module.check_module_update HTTP/1.1" 200 - 2 0.009 0.020
2025-10-07 21:05:58,300 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:58] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range#ir.module.module.search_panel_select_range HTTP/1.1" 200 - 41 0.035 0.049
2025-10-07 21:05:58,321 14232 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:05:58] "POST /web/dataset/call_kw/ir.module.module/web_search_read#ir.module.module.web_search_read HTTP/1.1" 200 - 3 0.010 0.092
2025-10-07 21:08:23,821 14232 WARNING ? odoo.service.server: Thread <Thread(odoo.service.http.request.19240, started 19240)> virtual real time limit (145/120s) reached. 
2025-10-07 21:08:23,821 14232 INFO ? odoo.service.server: Dumping stacktrace of limit exceeding threads before reloading 
2025-10-07 21:08:23,835 14232 INFO ? odoo.tools.misc: 
# Thread: <Thread(odoo.service.http.request.19240, started 19240)> (db:n/a) (uid:n/a) (url:n/a) (qc:n/a qt:n/a pt:n/a)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1014, in _bootstrap
  self._bootstrap_inner()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1043, in _bootstrap_inner
  self.run()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 994, in run
  self._target(*self._args, **self._kwargs)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socketserver.py", line 697, in process_request_thread
  self.finish_request(request, client_address)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socketserver.py", line 362, in finish_request
  self.RequestHandlerClass(request, client_address, self)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socketserver.py", line 766, in __init__
  self.handle()
File: "D:\odoo_19.0_new\venv\Lib\site-packages\werkzeug\serving.py", line 398, in handle
  super().handle()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\server.py", line 436, in handle
  self.handle_one_request()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\server.py", line 404, in handle_one_request
  self.raw_requestline = self.rfile.readline(65537)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
  return self._sock.recv_into(b) 
