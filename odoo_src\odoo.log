2025-10-07 21:20:34,204 4824 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\odoo\\addons', skipped 
2025-10-07 21:20:34,205 4824 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\addons', skipped 
2025-10-07 21:20:34,205 4824 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\custom_addons', skipped 
2025-10-07 21:20:34,205 4824 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 21:20:34,207 4824 INFO ? odoo: Odoo version 19.0 
2025-10-07 21:20:34,207 4824 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 21:20:34,208 4824 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'd:\\odoo_19.0_new\\odoo_src\\data\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons']) 
2025-10-07 21:20:34,208 4824 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 21:20:34,559 4824 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 21:20:35,653 4824 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-10-07 21:20:35,661 4824 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 21:21:29,010 23424 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\odoo\\addons', skipped 
2025-10-07 21:21:29,010 23424 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\addons', skipped 
2025-10-07 21:21:29,011 23424 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\custom_addons', skipped 
2025-10-07 21:21:29,011 23424 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 21:21:29,012 23424 INFO ? odoo: Odoo version 19.0 
2025-10-07 21:21:29,012 23424 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 21:21:29,012 23424 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'd:\\odoo_19.0_new\\odoo_src\\data\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons']) 
2025-10-07 21:21:29,013 23424 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 21:21:29,553 23424 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 21:21:30,366 23424 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-10-07 21:21:30,376 23424 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 21:21:33,955 23424 INFO ? odoo.modules.loading: loading 1 modules... 
2025-10-07 21:21:33,961 23424 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-10-07 21:21:33,977 23424 INFO ? odoo.modules.loading: loading 14 modules... 
2025-10-07 21:21:34,665 23424 INFO ? odoo.modules.loading: 14 modules loaded in 0.69s, 0 queries (+0 extra) 
2025-10-07 21:21:34,713 23424 INFO ? odoo.modules.loading: Modules loaded. 
2025-10-07 21:21:34,735 23424 INFO ? odoo.registry: Registry loaded in 0.864s 
2025-10-07 21:21:34,736 23424 INFO school odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-10-07 21:21:34,788 23424 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:21:34] "POST /web/webclient/version_info HTTP/1.1" 200 - 20 0.057 0.969
2025-10-07 21:22:12,413 16720 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\odoo\\addons', skipped 
2025-10-07 21:22:12,413 16720 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\odoo_src\\addons', skipped 
2025-10-07 21:22:12,413 16720 WARNING ? odoo.tools.config: option addons_path, no such directory 'd:\\odoo_19.0_new\\odoo_src\\custom_addons', skipped 
2025-10-07 21:22:12,414 16720 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 21:22:12,414 16720 INFO ? odoo: Odoo version 19.0 
2025-10-07 21:22:12,415 16720 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 21:22:12,415 16720 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'd:\\odoo_19.0_new\\odoo_src\\data\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons']) 
2025-10-07 21:22:12,415 16720 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 21:22:12,803 16720 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 21:22:13,543 16720 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-10-07 21:22:13,552 16720 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 21:22:25,205 16720 INFO ? odoo.modules.loading: loading 1 modules... 
2025-10-07 21:22:25,213 16720 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-10-07 21:22:25,226 16720 INFO ? odoo.modules.loading: loading 14 modules... 
2025-10-07 21:22:25,691 16720 INFO ? odoo.modules.loading: 14 modules loaded in 0.46s, 0 queries (+0 extra) 
2025-10-07 21:22:25,736 16720 INFO ? odoo.modules.loading: Modules loaded. 
2025-10-07 21:22:25,756 16720 INFO ? odoo.registry: Registry loaded in 0.663s 
2025-10-07 21:22:25,758 16720 INFO school odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-10-07 21:22:25,814 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:22:25] "GET /websocket?version=saas-18.5-1 HTTP/1.1" 101 - 23 0.071 0.784
2025-10-07 21:22:26,174 16720 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-10-07 21:22:26,269 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:22:26] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.017 0.109
2025-10-07 21:22:52,116 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:22:52] "GET / HTTP/1.1" 303 - 1 0.003 0.006
2025-10-07 21:22:52,415 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:22:52] "GET /odoo HTTP/1.1" 303 - 1 0.001 0.003
2025-10-07 21:22:53,182 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:22:53] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 42 0.048 0.391
2025-10-07 21:22:53,539 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:22:53] "GET /web/binary/company_logo HTTP/1.1" 200 - 2 0.015 0.006
2025-10-07 21:22:59,833 16720 INFO school odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/fcd3ebf/web.assets_frontend.min.css (id:18) 
2025-10-07 21:22:59,867 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:22:59] "GET /web/assets/fcd3ebf/web.assets_frontend.min.css HTTP/1.1" 200 - 12 0.041 6.630
2025-10-07 21:22:59,983 16720 INFO school odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/7680765/web.assets_frontend_minimal.min.js (id:19) 
2025-10-07 21:23:00,037 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:00] "GET /web/assets/7680765/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 9 0.021 6.137
2025-10-07 21:23:02,963 16720 INFO school odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/e2c44cb/web.assets_frontend_lazy.min.js (id:20) 
2025-10-07 21:23:03,409 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:03] "GET /web/assets/e2c44cb/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 9 0.013 3.028
2025-10-07 21:23:03,539 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:03] "GET /web/webclient/translations?hash=&lang= HTTP/1.1" 200 - 3 0.002 0.006
2025-10-07 21:23:10,341 16720 INFO school odoo.addons.base.models.res_users: Login failed for login:admin from 127.0.0.1 
2025-10-07 21:23:10,345 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:10] "POST /web/login HTTP/1.1" 200 - 7 0.017 0.075
2025-10-07 21:23:10,366 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:10] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.003 0.004
2025-10-07 21:23:10,538 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:10] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.003 0.005
2025-10-07 21:23:16,067 16720 INFO school odoo.addons.base.models.res_users: Login failed for login:admin from 127.0.0.1 
2025-10-07 21:23:16,071 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:16] "POST /web/login HTTP/1.1" 200 - 5 0.011 0.103
2025-10-07 21:23:16,087 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:16] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.002 0.004
2025-10-07 21:23:16,220 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:16] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.004 0.004
2025-10-07 21:23:25,413 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:25] "GET /web/database/manager HTTP/1.1" 200 - 4 0.008 1.006
2025-10-07 21:23:29,956 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:29] "GET /odoo?db=school HTTP/1.1" 303 - 1 0.002 0.004
2025-10-07 21:23:30,042 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:30] "GET /web/login?redirect=/odoo?db%3Dschool HTTP/1.1" 200 - 4 0.007 0.068
2025-10-07 21:23:30,367 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:30] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.002 0.004
2025-10-07 21:23:30,621 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:30] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.001 0.003
2025-10-07 21:23:32,849 16720 INFO school odoo.addons.base.models.res_users: Login failed for login:admin from 127.0.0.1 
2025-10-07 21:23:32,857 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:32] "POST /web/login HTTP/1.1" 200 - 5 0.012 0.099
2025-10-07 21:23:33,152 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:33] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.004 0.005
2025-10-07 21:23:33,519 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:23:33] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.004 0.006
2025-10-07 21:24:08,048 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:24:08] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.002 0.005
2025-10-07 21:24:09,711 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:24:09] "GET /odoo/apps HTTP/1.1" 303 - 1 0.002 0.004
2025-10-07 21:24:09,941 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:24:09] "GET /web/login?redirect=/odoo/apps? HTTP/1.1" 200 - 4 0.008 0.211
2025-10-07 21:24:09,966 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:24:09] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.003 0.004
2025-10-07 21:24:10,448 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:24:10] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.005 0.010
2025-10-07 21:24:12,487 16720 INFO school odoo.addons.base.models.res_users: Login failed for login:admin from 127.0.0.1 
2025-10-07 21:24:12,491 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:24:12] "POST /web/login HTTP/1.1" 200 - 5 0.013 0.098
2025-10-07 21:24:12,787 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:24:12] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.007 0.013
2025-10-07 21:24:13,167 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:24:13] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.002 0.004
2025-10-07 21:25:17,975 16720 INFO school odoo.addons.base.models.res_users: Login failed for login:odoo from 127.0.0.1 
2025-10-07 21:25:17,978 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:25:17] "POST /web/login HTTP/1.1" 200 - 5 0.013 0.107
2025-10-07 21:25:17,997 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:25:17] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.002 0.003
2025-10-07 21:25:18,118 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:25:18] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.003 0.008
2025-10-07 21:25:53,626 16720 INFO school odoo.addons.base.models.res_users: Login failed for login:admin from 127.0.0.1 
2025-10-07 21:25:53,629 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:25:53] "POST /web/login HTTP/1.1" 200 - 5 0.011 0.095
2025-10-07 21:25:53,646 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:25:53] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.002 0.003
2025-10-07 21:25:53,795 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:25:53] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.002 0.003
2025-10-07 21:26:38,652 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:26:38] "GET /web/database/manager HTTP/1.1" 200 - 4 0.008 1.416
2025-10-07 21:26:48,694 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:26:48] "GET /odoo?db=school HTTP/1.1" 303 - 1 0.001 0.003
2025-10-07 21:26:48,796 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:26:48] "GET /web/login?redirect=/odoo?db%3Dschool HTTP/1.1" 200 - 4 0.008 0.083
2025-10-07 21:26:49,161 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:26:49] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.010 0.010
2025-10-07 21:26:49,403 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:26:49] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.002 0.003
2025-10-07 21:26:51,507 16720 INFO school odoo.addons.base.models.res_users: Login failed for login:admin from 127.0.0.1 
2025-10-07 21:26:51,511 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:26:51] "POST /web/login HTTP/1.1" 200 - 5 0.014 0.110
2025-10-07 21:26:51,806 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:26:51] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.009 0.006
2025-10-07 21:26:52,188 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:26:52] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.002 0.004
2025-10-07 21:27:46,929 16720 INFO school odoo.addons.base.models.res_users: Login failed for login:admin from 127.0.0.1 
2025-10-07 21:27:46,932 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:27:46] "POST /web/login HTTP/1.1" 200 - 5 0.012 0.086
2025-10-07 21:27:47,147 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:27:47] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.002 0.003
2025-10-07 21:27:47,528 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:27:47] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.002 0.003
2025-10-07 21:27:53,311 16720 INFO school odoo.addons.base.models.res_users: Login failed for login:admin from 127.0.0.1 
2025-10-07 21:27:53,314 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:27:53] "POST /web/login HTTP/1.1" 200 - 5 0.013 0.098
2025-10-07 21:27:53,616 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:27:53] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.003 0.004
2025-10-07 21:27:53,980 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:27:53] "GET /web/webclient/translations?hash=f6edbf8f3d4b4718c81674d8056d895de3641cc4&lang= HTTP/1.1" 200 - 2 0.003 0.005
2025-10-07 21:27:57,964 16720 INFO school werkzeug: 127.0.0.1 - - [07/Oct/2025 21:27:57] "GET /web/database/manager HTTP/1.1" 200 - 4 0.009 2.127
2025-10-07 21:29:10,581 16720 INFO None odoo.service.db: Create database `student_db`. 
2025-10-07 21:29:11,262 16720 INFO None odoo.modules.loading: Initializing database student_db 
2025-10-07 21:29:13,631 16720 INFO None odoo.modules.loading: loading 1 modules... 
2025-10-07 21:29:13,631 16720 INFO None odoo.modules.loading: Loading module base (1/1) 
2025-10-07 21:29:13,664 16720 INFO None odoo.registry: module base: creating or updating database tables 
2025-10-07 21:29:14,195 16720 INFO None odoo.models: Prepare computation of ir.module.module.menus_by_module 
2025-10-07 21:29:14,195 16720 INFO None odoo.models: Prepare computation of ir.module.module.reports_by_module 
2025-10-07 21:29:14,196 16720 INFO None odoo.models: Prepare computation of ir.module.module.views_by_module 
2025-10-07 21:29:14,412 16720 INFO None odoo.models: Prepare computation of res.partner.user_id 
2025-10-07 21:29:14,412 16720 INFO None odoo.models: Prepare computation of res.partner.commercial_partner_id 
2025-10-07 21:29:14,412 16720 INFO None odoo.models: Prepare computation of res.partner.complete_name 
2025-10-07 21:29:14,412 16720 INFO None odoo.models: Prepare computation of res.partner.lang 
2025-10-07 21:29:14,413 16720 INFO None odoo.models: Prepare computation of res.partner.company_registry 
2025-10-07 21:29:14,413 16720 INFO None odoo.models: Prepare computation of res.partner.commercial_company_name 
2025-10-07 21:29:14,413 16720 INFO None odoo.models: Prepare computation of res.partner.properties 
2025-10-07 21:29:14,413 16720 INFO None odoo.models: Prepare computation of res.partner.partner_share 
2025-10-07 21:29:14,440 16720 ERROR student_db odoo.sql_db: bad query: b"\n            SELECT latest_version\n            FROM ir_module_module\n             WHERE name='base'\n        "
ERROR: relation "ir_module_module" does not exist
LINE 3:             FROM ir_module_module
                         ^
 
2025-10-07 21:29:14,440 16720 WARNING student_db odoo.addons.base.models.ir_cron: Tried to poll an undefined table on database student_db. 
2025-10-07 21:29:14,488 16720 INFO None odoo.models: Prepare computation of res.currency.decimal_places 
2025-10-07 21:29:14,528 16720 INFO None odoo.models: Prepare computation of res.company.uses_default_logo 
2025-10-07 21:29:14,528 16720 INFO None odoo.models: Prepare computation of res.company.logo_web 
2025-10-07 21:29:14,531 16720 INFO None odoo.models: Computing parent_path for table res_company... 
2025-10-07 21:29:14,583 16720 INFO None odoo.models: Prepare computation of res.users.signature 
2025-10-07 21:29:14,583 16720 INFO None odoo.models: Prepare computation of res.users.share 
2025-10-07 21:29:17,034 16720 INFO None odoo.modules.loading: loading base/data/res_bank.xml 
2025-10-07 21:29:17,046 16720 INFO None odoo.modules.loading: loading base/data/res.lang.csv 
2025-10-07 21:29:17,145 16720 INFO None odoo.modules.loading: loading base/data/res_lang_data.xml 
2025-10-07 21:29:17,207 16720 INFO None odoo.modules.loading: loading base/data/res_partner_data.xml 
2025-10-07 21:29:17,302 16720 INFO None odoo.modules.loading: loading base/data/res_currency_data.xml 
2025-10-07 21:29:18,017 16720 INFO None odoo.modules.loading: loading base/data/res_company_data.xml 
2025-10-07 21:29:18,027 16720 INFO None odoo.modules.loading: loading base/data/res_users_data.xml 
2025-10-07 21:29:19,639 16720 INFO None odoo.modules.loading: loading base/data/report_paperformat_data.xml 
2025-10-07 21:29:19,651 16720 INFO None odoo.modules.loading: loading base/data/res_country_data.xml 
2025-10-07 21:29:20,514 16720 INFO None odoo.modules.loading: loading base/data/ir_demo_data.xml 
2025-10-07 21:29:20,532 16720 INFO None odoo.modules.loading: loading base/security/base_groups.xml 
2025-10-07 21:29:20,734 16720 INFO None odoo.modules.loading: loading base/security/base_security.xml 
2025-10-07 21:29:20,958 16720 INFO None odoo.modules.loading: loading base/wizard/wizard_ir_model_menu_create_views.xml 
2025-10-07 21:29:20,976 16720 INFO None odoo.modules.loading: loading base/views/base_menus.xml 
2025-10-07 21:29:21,092 16720 INFO None odoo.modules.loading: loading base/views/decimal_precision_views.xml 
2025-10-07 21:29:21,119 16720 INFO None odoo.modules.loading: loading base/views/res_config_views.xml 
2025-10-07 21:29:21,129 16720 INFO None odoo.modules.loading: loading base/data/res.country.state.csv 
2025-10-07 21:29:21,467 16720 ERROR student_db odoo.sql_db: bad query: b"\n            SELECT latest_version\n            FROM ir_module_module\n             WHERE name='base'\n        "
ERROR: relation "ir_module_module" does not exist
LINE 3:             FROM ir_module_module
                         ^
 
2025-10-07 21:29:21,468 16720 WARNING student_db odoo.addons.base.models.ir_cron: Tried to poll an undefined table on database student_db. 
2025-10-07 21:29:21,582 16720 INFO None odoo.modules.loading: loading base/views/ir_actions_views.xml 
2025-10-07 21:29:21,821 16720 INFO None odoo.modules.loading: loading base/views/ir_asset_views.xml 
2025-10-07 21:29:21,849 16720 INFO None odoo.modules.loading: loading base/views/ir_config_parameter_views.xml 
2025-10-07 21:29:21,878 16720 INFO None odoo.modules.loading: loading base/views/ir_cron_views.xml 
2025-10-07 21:29:21,923 16720 INFO None odoo.modules.loading: loading base/views/ir_cron_trigger_views.xml 
2025-10-07 21:29:21,951 16720 INFO None odoo.modules.loading: loading base/views/ir_filters_views.xml 
2025-10-07 21:29:21,988 16720 INFO None odoo.modules.loading: loading base/views/ir_mail_server_views.xml 
2025-10-07 21:29:22,020 16720 INFO None odoo.modules.loading: loading base/views/ir_model_views.xml 
2025-10-07 21:29:22,231 16720 INFO None odoo.modules.loading: loading base/views/ir_attachment_views.xml 
2025-10-07 21:29:22,264 16720 INFO None odoo.modules.loading: loading base/views/ir_rule_views.xml 
2025-10-07 21:29:22,296 16720 INFO None odoo.modules.loading: loading base/views/ir_sequence_views.xml 
2025-10-07 21:29:22,328 16720 INFO None odoo.modules.loading: loading base/views/ir_ui_menu_views.xml 
2025-10-07 21:29:22,356 16720 INFO None odoo.modules.loading: loading base/views/ir_ui_view_views.xml 
2025-10-07 21:29:22,424 16720 INFO None odoo.modules.loading: loading base/views/ir_default_views.xml 
2025-10-07 21:29:22,451 16720 INFO None odoo.modules.loading: loading base/data/ir_config_parameter_data.xml 
2025-10-07 21:29:22,458 16720 INFO None odoo.modules.loading: loading base/data/ir_cron_data.xml 
2025-10-07 21:29:22,477 16720 INFO None odoo.modules.loading: loading base/report/ir_model_report.xml 
2025-10-07 21:29:22,483 16720 INFO None odoo.modules.loading: loading base/report/ir_model_templates.xml 
2025-10-07 21:29:22,495 16720 INFO None odoo.modules.loading: loading base/views/ir_logging_views.xml 
2025-10-07 21:29:22,524 16720 INFO None odoo.modules.loading: loading base/views/ir_qweb_widget_templates.xml 
2025-10-07 21:29:22,542 16720 INFO None odoo.modules.loading: loading base/views/ir_module_views.xml 
2025-10-07 21:29:22,615 16720 INFO None odoo.modules.loading: loading base/data/ir_module_category_data.xml 
2025-10-07 21:29:22,664 16720 INFO None odoo.modules.loading: loading base/data/ir_module_module.xml 
2025-10-07 21:29:22,742 16720 INFO None odoo.modules.loading: loading base/report/ir_module_reports.xml 
2025-10-07 21:29:22,747 16720 INFO None odoo.modules.loading: loading base/report/ir_module_report_templates.xml 
2025-10-07 21:29:22,758 16720 INFO None odoo.modules.loading: loading base/wizard/base_module_update_views.xml 
2025-10-07 21:29:22,776 16720 INFO None odoo.modules.loading: loading base/wizard/base_language_install_views.xml 
2025-10-07 21:29:22,794 16720 INFO None odoo.modules.loading: loading base/wizard/base_import_language_views.xml 
2025-10-07 21:29:22,812 16720 INFO None odoo.modules.loading: loading base/wizard/base_module_upgrade_views.xml 
2025-10-07 21:29:22,838 16720 INFO None odoo.modules.loading: loading base/wizard/base_module_uninstall_views.xml 
2025-10-07 21:29:22,851 16720 INFO None odoo.modules.loading: loading base/wizard/base_export_language_views.xml 
2025-10-07 21:29:22,871 16720 INFO None odoo.modules.loading: loading base/wizard/base_partner_merge_views.xml 
2025-10-07 21:29:22,893 16720 INFO None odoo.modules.loading: loading base/data/ir_demo_failure_data.xml 
2025-10-07 21:29:22,918 16720 INFO None odoo.modules.loading: loading base/views/ir_profile_views.xml 
2025-10-07 21:29:22,954 16720 INFO None odoo.modules.loading: loading base/views/res_company_views.xml 
2025-10-07 21:29:22,986 16720 INFO None odoo.modules.loading: loading base/views/res_lang_views.xml 
2025-10-07 21:29:23,015 16720 INFO None odoo.modules.loading: loading base/views/res_partner_views.xml 
2025-10-07 21:29:23,145 16720 INFO None odoo.modules.loading: loading base/views/res_bank_views.xml 
2025-10-07 21:29:23,192 16720 INFO None odoo.modules.loading: loading base/views/res_country_views.xml 
2025-10-07 21:29:23,254 16720 INFO None odoo.modules.loading: loading base/views/res_currency_views.xml 
2025-10-07 21:29:23,311 16720 INFO None odoo.modules.loading: loading base/views/res_groups_views.xml 
2025-10-07 21:29:23,373 16720 INFO None odoo.modules.loading: loading base/views/res_users_views.xml 
2025-10-07 21:29:23,507 16720 INFO None odoo.modules.loading: loading base/views/res_users_apikeys_views.xml 
2025-10-07 21:29:23,515 16720 INFO None odoo.modules.loading: loading base/views/res_device_views.xml 
2025-10-07 21:29:23,546 16720 INFO None odoo.modules.loading: loading base/views/res_users_identitycheck_views.xml 
2025-10-07 21:29:23,556 16720 INFO None odoo.modules.loading: loading base/views/res_config_settings_views.xml 
2025-10-07 21:29:23,567 16720 INFO None odoo.modules.loading: loading base/views/report_paperformat_views.xml 
2025-10-07 21:29:23,600 16720 INFO None odoo.modules.loading: loading base/security/ir.model.access.csv 
2025-10-07 21:29:23,814 16720 INFO None odoo.modules.loading: Module base loaded in 10.18s, 8370 queries (+8378 other) 
2025-10-07 21:29:23,814 16720 INFO None odoo.modules.loading: 1 modules loaded in 10.18s, 8370 queries (+8378 extra) 
2025-10-07 21:29:23,842 16720 INFO None odoo.modules.loading: updating modules list 
2025-10-07 21:29:23,846 16720 INFO None odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-10-07 21:29:25,374 16720 INFO None odoo.modules.loading: loading 14 modules... 
2025-10-07 21:29:25,374 16720 INFO None odoo.modules.loading: Loading module rpc (2/14) 
2025-10-07 21:29:25,402 16720 INFO None odoo.modules.loading: Module rpc loaded in 0.03s, 9 queries (+9 other) 
2025-10-07 21:29:25,402 16720 INFO None odoo.modules.loading: Loading module web (3/14) 
2025-10-07 21:29:25,444 16720 INFO None odoo.registry: module web: creating or updating database tables 
2025-10-07 21:29:26,261 16720 INFO None odoo.modules.loading: loading web/security/ir.model.access.csv 
2025-10-07 21:29:26,278 16720 INFO None odoo.modules.loading: loading web/security/web_security.xml 
2025-10-07 21:29:26,301 16720 INFO None odoo.modules.loading: loading web/views/webclient_templates.xml 
2025-10-07 21:29:26,393 16720 INFO None odoo.modules.loading: loading web/views/report_templates.xml 
2025-10-07 21:29:26,539 16720 INFO None odoo.modules.loading: loading web/views/base_document_layout_views.xml 
2025-10-07 21:29:26,579 16720 INFO None odoo.modules.loading: loading web/views/partner_view.xml 
2025-10-07 21:29:26,596 16720 INFO None odoo.modules.loading: loading web/views/speedscope_template.xml 
2025-10-07 21:29:26,607 16720 INFO None odoo.modules.loading: loading web/views/memory_template.xml 
2025-10-07 21:29:26,621 16720 INFO None odoo.modules.loading: loading web/views/speedscope_config_wizard.xml 
2025-10-07 21:29:26,632 16720 INFO None odoo.modules.loading: loading web/views/neutralize_views.xml 
2025-10-07 21:29:26,650 16720 INFO None odoo.modules.loading: loading web/views/ir_ui_view_views.xml 
2025-10-07 21:29:26,672 16720 INFO None odoo.modules.loading: loading web/data/ir_attachment.xml 
2025-10-07 21:29:26,681 16720 INFO None odoo.modules.loading: loading web/data/report_layout.xml 
2025-10-07 21:29:26,777 16720 INFO None odoo.modules.loading: Module web loaded in 1.37s, 1225 queries (+1225 other) 
2025-10-07 21:29:26,777 16720 INFO None odoo.modules.loading: Loading module api_doc (4/14) 
2025-10-07 21:29:26,782 16720 INFO None odoo.registry: module api_doc: creating or updating database tables 
2025-10-07 21:29:26,822 16720 INFO None odoo.modules.loading: loading api_doc/security/res_groups.xml 
2025-10-07 21:29:26,876 16720 INFO None odoo.modules.loading: loading api_doc/views/docclient.xml 
2025-10-07 21:29:26,916 16720 INFO None odoo.modules.loading: Module api_doc loaded in 0.14s, 63 queries (+63 other) 
2025-10-07 21:29:26,916 16720 INFO None odoo.modules.loading: Loading module auth_totp (5/14) 
2025-10-07 21:29:26,929 16720 INFO None odoo.registry: module auth_totp: creating or updating database tables 
2025-10-07 21:29:27,103 16720 INFO None odoo.modules.loading: loading auth_totp/security/security.xml 
2025-10-07 21:29:27,160 16720 INFO None odoo.modules.loading: loading auth_totp/security/ir.model.access.csv 
2025-10-07 21:29:27,178 16720 INFO None odoo.modules.loading: loading auth_totp/data/ir_action_data.xml 
2025-10-07 21:29:27,196 16720 INFO None odoo.modules.loading: loading auth_totp/views/res_users_views.xml 
2025-10-07 21:29:27,271 16720 INFO None odoo.modules.loading: loading auth_totp/views/templates.xml 
2025-10-07 21:29:27,284 16720 INFO None odoo.modules.loading: loading auth_totp/wizard/auth_totp_wizard_views.xml 
2025-10-07 21:29:27,329 16720 INFO None odoo.modules.loading: Module auth_totp loaded in 0.41s, 214 queries (+214 other) 
2025-10-07 21:29:27,330 16720 INFO None odoo.modules.loading: Loading module base_import (6/14) 
2025-10-07 21:29:27,407 16720 INFO None odoo.registry: module base_import: creating or updating database tables 
2025-10-07 21:29:28,689 16720 INFO None odoo.modules.loading: loading base_import/security/ir.model.access.csv 
2025-10-07 21:29:28,730 16720 INFO None odoo.modules.loading: Module base_import loaded in 1.40s, 953 queries (+953 other) 
2025-10-07 21:29:28,730 16720 INFO None odoo.modules.loading: Loading module base_import_module (7/14) 
2025-10-07 21:29:28,740 16720 INFO None odoo.registry: module base_import_module: creating or updating database tables 
2025-10-07 21:29:28,896 16720 INFO None odoo.modules.loading: loading base_import_module/security/ir.model.access.csv 
2025-10-07 21:29:28,908 16720 INFO None odoo.modules.loading: loading base_import_module/views/base_import_module_view.xml 
2025-10-07 21:29:28,946 16720 INFO None odoo.modules.loading: loading base_import_module/views/ir_module_views.xml 
2025-10-07 21:29:29,010 16720 INFO None odoo.modules.loading: Module base_import_module loaded in 0.28s, 172 queries (+172 other) 
2025-10-07 21:29:29,010 16720 INFO None odoo.modules.loading: Loading module base_setup (8/14) 
2025-10-07 21:29:29,018 16720 INFO None odoo.registry: module base_setup: creating or updating database tables 
2025-10-07 21:29:29,093 16720 INFO None odoo.modules.loading: loading base_setup/data/base_setup_data.xml 
2025-10-07 21:29:29,099 16720 INFO None odoo.modules.loading: loading base_setup/views/res_config_settings_views.xml 
2025-10-07 21:29:29,147 16720 INFO None odoo.modules.loading: loading base_setup/views/res_partner_views.xml 
2025-10-07 21:29:29,173 16720 INFO None odoo.modules.loading: Module base_setup loaded in 0.16s, 152 queries (+152 other) 
2025-10-07 21:29:29,173 16720 INFO None odoo.modules.loading: Loading module bus (9/14) 
2025-10-07 21:29:29,186 16720 INFO None odoo.registry: module bus: creating or updating database tables 
2025-10-07 21:29:29,317 16720 INFO None odoo.modules.loading: loading bus/security/ir.model.access.csv 
2025-10-07 21:29:29,336 16720 INFO None odoo.modules.loading: Module bus loaded in 0.16s, 152 queries (+152 other) 
2025-10-07 21:29:29,337 16720 INFO None odoo.modules.loading: Loading module web_tour (10/14) 
2025-10-07 21:29:29,345 16720 INFO None odoo.registry: module web_tour: creating or updating database tables 
2025-10-07 21:29:29,353 16720 INFO None odoo.models: Prepare computation of res.users.tour_enabled 
2025-10-07 21:29:29,476 16720 INFO None odoo.modules.loading: loading web_tour/security/ir.model.access.csv 
2025-10-07 21:29:29,487 16720 INFO None odoo.modules.loading: loading web_tour/views/tour_views.xml 
2025-10-07 21:29:29,541 16720 INFO None odoo.modules.loading: Module web_tour loaded in 0.20s, 176 queries (+176 other) 
2025-10-07 21:29:29,542 16720 INFO None odoo.modules.loading: Loading module auth_passkey (11/14) 
2025-10-07 21:29:29,550 16720 INFO None odoo.registry: module auth_passkey: creating or updating database tables 
2025-10-07 21:29:29,646 16720 INFO None odoo.modules.loading: loading auth_passkey/views/auth_passkey_key_views.xml 
2025-10-07 21:29:29,677 16720 INFO None odoo.modules.loading: loading auth_passkey/views/auth_passkey_login_templates.xml 
2025-10-07 21:29:29,695 16720 INFO None odoo.modules.loading: loading auth_passkey/views/res_users_identitycheck_views.xml 
2025-10-07 21:29:29,709 16720 INFO None odoo.modules.loading: loading auth_passkey/views/res_users_views.xml 
2025-10-07 21:29:29,739 16720 INFO None odoo.modules.loading: loading auth_passkey/security/ir.model.access.csv 
2025-10-07 21:29:29,750 16720 INFO None odoo.modules.loading: loading auth_passkey/security/security.xml 
2025-10-07 21:29:29,790 16720 INFO None odoo.modules.loading: Module auth_passkey loaded in 0.25s, 218 queries (+218 other) 
2025-10-07 21:29:29,790 16720 INFO None odoo.modules.loading: Loading module html_editor (12/14) 
2025-10-07 21:29:29,837 16720 INFO None odoo.registry: module html_editor: creating or updating database tables 
2025-10-07 21:29:30,641 16720 INFO None odoo.modules.loading: loading html_editor/security/ir.model.access.csv 
2025-10-07 21:29:30,661 16720 INFO None odoo.modules.loading: Module html_editor loaded in 0.87s, 1031 queries (+1031 other) 
2025-10-07 21:29:30,661 16720 INFO None odoo.modules.loading: Loading module iap (13/14) 
2025-10-07 21:29:30,666 16720 INFO None odoo.registry: module iap: creating or updating database tables 
2025-10-07 21:29:30,772 16720 INFO None odoo.modules.loading: loading iap/data/services.xml 
2025-10-07 21:29:30,778 16720 INFO None odoo.modules.loading: loading iap/security/ir.model.access.csv 
2025-10-07 21:29:30,791 16720 INFO None odoo.modules.loading: loading iap/security/ir_rule.xml 
2025-10-07 21:29:30,801 16720 INFO None odoo.modules.loading: loading iap/views/iap_views.xml 
2025-10-07 21:29:30,837 16720 INFO None odoo.modules.loading: loading iap/views/res_config_settings.xml 
2025-10-07 21:29:30,870 16720 INFO None odoo.modules.loading: Module iap loaded in 0.21s, 182 queries (+182 other) 
2025-10-07 21:29:30,870 16720 INFO None odoo.modules.loading: Loading module web_unsplash (14/14) 
2025-10-07 21:29:30,880 16720 INFO None odoo.registry: module web_unsplash: creating or updating database tables 
2025-10-07 21:29:30,938 16720 INFO None odoo.modules.loading: loading web_unsplash/views/res_config_settings_view.xml 
2025-10-07 21:29:30,977 16720 INFO None odoo.modules.loading: Module web_unsplash loaded in 0.11s, 102 queries (+102 other) 
2025-10-07 21:29:30,977 16720 INFO None odoo.modules.loading: 14 modules loaded in 5.60s, 4649 queries (+4649 extra) 
2025-10-07 21:29:31,292 16720 INFO None odoo.modules.loading: Modules loaded. 
2025-10-07 21:29:31,297 16720 INFO None odoo.registry: Registry changed, signaling through the database 
2025-10-07 21:29:31,300 16720 INFO None odoo.registry: Registry loaded in 20.103s 
2025-10-07 21:29:33,119 16720 INFO None odoo.addons.base.models.res_users: Login successful for login:<EMAIL> from 127.0.0.1 
2025-10-07 21:29:33,145 16720 INFO None werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:33] "POST /web/database/create HTTP/1.1" 303 - 19309 12.064 13.720
2025-10-07 21:29:33,179 16720 INFO student_db odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-10-07 21:29:33,195 16720 INFO student_db odoo.addons.base.models.res_device: User 2 inserts device log (D_uXjKnDtJP5plpdB4Ov7-cVbWlrVnmVAQuKDpLb63) 
2025-10-07 21:29:33,736 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:33] "GET /odoo HTTP/1.1" 200 - 55 0.045 0.529
2025-10-07 21:29:33,813 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:33] "GET /web/webclient/load_menus HTTP/1.1" 200 - 12 0.018 0.033
2025-10-07 21:29:34,525 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:34] "GET /web/webclient/translations?hash=&lang=en_US HTTP/1.1" 200 - 2 0.005 0.005
2025-10-07 21:29:34,550 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:34] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 6 0.004 0.007
2025-10-07 21:29:34,809 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:34] "POST /web/action/load HTTP/1.1" 200 - 10 0.013 0.014
2025-10-07 21:29:35,084 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "POST /web/dataset/call_kw/ir.module.module/get_views#ir.module.module.get_views HTTP/1.1" 200 - 45 0.047 0.182
2025-10-07 21:29:35,085 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "GET /web/image/res.partner/3/avatar_128?unique=1759854572000 HTTP/1.1" 200 - 14 0.068 0.118
2025-10-07 21:29:35,085 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 8 0.038 0.141
2025-10-07 21:29:35,172 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range#ir.module.module.search_panel_select_range HTTP/1.1" 200 - 1 0.006 0.010
2025-10-07 21:29:35,188 16720 INFO student_db odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/942969f/bus.websocket_worker_assets.min.js (id:14) 
2025-10-07 21:29:35,224 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "GET /bus/websocket_worker_bundle?v=saas-18.5-1 HTTP/1.1" 304 - 11 0.053 0.239
2025-10-07 21:29:35,439 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "POST /web/dataset/call_kw/ir.module.module/check_module_update#ir.module.module.check_module_update HTTP/1.1" 200 - 2 0.012 0.008
2025-10-07 21:29:35,476 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range#ir.module.module.search_panel_select_range HTTP/1.1" 200 - 42 0.030 0.030
2025-10-07 21:29:35,571 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "GET /websocket?version=saas-18.5-1 HTTP/1.1" 101 - 1 0.004 0.006
2025-10-07 21:29:35,579 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "POST /web/dataset/call_kw/ir.module.module/web_search_read#ir.module.module.web_search_read HTTP/1.1" 200 - 4 0.012 0.149
2025-10-07 21:29:35,700 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:29:35] "POST /web/dataset/call_kw/ir.module.module/check_module_update#ir.module.module.check_module_update HTTP/1.1" 200 - 2 0.003 0.004
2025-10-07 21:30:14,562 16720 INFO student_db odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-10-07 21:30:14,741 16720 INFO student_db odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-10-07 21:30:14,788 16720 INFO student_db odoo.addons.base.models.ir_attachment: filestore gc 11 checked, 0 removed 
2025-10-07 21:30:14,802 16720 INFO student_db odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-10-07 21:30:14,826 16720 INFO student_db odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-10-07 21:30:14,869 16720 INFO student_db odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-10-07 21:30:14,934 16720 INFO student_db odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-10-07 21:30:14,940 16720 INFO student_db odoo.addons.api_doc.models.ir_attachment: GC'd 0 /doc cached index 
2025-10-07 21:30:14,944 16720 INFO student_db odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) fully done (#loop 1; done 44; remaining 0; duration 0.38s) 
2025-10-07 21:30:14,960 16720 INFO student_db odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) starting 
2025-10-07 21:30:14,977 16720 INFO student_db odoo.addons.base.models.ir_cron: Job 'Base: Portal Users Deletion' (2) fully done (#loop 1; done 0; remaining 0; duration 0.02s) 
2025-10-07 21:30:55,460 16720 INFO student_db werkzeug: 127.0.0.1 - - [07/Oct/2025 21:30:55] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.008 0.004
2025-10-07 21:31:15,105 16720 INFO student_db odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-10-07 21:31:15,227 16720 INFO student_db odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-10-07 21:31:15,254 16720 INFO student_db odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-10-07 21:31:15,273 16720 INFO student_db odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-10-07 21:31:15,287 16720 INFO student_db odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-10-07 21:31:15,317 16720 INFO student_db odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-10-07 21:31:15,364 16720 INFO student_db odoo.addons.api_doc.models.ir_attachment: GC'd 0 /doc cached index 
2025-10-07 21:31:15,384 16720 INFO student_db odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-10-07 21:31:15,393 16720 INFO student_db odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) fully done (#loop 1; done 44; remaining 0; duration 0.29s) 
