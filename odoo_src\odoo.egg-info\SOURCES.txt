LICENSE
MANIFEST.in
README.md
requirements.txt
setup.cfg
setup.py
addons/account/__init__.py
addons/account/__manifest__.py
addons/account/controllers/__init__.py
addons/account/controllers/catalog.py
addons/account/controllers/download_docs.py
addons/account/controllers/portal.py
addons/account/controllers/terms.py
addons/account/controllers/tests_shared_js_python.py
addons/account/demo/__init__.py
addons/account/demo/account_demo.py
addons/account/models/__init__.py
addons/account/models/account_account.py
addons/account/models/account_account_tag.py
addons/account/models/account_analytic_account.py
addons/account/models/account_analytic_distribution_model.py
addons/account/models/account_analytic_line.py
addons/account/models/account_analytic_plan.py
addons/account/models/account_bank_statement.py
addons/account/models/account_bank_statement_line.py
addons/account/models/account_cash_rounding.py
addons/account/models/account_code_mapping.py
addons/account/models/account_document_import_mixin.py
addons/account/models/account_full_reconcile.py
addons/account/models/account_incoterms.py
addons/account/models/account_journal.py
addons/account/models/account_journal_dashboard.py
addons/account/models/account_lock_exception.py
addons/account/models/account_move.py
addons/account/models/account_move_line.py
addons/account/models/account_move_line_tax_details.py
addons/account/models/account_move_send.py
addons/account/models/account_partial_reconcile.py
addons/account/models/account_payment.py
addons/account/models/account_payment_method.py
addons/account/models/account_payment_term.py
addons/account/models/account_reconcile_model.py
addons/account/models/account_report.py
addons/account/models/account_root.py
addons/account/models/account_tax.py
addons/account/models/chart_template.py
addons/account/models/company.py
addons/account/models/decimal_precision.py
addons/account/models/digest.py
addons/account/models/ir_actions_report.py
addons/account/models/ir_attachment.py
addons/account/models/ir_http.py
addons/account/models/ir_module.py
addons/account/models/kpi_provider.py
addons/account/models/mail_message.py
addons/account/models/mail_template.py
addons/account/models/mail_tracking_value.py
addons/account/models/merge_partner_automatic.py
addons/account/models/onboarding_onboarding.py
addons/account/models/onboarding_onboarding_step.py
addons/account/models/partner.py
addons/account/models/product.py
addons/account/models/product_catalog_mixin.py
addons/account/models/res_config_settings.py
addons/account/models/res_country_group.py
addons/account/models/res_currency.py
addons/account/models/res_partner_bank.py
addons/account/models/res_users.py
addons/account/models/sequence_mixin.py
addons/account/models/template_generic_coa.py
addons/account/models/uom_uom.py
addons/account/report/__init__.py
addons/account/report/account_hash_integrity_templates.py
addons/account/report/account_invoice_report.py
addons/account/tests/__init__.py
addons/account/tests/common.py
addons/account/tests/test_account_account.py
addons/account/tests/test_account_all_l10n.py
addons/account/tests/test_account_analytic.py
addons/account/tests/test_account_bank_statement.py
addons/account/tests/test_account_bill_deductibility.py
addons/account/tests/test_account_inalterable_hash.py
addons/account/tests/test_account_incoming_supplier_invoice.py
addons/account/tests/test_account_invoice_report.py
addons/account/tests/test_account_journal.py
addons/account/tests/test_account_journal_dashboard.py
addons/account/tests/test_account_journal_dashboard_common.py
addons/account/tests/test_account_lock_exception.py
addons/account/tests/test_account_merge_wizard.py
addons/account/tests/test_account_move_attachment.py
addons/account/tests/test_account_move_date_algorithm.py
addons/account/tests/test_account_move_duplicate.py
addons/account/tests/test_account_move_entry.py
addons/account/tests/test_account_move_import_template.py
addons/account/tests/test_account_move_in_invoice.py
addons/account/tests/test_account_move_in_refund.py
addons/account/tests/test_account_move_line_tax_details.py
addons/account/tests/test_account_move_out_invoice.py
addons/account/tests/test_account_move_out_refund.py
addons/account/tests/test_account_move_payments_widget.py
addons/account/tests/test_account_move_reconcile.py
addons/account/tests/test_account_move_send.py
addons/account/tests/test_account_partner.py
addons/account/tests/test_account_payment.py
addons/account/tests/test_account_payment_duplicate.py
addons/account/tests/test_account_payment_items.py
addons/account/tests/test_account_payment_method_line.py
addons/account/tests/test_account_payment_register.py
addons/account/tests/test_account_report.py
addons/account/tests/test_account_tax.py
addons/account/tests/test_account_to_check.py
addons/account/tests/test_audit_trail.py
addons/account/tests/test_chart_template.py
addons/account/tests/test_company_branch.py
addons/account/tests/test_dict_to_xml.py
addons/account/tests/test_digest.py
addons/account/tests/test_download_docs.py
addons/account/tests/test_duplicate_res_partner_bank.py
addons/account/tests/test_early_payment_discount.py
addons/account/tests/test_fiscal_position.py
addons/account/tests/test_invoice_taxes.py
addons/account/tests/test_ir_actions_report.py
addons/account/tests/test_kpi_provider.py
addons/account/tests/test_mail_tracking_value.py
addons/account/tests/test_multivat.py
addons/account/tests/test_payment_term.py
addons/account/tests/test_portal_attachment.py
addons/account/tests/test_portal_invoice.py
addons/account/tests/test_product.py
addons/account/tests/test_res_partner_merge.py
addons/account/tests/test_sequence_mixin.py
addons/account/tests/test_setup_wizard.py
addons/account/tests/test_structured_reference.py
addons/account/tests/test_tax.py
addons/account/tests/test_tax_report.py
addons/account/tests/test_taxes_base_lines_tax_details.py
addons/account/tests/test_taxes_computation.py
addons/account/tests/test_taxes_dispatching_base_lines.py
addons/account/tests/test_taxes_downpayment.py
addons/account/tests/test_taxes_global_discount.py
addons/account/tests/test_taxes_tax_totals_summary.py
addons/account/tests/test_tour.py
addons/account/tests/test_transfer_wizard.py
addons/account/tests/test_unexpected_invoice.py
addons/account/tools/__init__.py
addons/account/tools/dict_to_xml.py
addons/account/tools/structured_reference.py
addons/account/wizard/__init__.py
addons/account/wizard/account_automatic_entry_wizard.py
addons/account/wizard/account_autopost_bills_wizard.py
addons/account/wizard/account_merge_wizard.py
addons/account/wizard/account_move_reversal.py
addons/account/wizard/account_move_send_batch_wizard.py
addons/account/wizard/account_move_send_wizard.py
addons/account/wizard/account_payment_register.py
addons/account/wizard/account_resequence.py
addons/account/wizard/account_secure_entries_wizard.py
addons/account/wizard/account_validate_account_move.py
addons/account/wizard/accrued_orders.py
addons/account/wizard/base_document_layout.py
addons/account/wizard/base_partner_merge.py
addons/account/wizard/setup_wizards.py
addons/account_check_printing/__init__.py
addons/account_check_printing/__manifest__.py
addons/account_check_printing/models/__init__.py
addons/account_check_printing/models/account_journal.py
addons/account_check_printing/models/account_payment.py
addons/account_check_printing/models/account_payment_method.py
addons/account_check_printing/models/res_company.py
addons/account_check_printing/models/res_config_settings.py
addons/account_check_printing/tests/__init__.py
addons/account_check_printing/tests/test_print_check.py
addons/account_check_printing/wizard/__init__.py
addons/account_check_printing/wizard/print_prenumbered_checks.py
addons/account_debit_note/__init__.py
addons/account_debit_note/__manifest__.py
addons/account_debit_note/models/__init__.py
addons/account_debit_note/models/account_journal.py
addons/account_debit_note/models/account_move.py
addons/account_debit_note/tests/__init__.py
addons/account_debit_note/tests/test_out_debit_note.py
addons/account_debit_note/wizard/__init__.py
addons/account_debit_note/wizard/account_debit_note.py
addons/account_edi/__init__.py
addons/account_edi/__manifest__.py
addons/account_edi/models/__init__.py
addons/account_edi/models/account_edi_document.py
addons/account_edi/models/account_edi_format.py
addons/account_edi/models/account_journal.py
addons/account_edi/models/account_move.py
addons/account_edi/models/account_move_send.py
addons/account_edi/models/ir_actions_report.py
addons/account_edi/models/ir_attachment.py
addons/account_edi/tests/__init__.py
addons/account_edi/tests/common.py
addons/account_edi/tests/test_edi.py
addons/account_edi/tests/test_import_vendor_bill.py
addons/account_edi/wizard/__init__.py
addons/account_edi/wizard/account_resequence.py
addons/account_edi_proxy_client/__init__.py
addons/account_edi_proxy_client/__manifest__.py
addons/account_edi_proxy_client/models/__init__.py
addons/account_edi_proxy_client/models/account_edi_proxy_auth.py
addons/account_edi_proxy_client/models/account_edi_proxy_user.py
addons/account_edi_proxy_client/models/key.py
addons/account_edi_proxy_client/models/res_company.py
addons/account_edi_ubl_cii/__init__.py
addons/account_edi_ubl_cii/__manifest__.py
addons/account_edi_ubl_cii/models/__init__.py
addons/account_edi_ubl_cii/models/account_edi_common.py
addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py
addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py
addons/account_edi_ubl_cii/models/account_edi_xml_ubl_21.py
addons/account_edi_ubl_cii/models/account_edi_xml_ubl_a_nz.py
addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py
addons/account_edi_ubl_cii/models/account_edi_xml_ubl_efff.py
addons/account_edi_ubl_cii/models/account_edi_xml_ubl_nlcius.py
addons/account_edi_ubl_cii/models/account_edi_xml_ubl_sg.py
addons/account_edi_ubl_cii/models/account_edi_xml_ubl_xrechnung.py
addons/account_edi_ubl_cii/models/account_move.py
addons/account_edi_ubl_cii/models/account_move_send.py
addons/account_edi_ubl_cii/models/account_tax.py
addons/account_edi_ubl_cii/models/ir_actions_report.py
addons/account_edi_ubl_cii/models/res_partner.py
addons/account_edi_ubl_cii/tests/__init__.py
addons/account_edi_ubl_cii/tests/test_autopost_bills.py
addons/account_edi_ubl_cii/tests/test_download_docs.py
addons/account_edi_ubl_cii/tests/test_partner_peppol_fields.py
addons/account_edi_ubl_cii/tests/test_ubl_bis3.py
addons/account_edi_ubl_cii/tests/test_ubl_cii.py
addons/account_edi_ubl_cii/tools/__init__.py
addons/account_edi_ubl_cii/tools/ubl_21_common.py
addons/account_edi_ubl_cii/tools/ubl_21_credit_note.py
addons/account_edi_ubl_cii/tools/ubl_21_debit_note.py
addons/account_edi_ubl_cii/tools/ubl_21_invoice.py
addons/account_edi_ubl_cii/tools/ubl_21_order.py
addons/account_fleet/__init__.py
addons/account_fleet/__manifest__.py
addons/account_fleet/models/__init__.py
addons/account_fleet/models/account_move.py
addons/account_fleet/models/fleet_vehicle.py
addons/account_fleet/models/fleet_vehicle_log_services.py
addons/account_fleet/tests/__init__.py
addons/account_fleet/tests/test_account_fleet.py
addons/account_fleet/tests/test_fleet_vehicle_log_services.py
addons/account_fleet/wizard/__init__.py
addons/account_fleet/wizard/account_automatic_entry_wizard.py
addons/account_payment/__init__.py
addons/account_payment/__manifest__.py
addons/account_payment/controllers/__init__.py
addons/account_payment/controllers/payment.py
addons/account_payment/controllers/portal.py
addons/account_payment/models/__init__.py
addons/account_payment/models/account_journal.py
addons/account_payment/models/account_move.py
addons/account_payment/models/account_payment.py
addons/account_payment/models/account_payment_method.py
addons/account_payment/models/account_payment_method_line.py
addons/account_payment/models/payment_provider.py
addons/account_payment/models/payment_transaction.py
addons/account_payment/tests/__init__.py
addons/account_payment/tests/common.py
addons/account_payment/tests/test_account_payment.py
addons/account_payment/tests/test_payment_flows.py
addons/account_payment/tests/test_payment_provider.py
addons/account_payment/wizards/__init__.py
addons/account_payment/wizards/account_payment_register.py
addons/account_payment/wizards/payment_link_wizard.py
addons/account_payment/wizards/payment_refund_wizard.py
addons/account_payment/wizards/res_config_settings.py
addons/account_peppol/__init__.py
addons/account_peppol/__manifest__.py
addons/account_peppol/exceptions.py
addons/account_peppol/controllers/__init__.py
addons/account_peppol/controllers/portal.py
addons/account_peppol/controllers/webhooks.py
addons/account_peppol/models/__init__.py
addons/account_peppol/models/account_edi_common.py
addons/account_peppol/models/account_edi_proxy_user.py
addons/account_peppol/models/account_edi_ubl_xml.py
addons/account_peppol/models/account_journal.py
addons/account_peppol/models/account_move.py
addons/account_peppol/models/account_move_send.py
addons/account_peppol/models/res_company.py
addons/account_peppol/models/res_config_settings.py
addons/account_peppol/models/res_partner.py
addons/account_peppol/tests/__init__.py
addons/account_peppol/tests/test_peppol_messages.py
addons/account_peppol/tests/test_peppol_out_of_sync_resolution.py
addons/account_peppol/tests/test_peppol_participant.py
addons/account_peppol/tools/__init__.py
addons/account_peppol/tools/demo_utils.py
addons/account_peppol/wizard/__init__.py
addons/account_peppol/wizard/account_move_send_batch_wizard.py
addons/account_peppol/wizard/account_move_send_wizard.py
addons/account_peppol/wizard/peppol_config_wizard.py
addons/account_peppol/wizard/peppol_registration.py
addons/account_peppol_advanced_fields/__init__.py
addons/account_peppol_advanced_fields/__manifest__.py
addons/account_peppol_advanced_fields/models/__init__.py
addons/account_peppol_advanced_fields/models/account_edi_xml_ubl_bis3.py
addons/account_peppol_advanced_fields/models/account_move.py
addons/account_peppol_advanced_fields/tests/__init__.py
addons/account_peppol_advanced_fields/tests/test_ubl_bis3_peppol.py
addons/account_qr_code_emv/__init__.py
addons/account_qr_code_emv/__manifest__.py
addons/account_qr_code_emv/const.py
addons/account_qr_code_emv/models/__init__.py
addons/account_qr_code_emv/models/res_bank.py
addons/account_qr_code_sepa/__init__.py
addons/account_qr_code_sepa/__manifest__.py
addons/account_qr_code_sepa/models/__init__.py
addons/account_qr_code_sepa/models/res_bank.py
addons/account_qr_code_sepa/tests/__init__.py
addons/account_qr_code_sepa/tests/test_sepa_qr.py
addons/account_tax_python/__init__.py
addons/account_tax_python/__manifest__.py
addons/account_tax_python/models/__init__.py
addons/account_tax_python/models/account_tax.py
addons/account_tax_python/tests/__init__.py
addons/account_tax_python/tests/common.py
addons/account_tax_python/tests/test_taxes_computation.py
addons/account_tax_python/tools/formula_utils.py
addons/account_test/__init__.py
addons/account_test/__manifest__.py
addons/account_test/models/__init__.py
addons/account_test/models/accounting_assert_test.py
addons/account_test/report/__init__.py
addons/account_test/report/report_account_test.py
addons/account_update_tax_tags/__init__.py
addons/account_update_tax_tags/__manifest__.py
addons/account_update_tax_tags/tests/__init__.py
addons/account_update_tax_tags/tests/test_account_update_tax_tags_wizard.py
addons/account_update_tax_tags/wizard/__init__.py
addons/account_update_tax_tags/wizard/account_update_tax_tags_wizard.py
addons/analytic/__init__.py
addons/analytic/__manifest__.py
addons/analytic/models/__init__.py
addons/analytic/models/analytic_account.py
addons/analytic/models/analytic_distribution_model.py
addons/analytic/models/analytic_line.py
addons/analytic/models/analytic_mixin.py
addons/analytic/models/analytic_plan.py
addons/analytic/models/res_config_settings.py
addons/analytic/tests/__init__.py
addons/analytic/tests/common.py
addons/analytic/tests/test_analytic_account.py
addons/analytic/tests/test_analytic_dynamic_update.py
addons/analytic/tests/test_analytic_mixin.py
addons/analytic/tests/test_plan_operations.py
addons/api_doc/__init__.py
addons/api_doc/__manifest__.py
addons/api_doc/controllers/__init__.py
addons/api_doc/controllers/api_doc.py
addons/api_doc/models/__init__.py
addons/api_doc/models/ir_attachment.py
addons/api_doc/tests/__init__.py
addons/api_doc/tests/test_doc.py
addons/attachment_indexation/__init__.py
addons/attachment_indexation/__manifest__.py
addons/attachment_indexation/models/__init__.py
addons/attachment_indexation/models/ir_attachment.py
addons/attachment_indexation/tests/__init__.py
addons/attachment_indexation/tests/test_indexation.py
addons/auth_ldap/__init__.py
addons/auth_ldap/__manifest__.py
addons/auth_ldap/models/__init__.py
addons/auth_ldap/models/res_company.py
addons/auth_ldap/models/res_company_ldap.py
addons/auth_ldap/models/res_config_settings.py
addons/auth_ldap/models/res_users.py
addons/auth_ldap/tests/__init__.py
addons/auth_ldap/tests/test_auth_ldap.py
addons/auth_oauth/__init__.py
addons/auth_oauth/__manifest__.py
addons/auth_oauth/controllers/__init__.py
addons/auth_oauth/controllers/main.py
addons/auth_oauth/models/__init__.py
addons/auth_oauth/models/auth_oauth.py
addons/auth_oauth/models/ir_config_parameter.py
addons/auth_oauth/models/res_config_settings.py
addons/auth_oauth/models/res_users.py
addons/auth_passkey/__init__.py
addons/auth_passkey/__manifest__.py
addons/auth_passkey/_vendor/webauthn/__init__.py
addons/auth_passkey/_vendor/webauthn/py.typed
addons/auth_passkey/_vendor/webauthn/authentication/__init__.py
addons/auth_passkey/_vendor/webauthn/authentication/generate_authentication_options.py
addons/auth_passkey/_vendor/webauthn/authentication/verify_authentication_response.py
addons/auth_passkey/_vendor/webauthn/helpers/__init__.py
addons/auth_passkey/_vendor/webauthn/helpers/aaguid_to_string.py
addons/auth_passkey/_vendor/webauthn/helpers/algorithms.py
addons/auth_passkey/_vendor/webauthn/helpers/base64url_to_bytes.py
addons/auth_passkey/_vendor/webauthn/helpers/bytes_to_base64url.py
addons/auth_passkey/_vendor/webauthn/helpers/byteslike_to_bytes.py
addons/auth_passkey/_vendor/webauthn/helpers/cose.py
addons/auth_passkey/_vendor/webauthn/helpers/decode_credential_public_key.py
addons/auth_passkey/_vendor/webauthn/helpers/decoded_public_key_to_cryptography.py
addons/auth_passkey/_vendor/webauthn/helpers/encode_cbor.py
addons/auth_passkey/_vendor/webauthn/helpers/exceptions.py
addons/auth_passkey/_vendor/webauthn/helpers/generate_challenge.py
addons/auth_passkey/_vendor/webauthn/helpers/generate_user_handle.py
addons/auth_passkey/_vendor/webauthn/helpers/hash_by_alg.py
addons/auth_passkey/_vendor/webauthn/helpers/known_root_certs.py
addons/auth_passkey/_vendor/webauthn/helpers/options_to_json.py
addons/auth_passkey/_vendor/webauthn/helpers/parse_attestation_object.py
addons/auth_passkey/_vendor/webauthn/helpers/parse_attestation_statement.py
addons/auth_passkey/_vendor/webauthn/helpers/parse_authentication_credential_json.py
addons/auth_passkey/_vendor/webauthn/helpers/parse_authenticator_data.py
addons/auth_passkey/_vendor/webauthn/helpers/parse_backup_flags.py
addons/auth_passkey/_vendor/webauthn/helpers/parse_cbor.py
addons/auth_passkey/_vendor/webauthn/helpers/parse_client_data_json.py
addons/auth_passkey/_vendor/webauthn/helpers/parse_registration_credential_json.py
addons/auth_passkey/_vendor/webauthn/helpers/pem_cert_bytes_to_open_ssl_x509.py
addons/auth_passkey/_vendor/webauthn/helpers/snake_case_to_camel_case.py
addons/auth_passkey/_vendor/webauthn/helpers/structs.py
addons/auth_passkey/_vendor/webauthn/helpers/validate_certificate_chain.py
addons/auth_passkey/_vendor/webauthn/helpers/verify_safetynet_timestamp.py
addons/auth_passkey/_vendor/webauthn/helpers/verify_signature.py
addons/auth_passkey/_vendor/webauthn/helpers/asn1/__init__.py
addons/auth_passkey/_vendor/webauthn/helpers/asn1/android_key.py
addons/auth_passkey/_vendor/webauthn/helpers/tpm/__init__.py
addons/auth_passkey/_vendor/webauthn/helpers/tpm/parse_cert_info.py
addons/auth_passkey/_vendor/webauthn/helpers/tpm/parse_pub_area.py
addons/auth_passkey/_vendor/webauthn/helpers/tpm/structs.py
addons/auth_passkey/_vendor/webauthn/registration/__init__.py
addons/auth_passkey/_vendor/webauthn/registration/generate_registration_options.py
addons/auth_passkey/_vendor/webauthn/registration/verify_registration_response.py
addons/auth_passkey/_vendor/webauthn/registration/formats/__init__.py
addons/auth_passkey/_vendor/webauthn/registration/formats/android_key.py
addons/auth_passkey/_vendor/webauthn/registration/formats/android_safetynet.py
addons/auth_passkey/_vendor/webauthn/registration/formats/apple.py
addons/auth_passkey/_vendor/webauthn/registration/formats/fido_u2f.py
addons/auth_passkey/_vendor/webauthn/registration/formats/packed.py
addons/auth_passkey/_vendor/webauthn/registration/formats/tpm.py
addons/auth_passkey/controllers/__init__.py
addons/auth_passkey/controllers/main.py
addons/auth_passkey/models/__init__.py
addons/auth_passkey/models/auth_passkey_key.py
addons/auth_passkey/models/res_users.py
addons/auth_passkey/models/res_users_identitycheck.py
addons/auth_passkey/tests/__init__.py
addons/auth_passkey/tests/test_passkey_demo.py
addons/auth_passkey_portal/__init__.py
addons/auth_passkey_portal/__manifest__.py
addons/auth_passkey_portal/tests/__init__.py
addons/auth_passkey_portal/tests/test_passkey_portal.py
addons/auth_password_policy/__init__.py
addons/auth_password_policy/__manifest__.py
addons/auth_password_policy/models/__init__.py
addons/auth_password_policy/models/res_config_settings.py
addons/auth_password_policy/models/res_users.py
addons/auth_password_policy_portal/__init__.py
addons/auth_password_policy_portal/__manifest__.py
addons/auth_password_policy_portal/controllers.py
addons/auth_password_policy_portal/models/__init__.py
addons/auth_password_policy_portal/models/ir_http.py
addons/auth_password_policy_signup/__init__.py
addons/auth_password_policy_signup/__manifest__.py
addons/auth_password_policy_signup/controllers.py
addons/auth_password_policy_signup/models/__init__.py
addons/auth_password_policy_signup/models/ir_http.py
addons/auth_signup/__init__.py
addons/auth_signup/__manifest__.py
addons/auth_signup/controllers/__init__.py
addons/auth_signup/controllers/main.py
addons/auth_signup/models/__init__.py
addons/auth_signup/models/ir_http.py
addons/auth_signup/models/res_config_settings.py
addons/auth_signup/models/res_partner.py
addons/auth_signup/models/res_users.py
addons/auth_signup/tests/__init__.py
addons/auth_signup/tests/test_auth_signup.py
addons/auth_signup/tests/test_login.py
addons/auth_signup/tests/test_reset_password.py
addons/auth_timeout/__init__.py
addons/auth_timeout/__manifest__.py
addons/auth_timeout/controllers/__init__.py
addons/auth_timeout/controllers/auth_passkey_webauthn.py
addons/auth_timeout/controllers/main.py
addons/auth_timeout/controllers/web_home.py
addons/auth_timeout/models/__init__.py
addons/auth_timeout/models/auth_totp_device.py
addons/auth_timeout/models/ir_http.py
addons/auth_timeout/models/ir_websocket.py
addons/auth_timeout/models/res_groups.py
addons/auth_timeout/models/res_users.py
addons/auth_timeout/tests/__init__.py
addons/auth_timeout/tests/test_auth_timeout.py
addons/auth_totp/__init__.py
addons/auth_totp/__manifest__.py
addons/auth_totp/controllers/__init__.py
addons/auth_totp/controllers/home.py
addons/auth_totp/models/__init__.py
addons/auth_totp/models/auth_totp.py
addons/auth_totp/models/auth_totp_rate_limit_log.py
addons/auth_totp/models/res_users.py
addons/auth_totp/models/totp.py
addons/auth_totp/tests/__init__.py
addons/auth_totp/tests/test_apikeys.py
addons/auth_totp/tests/test_totp.py
addons/auth_totp/wizard/__init__.py
addons/auth_totp/wizard/auth_totp_wizard.py
addons/auth_totp_mail/__init__.py
addons/auth_totp_mail/__manifest__.py
addons/auth_totp_mail/controllers/__init__.py
addons/auth_totp_mail/controllers/home.py
addons/auth_totp_mail/models/__init__.py
addons/auth_totp_mail/models/auth_totp_device.py
addons/auth_totp_mail/models/res_config_settings.py
addons/auth_totp_mail/models/res_users.py
addons/auth_totp_mail/tests/__init__.py
addons/auth_totp_mail/tests/test_auth_signup.py
addons/auth_totp_mail/tests/test_notify_security_update_totp.py
addons/auth_totp_mail/tests/test_totp.py
addons/auth_totp_portal/__init__.py
addons/auth_totp_portal/__manifest__.py
addons/auth_totp_portal/models/__init__.py
addons/auth_totp_portal/models/res_users.py
addons/auth_totp_portal/tests/__init__.py
addons/auth_totp_portal/tests/test_tour.py
addons/barcodes/__init__.py
addons/barcodes/__manifest__.py
addons/barcodes/models/__init__.py
addons/barcodes/models/barcode_events_mixin.py
addons/barcodes/models/barcode_nomenclature.py
addons/barcodes/models/barcode_rule.py
addons/barcodes/models/ir_http.py
addons/barcodes/models/res_company.py
addons/barcodes/tests/__init__.py
addons/barcodes/tests/test_barcode_nomenclature.py
addons/barcodes_gs1_nomenclature/__init__.py
addons/barcodes_gs1_nomenclature/__manifest__.py
addons/barcodes_gs1_nomenclature/models/__init__.py
addons/barcodes_gs1_nomenclature/models/barcode_nomenclature.py
addons/barcodes_gs1_nomenclature/models/barcode_rule.py
addons/barcodes_gs1_nomenclature/models/ir_http.py
addons/barcodes_gs1_nomenclature/tests/__init__.py
addons/barcodes_gs1_nomenclature/tests/test_barcodes_gs1_nomenclature.py
addons/base_address_extended/__init__.py
addons/base_address_extended/__manifest__.py
addons/base_address_extended/models/__init__.py
addons/base_address_extended/models/res_city.py
addons/base_address_extended/models/res_country.py
addons/base_address_extended/models/res_partner.py
addons/base_address_extended/tests/__init__.py
addons/base_address_extended/tests/test_street_fields.py
addons/base_automation/__init__.py
addons/base_automation/__manifest__.py
addons/base_automation/controllers/__init__.py
addons/base_automation/controllers/main.py
addons/base_automation/models/__init__.py
addons/base_automation/models/base_automation.py
addons/base_automation/models/ir_actions_server.py
addons/base_automation/tests/__init__.py
addons/base_automation/tests/test_automation.py
addons/base_automation/tests/test_mail_composer.py
addons/base_geolocalize/__init__.py
addons/base_geolocalize/__manifest__.py
addons/base_geolocalize/models/__init__.py
addons/base_geolocalize/models/base_geocoder.py
addons/base_geolocalize/models/res_config_settings.py
addons/base_geolocalize/models/res_partner.py
addons/base_geolocalize/tests/__init__.py
addons/base_geolocalize/tests/test_geolocalize.py
addons/base_iban/__init__.py
addons/base_iban/__manifest__.py
addons/base_iban/models/__init__.py
addons/base_iban/models/res_partner_bank.py
addons/base_import/__init__.py
addons/base_import/__manifest__.py
addons/base_import/controllers/__init__.py
addons/base_import/controllers/main.py
addons/base_import/models/__init__.py
addons/base_import/models/base_import.py
addons/base_import/models/odf_ods_reader.py
addons/base_import_module/__init__.py
addons/base_import_module/__manifest__.py
addons/base_import_module/controllers/__init__.py
addons/base_import_module/controllers/main.py
addons/base_import_module/models/__init__.py
addons/base_import_module/models/base_import_module.py
addons/base_import_module/models/ir_http.py
addons/base_import_module/models/ir_module.py
addons/base_import_module/models/ir_ui_view.py
addons/base_import_module/tests/__init__.py
addons/base_import_module/tests/test_cloc.py
addons/base_import_module/tests/test_import_module.py
addons/base_import_module/wizard/__init__.py
addons/base_import_module/wizard/base_module_uninstall.py
addons/base_install_request/__init__.py
addons/base_install_request/__manifest__.py
addons/base_install_request/models/__init__.py
addons/base_install_request/models/ir_module_module.py
addons/base_install_request/wizard/__init__.py
addons/base_install_request/wizard/base_module_install_request.py
addons/base_setup/__init__.py
addons/base_setup/__manifest__.py
addons/base_setup/controllers/__init__.py
addons/base_setup/controllers/main.py
addons/base_setup/models/__init__.py
addons/base_setup/models/ir_http.py
addons/base_setup/models/kpi_provider.py
addons/base_setup/models/res_config_settings.py
addons/base_setup/models/res_users.py
addons/base_setup/tests/__init__.py
addons/base_setup/tests/test_default_group.py
addons/base_setup/tests/test_res_config.py
addons/base_setup/tests/test_res_config_doc_links.py
addons/base_sparse_field/__init__.py
addons/base_sparse_field/__manifest__.py
addons/base_sparse_field/models/__init__.py
addons/base_sparse_field/models/fields.py
addons/base_sparse_field/models/models.py
addons/base_sparse_field/tests/__init__.py
addons/base_sparse_field/tests/test_sparse_fields.py
addons/base_vat/__init__.py
addons/base_vat/__manifest__.py
addons/base_vat/models/__init__.py
addons/base_vat/models/res_company.py
addons/base_vat/models/res_config_settings.py
addons/base_vat/models/res_country.py
addons/base_vat/models/res_partner.py
addons/base_vat/tests/__init__.py
addons/base_vat/tests/test_vat_numbers.py
addons/board/__init__.py
addons/board/__manifest__.py
addons/board/controllers/__init__.py
addons/board/controllers/main.py
addons/board/models/__init__.py
addons/board/models/board.py
addons/bus/__init__.py
addons/bus/__manifest__.py
addons/bus/websocket.py
addons/bus/controllers/__init__.py
addons/bus/controllers/home.py
addons/bus/controllers/main.py
addons/bus/controllers/websocket.py
addons/bus/models/__init__.py
addons/bus/models/bus.py
addons/bus/models/bus_listener_mixin.py
addons/bus/models/ir_attachment.py
addons/bus/models/ir_http.py
addons/bus/models/ir_model.py
addons/bus/models/ir_websocket.py
addons/bus/models/res_groups.py
addons/bus/models/res_partner.py
addons/bus/models/res_users.py
addons/bus/models/res_users_settings.py
addons/bus/tests/__init__.py
addons/bus/tests/common.py
addons/bus/tests/test_assetsbundle.py
addons/bus/tests/test_bus_gc.py
addons/bus/tests/test_close_websocket_after_tour.py
addons/bus/tests/test_health.py
addons/bus/tests/test_ir_model.py
addons/bus/tests/test_ir_websocket.py
addons/bus/tests/test_notify.py
addons/bus/tests/test_websocket_caryall.py
addons/bus/tests/test_websocket_controller.py
addons/bus/tests/test_websocket_rate_limiting.py
addons/calendar/__init__.py
addons/calendar/__manifest__.py
addons/calendar/controllers/__init__.py
addons/calendar/controllers/main.py
addons/calendar/models/__init__.py
addons/calendar/models/calendar_alarm.py
addons/calendar/models/calendar_alarm_manager.py
addons/calendar/models/calendar_attendee.py
addons/calendar/models/calendar_event.py
addons/calendar/models/calendar_event_type.py
addons/calendar/models/calendar_filter.py
addons/calendar/models/calendar_recurrence.py
addons/calendar/models/discuss_channel.py
addons/calendar/models/ir_http.py
addons/calendar/models/mail_activity.py
addons/calendar/models/mail_activity_mixin.py
addons/calendar/models/mail_activity_type.py
addons/calendar/models/res_partner.py
addons/calendar/models/res_users.py
addons/calendar/models/res_users_settings.py
addons/calendar/models/utils.py
addons/calendar/tests/__init__.py
addons/calendar/tests/test_access_rights.py
addons/calendar/tests/test_attendees.py
addons/calendar/tests/test_calendar.py
addons/calendar/tests/test_calendar_activity.py
addons/calendar/tests/test_calendar_controller.py
addons/calendar/tests/test_calendar_recurrent_event_case2.py
addons/calendar/tests/test_calendar_tour.py
addons/calendar/tests/test_event_notifications.py
addons/calendar/tests/test_event_recurrence.py
addons/calendar/tests/test_mail_activity_mixin.py
addons/calendar/tests/test_recurrence_rule.py
addons/calendar/tests/test_res_partner.py
addons/calendar/tests/test_res_users.py
addons/calendar/wizard/__init__.py
addons/calendar/wizard/calendar_popover_delete_wizard.py
addons/calendar/wizard/calendar_provider_config.py
addons/calendar/wizard/mail_activity_schedule.py
addons/calendar_sms/__init__.py
addons/calendar_sms/__manifest__.py
addons/calendar_sms/models/__init__.py
addons/calendar_sms/models/calendar_alarm.py
addons/calendar_sms/models/calendar_alarm_manager.py
addons/calendar_sms/models/calendar_event.py
addons/calendar_sms/tests/__init__.py
addons/calendar_sms/tests/test_calendar_sms.py
addons/certificate/__init__.py
addons/certificate/__manifest__.py
addons/certificate/models/__init__.py
addons/certificate/models/certificate.py
addons/certificate/models/key.py
addons/certificate/tests/__init__.py
addons/certificate/tests/test_keys_certificates.py
addons/certificate/tools/__init__.py
addons/certificate/tools/certificate_adapter.py
addons/cloud_storage/__init__.py
addons/cloud_storage/__manifest__.py
addons/cloud_storage/controllers/__init__.py
addons/cloud_storage/controllers/attachment.py
addons/cloud_storage/models/__init__.py
addons/cloud_storage/models/ir_attachment.py
addons/cloud_storage/models/ir_http.py
addons/cloud_storage/models/res_config_settings.py
addons/cloud_storage_azure/__init__.py
addons/cloud_storage_azure/__manifest__.py
addons/cloud_storage_azure/models/__init__.py
addons/cloud_storage_azure/models/ir_attachment.py
addons/cloud_storage_azure/models/res_config_settings.py
addons/cloud_storage_azure/tests/__init__.py
addons/cloud_storage_azure/tests/test_cloud_storage_azure.py
addons/cloud_storage_azure/tests/test_cloud_storage_azure_attachment_controller.py
addons/cloud_storage_azure/utils/__init__.py
addons/cloud_storage_azure/utils/cleanup_cloud_storage_azure.py
addons/cloud_storage_azure/utils/cloud_storage_azure_utils.py
addons/cloud_storage_google/__init__.py
addons/cloud_storage_google/__manifest__.py
addons/cloud_storage_google/models/__init__.py
addons/cloud_storage_google/models/ir_attachment.py
addons/cloud_storage_google/models/res_config_settings.py
addons/cloud_storage_google/tests/__init__.py
addons/cloud_storage_google/tests/test_cloud_storage_google.py
addons/cloud_storage_google/tests/test_cloud_storage_google_attachment_controller.py
addons/cloud_storage_google/utils/__init__.py
addons/cloud_storage_google/utils/cleanup_cloud_storage_google.py
addons/cloud_storage_google/utils/cloud_storage_google_utils.py
addons/contacts/__init__.py
addons/contacts/__manifest__.py
addons/contacts/models/__init__.py
addons/contacts/models/res_partner.py
addons/contacts/models/res_users.py
addons/contacts/tests/__init__.py
addons/contacts/tests/test_ui.py
addons/crm/__init__.py
addons/crm/__manifest__.py
addons/crm/controllers/__init__.py
addons/crm/controllers/main.py
addons/crm/models/__init__.py
addons/crm/models/calendar.py
addons/crm/models/crm_lead.py
addons/crm/models/crm_lead_scoring_frequency.py
addons/crm/models/crm_lost_reason.py
addons/crm/models/crm_recurring_plan.py
addons/crm/models/crm_stage.py
addons/crm/models/crm_team.py
addons/crm/models/crm_team_member.py
addons/crm/models/digest.py
addons/crm/models/ir_config_parameter.py
addons/crm/models/mail_activity.py
addons/crm/models/res_config_settings.py
addons/crm/models/res_partner.py
addons/crm/models/res_users.py
addons/crm/models/utm.py
addons/crm/report/__init__.py
addons/crm/report/crm_activity_report.py
addons/crm/tests/__init__.py
addons/crm/tests/common.py
addons/crm/tests/test_crm_activity.py
addons/crm/tests/test_crm_lead.py
addons/crm/tests/test_crm_lead_assignment.py
addons/crm/tests/test_crm_lead_convert.py
addons/crm/tests/test_crm_lead_convert_mass.py
addons/crm/tests/test_crm_lead_duplicates.py
addons/crm/tests/test_crm_lead_merge.py
addons/crm/tests/test_crm_lead_multicompany.py
addons/crm/tests/test_crm_lead_notification.py
addons/crm/tests/test_crm_lead_smart_calendar.py
addons/crm/tests/test_crm_pls.py
addons/crm/tests/test_crm_rainbowman.py
addons/crm/tests/test_crm_ui.py
addons/crm/tests/test_digest.py
addons/crm/tests/test_performances.py
addons/crm/tests/test_res_partner.py
addons/crm/tests/test_sales_team_ui.py
addons/crm/wizard/__init__.py
addons/crm/wizard/crm_lead_lost.py
addons/crm/wizard/crm_lead_pls_update.py
addons/crm/wizard/crm_lead_to_opportunity.py
addons/crm/wizard/crm_lead_to_opportunity_mass.py
addons/crm/wizard/crm_merge_opportunities.py
addons/crm_iap_enrich/__init__.py
addons/crm_iap_enrich/__manifest__.py
addons/crm_iap_enrich/models/__init__.py
addons/crm_iap_enrich/models/crm_lead.py
addons/crm_iap_enrich/models/res_config_settings.py
addons/crm_iap_enrich/tests/__init__.py
addons/crm_iap_enrich/tests/test_crm_lead_merge.py
addons/crm_iap_enrich/tests/test_lead_enrich.py
addons/crm_iap_mine/__init__.py
addons/crm_iap_mine/__manifest__.py
addons/crm_iap_mine/models/__init__.py
addons/crm_iap_mine/models/crm_iap_lead_helpers.py
addons/crm_iap_mine/models/crm_iap_lead_industry.py
addons/crm_iap_mine/models/crm_iap_lead_mining_request.py
addons/crm_iap_mine/models/crm_iap_lead_role.py
addons/crm_iap_mine/models/crm_iap_lead_seniority.py
addons/crm_iap_mine/models/crm_lead.py
addons/crm_iap_mine/tests/__init__.py
addons/crm_iap_mine/tests/common.py
addons/crm_iap_mine/tests/test_lead_mine.py
addons/crm_livechat/__init__.py
addons/crm_livechat/__manifest__.py
addons/crm_livechat/models/__init__.py
addons/crm_livechat/models/chatbot_script.py
addons/crm_livechat/models/chatbot_script_step.py
addons/crm_livechat/models/crm_lead.py
addons/crm_livechat/models/discuss_channel.py
addons/crm_livechat/models/res_users.py
addons/crm_livechat/report/__init__.py
addons/crm_livechat/report/im_livechat_report_channel.py
addons/crm_livechat/tests/__init__.py
addons/crm_livechat/tests/chatbot_common.py
addons/crm_livechat/tests/test_chatbot_lead.py
addons/crm_livechat/tests/test_crm_lead.py
addons/crm_livechat/tests/test_discuss_channel_access.py
addons/crm_mail_plugin/__init__.py
addons/crm_mail_plugin/__manifest__.py
addons/crm_mail_plugin/controllers/__init__.py
addons/crm_mail_plugin/controllers/crm_client.py
addons/crm_mail_plugin/controllers/mail_plugin.py
addons/crm_mail_plugin/models/__init__.py
addons/crm_mail_plugin/models/crm_lead.py
addons/crm_mail_plugin/tests/__init__.py
addons/crm_mail_plugin/tests/test_crm_mail_plugin.py
addons/crm_sms/__init__.py
addons/crm_sms/__manifest__.py
addons/crm_sms/tests/__init__.py
addons/crm_sms/tests/test_crm_lead.py
addons/data_recycle/__init__.py
addons/data_recycle/__manifest__.py
addons/data_recycle/models/__init__.py
addons/data_recycle/models/data_recycle_model.py
addons/data_recycle/models/data_recycle_record.py
addons/data_recycle/tests/__init__.py
addons/data_recycle/tests/test_data_recycle.py
addons/delivery/__init__.py
addons/delivery/__manifest__.py
addons/delivery/const.py
addons/delivery/controllers/__init__.py
addons/delivery/controllers/location_selector.py
addons/delivery/models/__init__.py
addons/delivery/models/delivery_carrier.py
addons/delivery/models/delivery_price_rule.py
addons/delivery/models/delivery_zip_prefix.py
addons/delivery/models/ir_http.py
addons/delivery/models/ir_module_module.py
addons/delivery/models/payment_provider.py
addons/delivery/models/payment_transaction.py
addons/delivery/models/product_category.py
addons/delivery/models/res_partner.py
addons/delivery/models/sale_order.py
addons/delivery/models/sale_order_line.py
addons/delivery/tests/__init__.py
addons/delivery/tests/cash_on_delivery_common.py
addons/delivery/tests/common.py
addons/delivery/tests/test_delivery_availability.py
addons/delivery/tests/test_delivery_cost.py
addons/delivery/tests/test_payment_provider.py
addons/delivery/tests/test_payment_transaction.py
addons/delivery/tests/test_sale_order.py
addons/delivery/wizard/__init__.py
addons/delivery/wizard/choose_delivery_carrier.py
addons/delivery_mondialrelay/__init__.py
addons/delivery_mondialrelay/__manifest__.py
addons/delivery_mondialrelay/models/__init__.py
addons/delivery_mondialrelay/models/delivery_carrier.py
addons/delivery_mondialrelay/models/res_partner.py
addons/delivery_mondialrelay/models/sale_order.py
addons/delivery_mondialrelay/wizard/__init__.py
addons/delivery_mondialrelay/wizard/choose_delivery_carrier.py
addons/delivery_stock_picking_batch/__init__.py
addons/delivery_stock_picking_batch/__manifest__.py
addons/delivery_stock_picking_batch/models/__init__.py
addons/delivery_stock_picking_batch/models/stock_picking.py
addons/delivery_stock_picking_batch/models/stock_picking_batch.py
addons/delivery_stock_picking_batch/tests/__init__.py
addons/delivery_stock_picking_batch/tests/test_delivery_picking_batch.py
addons/digest/__init__.py
addons/digest/__manifest__.py
addons/digest/controllers/__init__.py
addons/digest/controllers/portal.py
addons/digest/models/__init__.py
addons/digest/models/digest.py
addons/digest/models/digest_tip.py
addons/digest/models/res_config_settings.py
addons/digest/models/res_users.py
addons/digest/tests/__init__.py
addons/digest/tests/common.py
addons/digest/tests/test_digest.py
addons/event/__init__.py
addons/event/__manifest__.py
addons/event/controllers/__init__.py
addons/event/controllers/main.py
addons/event/models/__init__.py
addons/event/models/event_event.py
addons/event/models/event_mail.py
addons/event/models/event_mail_registration.py
addons/event/models/event_mail_slot.py
addons/event/models/event_question.py
addons/event/models/event_question_answer.py
addons/event/models/event_registration.py
addons/event/models/event_registration_answer.py
addons/event/models/event_slot.py
addons/event/models/event_stage.py
addons/event/models/event_tag.py
addons/event/models/event_ticket.py
addons/event/models/event_type.py
addons/event/models/event_type_mail.py
addons/event/models/event_type_ticket.py
addons/event/models/mail_template.py
addons/event/models/res_config_settings.py
addons/event/models/res_partner.py
addons/event/report/__init__.py
addons/event/tests/__init__.py
addons/event/tests/common.py
addons/event/tests/test_event_internals.py
addons/event/tests/test_event_mail_schedule.py
addons/event/tests/test_event_slot.py
addons/event/tests/test_mailing.py
addons/event_booth/__init__.py
addons/event_booth/__manifest__.py
addons/event_booth/models/__init__.py
addons/event_booth/models/event_booth.py
addons/event_booth/models/event_booth_category.py
addons/event_booth/models/event_event.py
addons/event_booth/models/event_type.py
addons/event_booth/models/event_type_booth.py
addons/event_booth/tests/__init__.py
addons/event_booth/tests/common.py
addons/event_booth/tests/test_event_booth_internals.py
addons/event_booth/tests/test_event_internals.py
addons/event_booth_sale/__init__.py
addons/event_booth_sale/__manifest__.py
addons/event_booth_sale/models/__init__.py
addons/event_booth_sale/models/account_move.py
addons/event_booth_sale/models/event_booth.py
addons/event_booth_sale/models/event_booth_category.py
addons/event_booth_sale/models/event_booth_registration.py
addons/event_booth_sale/models/event_type_booth.py
addons/event_booth_sale/models/product_product.py
addons/event_booth_sale/models/product_template.py
addons/event_booth_sale/models/sale_order.py
addons/event_booth_sale/models/sale_order_line.py
addons/event_booth_sale/tests/__init__.py
addons/event_booth_sale/tests/common.py
addons/event_booth_sale/tests/test_event_booth_sale.py
addons/event_booth_sale/tests/test_event_internals.py
addons/event_booth_sale/wizard/__init__.py
addons/event_booth_sale/wizard/event_booth_configurator.py
addons/event_crm/__init__.py
addons/event_crm/__manifest__.py
addons/event_crm/models/__init__.py
addons/event_crm/models/crm_lead.py
addons/event_crm/models/event_event.py
addons/event_crm/models/event_lead_request.py
addons/event_crm/models/event_lead_rule.py
addons/event_crm/models/event_question_answer.py
addons/event_crm/models/event_registration.py
addons/event_crm/tests/__init__.py
addons/event_crm/tests/common.py
addons/event_crm/tests/test_crm_lead_merge.py
addons/event_crm/tests/test_event_crm_flow.py
addons/event_crm/tests/test_event_crm_http.py
addons/event_crm_sale/__init__.py
addons/event_crm_sale/__manifest__.py
addons/event_crm_sale/models/__init__.py
addons/event_crm_sale/models/event_registration.py
addons/event_product/__init__.py
addons/event_product/__manifest__.py
addons/event_product/models/__init__.py
addons/event_product/models/event_event.py
addons/event_product/models/event_event_ticket.py
addons/event_product/models/event_registration.py
addons/event_product/models/event_type_ticket.py
addons/event_product/models/product_product.py
addons/event_product/models/product_template.py
addons/event_product/tests/__init__.py
addons/event_product/tests/common.py
addons/event_sale/__init__.py
addons/event_sale/__manifest__.py
addons/event_sale/models/__init__.py
addons/event_sale/models/event_event.py
addons/event_sale/models/event_registration.py
addons/event_sale/models/event_ticket.py
addons/event_sale/models/product_template.py
addons/event_sale/models/sale_order.py
addons/event_sale/models/sale_order_line.py
addons/event_sale/report/__init__.py
addons/event_sale/report/event_sale_report.py
addons/event_sale/tests/__init__.py
addons/event_sale/tests/common.py
addons/event_sale/tests/test_event_internals.py
addons/event_sale/tests/test_event_sale.py
addons/event_sale/tests/test_event_sale_ui.py
addons/event_sale/tests/test_event_specific.py
addons/event_sale/wizard/__init__.py
addons/event_sale/wizard/event_configurator.py
addons/event_sale/wizard/event_edit_registration.py
addons/event_sms/__init__.py
addons/event_sms/__manifest__.py
addons/event_sms/models/__init__.py
addons/event_sms/models/event_mail.py
addons/event_sms/models/event_mail_registration.py
addons/event_sms/models/event_type_mail.py
addons/event_sms/models/sms_template.py
addons/event_sms/tests/__init__.py
addons/event_sms/tests/test_sms_schedule.py
addons/fleet/__init__.py
addons/fleet/__manifest__.py
addons/fleet/models/__init__.py
addons/fleet/models/fleet_service_type.py
addons/fleet/models/fleet_vehicle.py
addons/fleet/models/fleet_vehicle_assignation_log.py
addons/fleet/models/fleet_vehicle_log_contract.py
addons/fleet/models/fleet_vehicle_log_services.py
addons/fleet/models/fleet_vehicle_model.py
addons/fleet/models/fleet_vehicle_model_brand.py
addons/fleet/models/fleet_vehicle_model_category.py
addons/fleet/models/fleet_vehicle_odometer.py
addons/fleet/models/fleet_vehicle_state.py
addons/fleet/models/fleet_vehicle_tag.py
addons/fleet/models/mail_activity_type.py
addons/fleet/models/res_config_settings.py
addons/fleet/report/__init__.py
addons/fleet/report/fleet_report.py
addons/fleet/report/odometer_report.py
addons/fleet/tests/__init__.py
addons/fleet/tests/test_access_rights.py
addons/fleet/tests/test_overdue.py
addons/fleet/wizard/__init__.py
addons/fleet/wizard/fleet_vehicle_send_mail.py
addons/gamification/__init__.py
addons/gamification/__manifest__.py
addons/gamification/models/__init__.py
addons/gamification/models/gamification_badge.py
addons/gamification/models/gamification_badge_user.py
addons/gamification/models/gamification_challenge.py
addons/gamification/models/gamification_challenge_line.py
addons/gamification/models/gamification_goal.py
addons/gamification/models/gamification_goal_definition.py
addons/gamification/models/gamification_karma_rank.py
addons/gamification/models/gamification_karma_tracking.py
addons/gamification/models/res_users.py
addons/gamification/tests/__init__.py
addons/gamification/tests/common.py
addons/gamification/tests/test_challenge.py
addons/gamification/tests/test_karma_tracking.py
addons/gamification/wizard/__init__.py
addons/gamification/wizard/grant_badge.py
addons/gamification/wizard/update_goal.py
addons/gamification_sale_crm/__init__.py
addons/gamification_sale_crm/__manifest__.py
addons/google_account/__init__.py
addons/google_account/__manifest__.py
addons/google_account/controllers/__init__.py
addons/google_account/controllers/main.py
addons/google_account/models/__init__.py
addons/google_account/models/google_service.py
addons/google_address_autocomplete/__init__.py
addons/google_address_autocomplete/__manifest__.py
addons/google_address_autocomplete/controllers/__init__.py
addons/google_address_autocomplete/controllers/google_address_autocomplete.py
addons/google_address_autocomplete/models/__init__.py
addons/google_address_autocomplete/models/res_config_settings.py
addons/google_address_autocomplete/tests/__init__.py
addons/google_address_autocomplete/tests/mock_google_places.py
addons/google_address_autocomplete/tests/test_ui.py
addons/google_calendar/__init__.py
addons/google_calendar/__manifest__.py
addons/google_calendar/controllers/__init__.py
addons/google_calendar/controllers/main.py
addons/google_calendar/models/__init__.py
addons/google_calendar/models/calendar.py
addons/google_calendar/models/calendar_alarm_manager.py
addons/google_calendar/models/calendar_attendee.py
addons/google_calendar/models/calendar_recurrence_rule.py
addons/google_calendar/models/google_sync.py
addons/google_calendar/models/res_config_settings.py
addons/google_calendar/models/res_users.py
addons/google_calendar/models/res_users_settings.py
addons/google_calendar/tests/__init__.py
addons/google_calendar/tests/test_google_event.py
addons/google_calendar/tests/test_sync_common.py
addons/google_calendar/tests/test_sync_google2odoo.py
addons/google_calendar/tests/test_sync_odoo2google.py
addons/google_calendar/tests/test_sync_odoo2google_mail.py
addons/google_calendar/tests/test_token_access.py
addons/google_calendar/utils/__init__.py
addons/google_calendar/utils/google_calendar.py
addons/google_calendar/utils/google_event.py
addons/google_calendar/wizard/__init__.py
addons/google_calendar/wizard/reset_account.py
addons/google_gmail/__init__.py
addons/google_gmail/__manifest__.py
addons/google_gmail/controllers/__init__.py
addons/google_gmail/controllers/main.py
addons/google_gmail/models/__init__.py
addons/google_gmail/models/fetchmail_server.py
addons/google_gmail/models/google_gmail_mixin.py
addons/google_gmail/models/ir_mail_server.py
addons/google_gmail/models/res_config_settings.py
addons/google_gmail/models/res_users.py
addons/google_gmail/tests/__init__.py
addons/google_gmail/tests/test_google_gmail.py
addons/google_recaptcha/__init__.py
addons/google_recaptcha/__manifest__.py
addons/google_recaptcha/models/__init__.py
addons/google_recaptcha/models/ir_http.py
addons/google_recaptcha/models/res_config_settings.py
addons/hr/__init__.py
addons/hr/__manifest__.py
addons/hr/models/__init__.py
addons/hr/models/discuss_channel.py
addons/hr/models/hr_contract_type.py
addons/hr/models/hr_department.py
addons/hr/models/hr_departure_reason.py
addons/hr/models/hr_employee.py
addons/hr/models/hr_employee_category.py
addons/hr/models/hr_employee_public.py
addons/hr/models/hr_job.py
addons/hr/models/hr_mixin.py
addons/hr/models/hr_payroll_structure_type.py
addons/hr/models/hr_version.py
addons/hr/models/hr_work_location.py
addons/hr/models/ir_ui_menu.py
addons/hr/models/mail_activity_plan.py
addons/hr/models/mail_activity_plan_template.py
addons/hr/models/mail_alias.py
addons/hr/models/models.py
addons/hr/models/res_company.py
addons/hr/models/res_config_settings.py
addons/hr/models/res_partner.py
addons/hr/models/res_partner_bank.py
addons/hr/models/res_users.py
addons/hr/models/resource.py
addons/hr/models/resource_calendar.py
addons/hr/models/resource_calendar_leaves.py
addons/hr/report/__init__.py
addons/hr/report/hr_manager_department_report.py
addons/hr/tests/__init__.py
addons/hr/tests/common.py
addons/hr/tests/test_attendances.py
addons/hr/tests/test_calendar_sync.py
addons/hr/tests/test_channel.py
addons/hr/tests/test_flexible_resource_calendar.py
addons/hr/tests/test_hr_contract_versions.py
addons/hr/tests/test_hr_department.py
addons/hr/tests/test_hr_employee.py
addons/hr/tests/test_hr_employee_public.py
addons/hr/tests/test_hr_version.py
addons/hr/tests/test_mail_activity_plan.py
addons/hr/tests/test_mail_features.py
addons/hr/tests/test_multi_company.py
addons/hr/tests/test_multiple_bank_accounts.py
addons/hr/tests/test_payroll_fields_access.py
addons/hr/tests/test_resource.py
addons/hr/tests/test_scenario.py
addons/hr/tests/test_self_user_access.py
addons/hr/tests/test_ui.py
addons/hr/wizard/__init__.py
addons/hr/wizard/hr_bank_account_allocation_wizard_line.py
addons/hr/wizard/hr_bank_account_wizard.py
addons/hr/wizard/hr_contract_template_wizard.py
addons/hr/wizard/hr_departure_wizard.py
addons/hr/wizard/mail_activity_schedule.py
addons/hr_attendance/__init__.py
addons/hr_attendance/__manifest__.py
addons/hr_attendance/controllers/__init__.py
addons/hr_attendance/controllers/main.py
addons/hr_attendance/models/__init__.py
addons/hr_attendance/models/hr_attendance.py
addons/hr_attendance/models/hr_attendance_overtime.py
addons/hr_attendance/models/hr_attendance_overtime_rule.py
addons/hr_attendance/models/hr_attendance_overtime_ruleset.py
addons/hr_attendance/models/hr_employee.py
addons/hr_attendance/models/hr_employee_public.py
addons/hr_attendance/models/hr_version.py
addons/hr_attendance/models/ir_http.py
addons/hr_attendance/models/res_company.py
addons/hr_attendance/models/res_config_settings.py
addons/hr_attendance/models/res_users.py
addons/hr_attendance/tests/__init__.py
addons/hr_attendance/tests/test_hr_attendance_constraints.py
addons/hr_attendance/tests/test_hr_attendance_domain_translation.py
addons/hr_attendance/tests/test_hr_attendance_kiosk.py
addons/hr_attendance/tests/test_hr_attendance_overtime.py
addons/hr_attendance/tests/test_hr_attendance_process.py
addons/hr_attendance/tests/test_hr_attendance_rulesets.py
addons/hr_attendance/tests/test_load_scenario.py
addons/hr_calendar/__init__.py
addons/hr_calendar/__manifest__.py
addons/hr_calendar/models/__init__.py
addons/hr_calendar/models/calendar_event.py
addons/hr_calendar/models/res_partner.py
addons/hr_calendar/tests/__init__.py
addons/hr_calendar/tests/common.py
addons/hr_calendar/tests/test_event_interval.py
addons/hr_calendar/tests/test_working_hours.py
addons/hr_expense/__init__.py
addons/hr_expense/__manifest__.py
addons/hr_expense/controllers/__init__.py
addons/hr_expense/controllers/webmanifest.py
addons/hr_expense/models/__init__.py
addons/hr_expense/models/account_move.py
addons/hr_expense/models/account_move_line.py
addons/hr_expense/models/account_payment.py
addons/hr_expense/models/account_tax.py
addons/hr_expense/models/analytic.py
addons/hr_expense/models/hr_department.py
addons/hr_expense/models/hr_employee.py
addons/hr_expense/models/hr_employee_public.py
addons/hr_expense/models/hr_expense.py
addons/hr_expense/models/ir_actions_report.py
addons/hr_expense/models/product_product.py
addons/hr_expense/models/product_template.py
addons/hr_expense/models/res_company.py
addons/hr_expense/models/res_config_settings.py
addons/hr_expense/tests/__init__.py
addons/hr_expense/tests/common.py
addons/hr_expense/tests/test_expenses.py
addons/hr_expense/tests/test_expenses_access_rights.py
addons/hr_expense/tests/test_expenses_mail_import.py
addons/hr_expense/tests/test_expenses_states.py
addons/hr_expense/tests/test_expenses_tour.py
addons/hr_expense/tests/test_ui.py
addons/hr_expense/wizard/__init__.py
addons/hr_expense/wizard/account_payment_register.py
addons/hr_expense/wizard/hr_expense_approve_duplicate.py
addons/hr_expense/wizard/hr_expense_post_wizard.py
addons/hr_expense/wizard/hr_expense_refuse_reason.py
addons/hr_expense/wizard/hr_expense_split.py
addons/hr_expense/wizard/hr_expense_split_wizard.py
addons/hr_fleet/__init__.py
addons/hr_fleet/__manifest__.py
addons/hr_fleet/models/__init__.py
addons/hr_fleet/models/employee.py
addons/hr_fleet/models/fleet_vehicle.py
addons/hr_fleet/models/fleet_vehicle_assignation_log.py
addons/hr_fleet/models/fleet_vehicle_log_contract.py
addons/hr_fleet/models/fleet_vehicle_log_services.py
addons/hr_fleet/models/fleet_vehicle_odometer.py
addons/hr_fleet/models/ir_attachment.py
addons/hr_fleet/models/mail_activity_plan_template.py
addons/hr_fleet/tests/__init__.py
addons/hr_fleet/tests/test_hr_fleet_driver.py
addons/hr_fleet/tests/test_mail_activity_plan.py
addons/hr_fleet/wizard/__init__.py
addons/hr_fleet/wizard/hr_departure_wizard.py
addons/hr_gamification/__init__.py
addons/hr_gamification/__manifest__.py
addons/hr_gamification/models/__init__.py
addons/hr_gamification/models/gamification.py
addons/hr_gamification/models/hr_employee.py
addons/hr_gamification/models/hr_employee_public.py
addons/hr_gamification/models/res_users.py
addons/hr_gamification/tests/__init__.py
addons/hr_gamification/tests/test_gamification_current_badge.py
addons/hr_gamification/wizard/__init__.py
addons/hr_gamification/wizard/gamification_badge_user_wizard.py
addons/hr_holidays/__init__.py
addons/hr_holidays/__manifest__.py
addons/hr_holidays/controllers/__init__.py
addons/hr_holidays/controllers/main.py
addons/hr_holidays/models/__init__.py
addons/hr_holidays/models/calendar_event.py
addons/hr_holidays/models/hr_department.py
addons/hr_holidays/models/hr_employee.py
addons/hr_holidays/models/hr_employee_public.py
addons/hr_holidays/models/hr_leave.py
addons/hr_holidays/models/hr_leave_accrual_plan.py
addons/hr_holidays/models/hr_leave_accrual_plan_level.py
addons/hr_holidays/models/hr_leave_allocation.py
addons/hr_holidays/models/hr_leave_mandatory_day.py
addons/hr_holidays/models/hr_leave_type.py
addons/hr_holidays/models/hr_version.py
addons/hr_holidays/models/mail_activity_type.py
addons/hr_holidays/models/mail_message_subtype.py
addons/hr_holidays/models/res_partner.py
addons/hr_holidays/models/res_users.py
addons/hr_holidays/models/resource.py
addons/hr_holidays/report/__init__.py
addons/hr_holidays/report/holidays_summary_report.py
addons/hr_holidays/report/hr_leave_employee_type_report.py
addons/hr_holidays/report/hr_leave_report.py
addons/hr_holidays/report/hr_leave_report_calendar.py
addons/hr_holidays/tests/__init__.py
addons/hr_holidays/tests/common.py
addons/hr_holidays/tests/test_access_rights.py
addons/hr_holidays/tests/test_accrual_allocations.py
addons/hr_holidays/tests/test_allocation_access_rights.py
addons/hr_holidays/tests/test_allocations.py
addons/hr_holidays/tests/test_automatic_leave_dates.py
addons/hr_holidays/tests/test_change_department.py
addons/hr_holidays/tests/test_company_leave.py
addons/hr_holidays/tests/test_dashboard.py
addons/hr_holidays/tests/test_expiring_leaves.py
addons/hr_holidays/tests/test_flexible_resource_calendar.py
addons/hr_holidays/tests/test_global_leaves.py
addons/hr_holidays/tests/test_holidays_calendar.py
addons/hr_holidays/tests/test_holidays_flow.py
addons/hr_holidays/tests/test_holidays_mail.py
addons/hr_holidays/tests/test_hr_departure_wizard.py
addons/hr_holidays/tests/test_hr_holidays_cancel_leave.py
addons/hr_holidays/tests/test_hr_holidays_tour.py
addons/hr_holidays/tests/test_hr_leave_type.py
addons/hr_holidays/tests/test_hr_leave_type_tour.py
addons/hr_holidays/tests/test_leave_requests.py
addons/hr_holidays/tests/test_leave_type_data.py
addons/hr_holidays/tests/test_mandatory_days.py
addons/hr_holidays/tests/test_multi_contract.py
addons/hr_holidays/tests/test_multicompany.py
addons/hr_holidays/tests/test_negative.py
addons/hr_holidays/tests/test_out_of_office.py
addons/hr_holidays/tests/test_past_accruals.py
addons/hr_holidays/tests/test_res_partner.py
addons/hr_holidays/tests/test_time_off_card_tour.py
addons/hr_holidays/tests/test_time_off_graph_view_tour.py
addons/hr_holidays/tests/test_timeoff_event.py
addons/hr_holidays/tests/test_uninstall.py
addons/hr_holidays/tests/test_working_hours.py
addons/hr_holidays/wizard/__init__.py
addons/hr_holidays/wizard/hr_departure_wizard.py
addons/hr_holidays/wizard/hr_holidays_cancel_leave.py
addons/hr_holidays/wizard/hr_holidays_summary_employees.py
addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py
addons/hr_holidays/wizard/hr_leave_generate_multi_wizard.py
addons/hr_holidays_attendance/__init__.py
addons/hr_holidays_attendance/__manifest__.py
addons/hr_holidays_attendance/models/__init__.py
addons/hr_holidays_attendance/models/hr_attendance_overtime.py
addons/hr_holidays_attendance/models/hr_attendance_overtime_rule.py
addons/hr_holidays_attendance/models/hr_employee.py
addons/hr_holidays_attendance/models/hr_leave.py
addons/hr_holidays_attendance/models/hr_leave_accrual_plan_level.py
addons/hr_holidays_attendance/models/hr_leave_allocation.py
addons/hr_holidays_attendance/models/hr_leave_type.py
addons/hr_holidays_attendance/models/ir_ui_menu.py
addons/hr_holidays_attendance/report/__init__.py
addons/hr_holidays_attendance/report/hr_leave_attendance_report.py
addons/hr_holidays_attendance/tests/__init__.py
addons/hr_holidays_attendance/tests/test_accrual_allocations.py
addons/hr_holidays_attendance/tests/test_holidays_overtime.py
addons/hr_homeworking/__init__.py
addons/hr_homeworking/__manifest__.py
addons/hr_homeworking/models/__init__.py
addons/hr_homeworking/models/hr_employee.py
addons/hr_homeworking/models/hr_employee_public.py
addons/hr_homeworking/models/hr_homeworking.py
addons/hr_homeworking/models/hr_work_location.py
addons/hr_homeworking/models/res_partner.py
addons/hr_homeworking/models/res_users.py
addons/hr_homeworking/tests/__init__.py
addons/hr_homeworking/tests/test_hr_employee.py
addons/hr_homeworking_calendar/__init__.py
addons/hr_homeworking_calendar/__manifest__.py
addons/hr_homeworking_calendar/models/__init__.py
addons/hr_homeworking_calendar/models/hr_employee.py
addons/hr_homeworking_calendar/models/res_partner.py
addons/hr_homeworking_calendar/tests/__init__.py
addons/hr_homeworking_calendar/tests/common.py
addons/hr_homeworking_calendar/tests/test_hr_employee_location.py
addons/hr_homeworking_calendar/wizard/__init__.py
addons/hr_homeworking_calendar/wizard/homework_location_wizard.py
addons/hr_hourly_cost/__init__.py
addons/hr_hourly_cost/__manifest__.py
addons/hr_hourly_cost/models/__init__.py
addons/hr_hourly_cost/models/hr_employee.py
addons/hr_livechat/__init__.py
addons/hr_livechat/__manifest__.py
addons/hr_maintenance/__init__.py
addons/hr_maintenance/__manifest__.py
addons/hr_maintenance/models/__init__.py
addons/hr_maintenance/models/equipment.py
addons/hr_maintenance/models/hr_employee.py
addons/hr_maintenance/models/hr_employee_public.py
addons/hr_maintenance/wizard/__init__.py
addons/hr_maintenance/wizard/hr_departure_wizard.py
addons/hr_org_chart/__init__.py
addons/hr_org_chart/__manifest__.py
addons/hr_org_chart/controllers/__init__.py
addons/hr_org_chart/controllers/hr_org_chart.py
addons/hr_org_chart/models/__init__.py
addons/hr_org_chart/models/hr_employee.py
addons/hr_org_chart/models/hr_org_chart_mixin.py
addons/hr_org_chart/tests/__init__.py
addons/hr_org_chart/tests/test_employee.py
addons/hr_org_chart/tests/test_employee_deletion.py
addons/hr_org_chart/tests/test_employee_ui.py
addons/hr_presence/__init__.py
addons/hr_presence/__manifest__.py
addons/hr_presence/models/__init__.py
addons/hr_presence/models/hr_employee.py
addons/hr_presence/models/hr_employee_public.py
addons/hr_presence/models/ir_websocket.py
addons/hr_presence/models/res_company.py
addons/hr_presence/models/res_config_settings.py
addons/hr_presence/models/res_users_log.py
addons/hr_recruitment/__init__.py
addons/hr_recruitment/__manifest__.py
addons/hr_recruitment/models/__init__.py
addons/hr_recruitment/models/calendar.py
addons/hr_recruitment/models/digest.py
addons/hr_recruitment/models/hr_applicant.py
addons/hr_recruitment/models/hr_applicant_category.py
addons/hr_recruitment/models/hr_applicant_refuse_reason.py
addons/hr_recruitment/models/hr_department.py
addons/hr_recruitment/models/hr_employee.py
addons/hr_recruitment/models/hr_job.py
addons/hr_recruitment/models/hr_job_platform.py
addons/hr_recruitment/models/hr_recruitment_degree.py
addons/hr_recruitment/models/hr_recruitment_source.py
addons/hr_recruitment/models/hr_recruitment_stage.py
addons/hr_recruitment/models/hr_talent_pool.py
addons/hr_recruitment/models/ir_attachment.py
addons/hr_recruitment/models/ir_ui_menu.py
addons/hr_recruitment/models/mail_activity_plan.py
addons/hr_recruitment/models/res_company.py
addons/hr_recruitment/models/res_config_settings.py
addons/hr_recruitment/models/res_partner.py
addons/hr_recruitment/models/res_users.py
addons/hr_recruitment/models/utm_campaign.py
addons/hr_recruitment/models/utm_source.py
addons/hr_recruitment/tests/__init__.py
addons/hr_recruitment/tests/test_recruitment.py
addons/hr_recruitment/tests/test_recruitment_allowed_user_ids.py
addons/hr_recruitment/tests/test_recruitment_interviewer.py
addons/hr_recruitment/tests/test_recruitment_process.py
addons/hr_recruitment/tests/test_recruitment_talent_pools.py
addons/hr_recruitment/tests/test_utm.py
addons/hr_recruitment/wizard/__init__.py
addons/hr_recruitment/wizard/applicant_refuse_reason.py
addons/hr_recruitment/wizard/applicant_send_mail.py
addons/hr_recruitment/wizard/job_add_applicants.py
addons/hr_recruitment/wizard/mail_activity_schedule.py
addons/hr_recruitment/wizard/talent_pool_add_applicants.py
addons/hr_recruitment_skills/__init__.py
addons/hr_recruitment_skills/__manifest__.py
addons/hr_recruitment_skills/models/__init__.py
addons/hr_recruitment_skills/models/hr_applicant.py
addons/hr_recruitment_skills/models/hr_applicant_skill.py
addons/hr_recruitment_skills/models/hr_job.py
addons/hr_recruitment_skills/tests/__init__.py
addons/hr_recruitment_skills/tests/test_applicant_skills.py
addons/hr_recruitment_skills/tests/test_recruitment_skills.py
addons/hr_recruitment_sms/__init__.py
addons/hr_recruitment_sms/__manifest__.py
addons/hr_recruitment_sms/models/__init__.py
addons/hr_recruitment_sms/models/hr_applicant.py
addons/hr_recruitment_survey/__init__.py
addons/hr_recruitment_survey/__manifest__.py
addons/hr_recruitment_survey/controllers/__init__.py
addons/hr_recruitment_survey/controllers/main.py
addons/hr_recruitment_survey/models/__init__.py
addons/hr_recruitment_survey/models/hr_applicant.py
addons/hr_recruitment_survey/models/hr_job.py
addons/hr_recruitment_survey/models/survey_survey.py
addons/hr_recruitment_survey/models/survey_user_input.py
addons/hr_recruitment_survey/tests/__init__.py
addons/hr_recruitment_survey/tests/test_recruitment_survey.py
addons/hr_recruitment_survey/wizard/__init__.py
addons/hr_recruitment_survey/wizard/survey_invite.py
addons/hr_skills/__init__.py
addons/hr_skills/__manifest__.py
addons/hr_skills/controllers/__init__.py
addons/hr_skills/controllers/main.py
addons/hr_skills/models/__init__.py
addons/hr_skills/models/hr_employee.py
addons/hr_skills/models/hr_employee_public.py
addons/hr_skills/models/hr_employee_skill.py
addons/hr_skills/models/hr_individual_skill_mixin.py
addons/hr_skills/models/hr_job.py
addons/hr_skills/models/hr_job_skill.py
addons/hr_skills/models/hr_resume_line.py
addons/hr_skills/models/hr_resume_line_type.py
addons/hr_skills/models/hr_skill.py
addons/hr_skills/models/hr_skill_level.py
addons/hr_skills/models/hr_skill_type.py
addons/hr_skills/models/resource_resource.py
addons/hr_skills/report/__init__.py
addons/hr_skills/report/hr_employee_certification_report.py
addons/hr_skills/report/hr_employee_cv_report.py
addons/hr_skills/report/hr_employee_skill_history_report.py
addons/hr_skills/report/hr_employee_skill_report.py
addons/hr_skills/tests/__init__.py
addons/hr_skills/tests/test_certification_activities.py
addons/hr_skills/tests/test_employee_skill.py
addons/hr_skills/tests/test_resource.py
addons/hr_skills/tests/test_ui.py
addons/hr_skills/wizard/__init__.py
addons/hr_skills/wizard/hr_employee_cv_wizard.py
addons/hr_skills_event/__init__.py
addons/hr_skills_event/__manifest__.py
addons/hr_skills_event/models/__init__.py
addons/hr_skills_event/models/hr_resume_line.py
addons/hr_skills_slides/__init__.py
addons/hr_skills_slides/__manifest__.py
addons/hr_skills_slides/models/__init__.py
addons/hr_skills_slides/models/hr_employee.py
addons/hr_skills_slides/models/hr_employee_public.py
addons/hr_skills_slides/models/hr_resume_line.py
addons/hr_skills_slides/models/slide_channel.py
addons/hr_skills_slides/tests/__init__.py
addons/hr_skills_slides/tests/test_hr_skills_slides.py
addons/hr_skills_survey/__init__.py
addons/hr_skills_survey/__manifest__.py
addons/hr_skills_survey/models/__init__.py
addons/hr_skills_survey/models/hr_resume_line.py
addons/hr_skills_survey/models/survey_survey.py
addons/hr_skills_survey/models/survey_user.py
addons/hr_skills_survey/tests/__init__.py
addons/hr_skills_survey/tests/test_certification_flow.py
addons/hr_timesheet/__init__.py
addons/hr_timesheet/__manifest__.py
addons/hr_timesheet/controllers/__init__.py
addons/hr_timesheet/controllers/portal.py
addons/hr_timesheet/controllers/project.py
addons/hr_timesheet/models/__init__.py
addons/hr_timesheet/models/account_analytic_line_calendar_employee.py
addons/hr_timesheet/models/analytic_applicability.py
addons/hr_timesheet/models/hr_employee.py
addons/hr_timesheet/models/hr_employee_public.py
addons/hr_timesheet/models/hr_timesheet.py
addons/hr_timesheet/models/ir_http.py
addons/hr_timesheet/models/ir_ui_menu.py
addons/hr_timesheet/models/project_collaborator.py
addons/hr_timesheet/models/project_project.py
addons/hr_timesheet/models/project_task.py
addons/hr_timesheet/models/project_update.py
addons/hr_timesheet/models/res_company.py
addons/hr_timesheet/models/res_config_settings.py
addons/hr_timesheet/models/uom_uom.py
addons/hr_timesheet/report/__init__.py
addons/hr_timesheet/report/project_report.py
addons/hr_timesheet/report/timesheets_analysis_report.py
addons/hr_timesheet/tests/__init__.py
addons/hr_timesheet/tests/test_employee_delete_wizard.py
addons/hr_timesheet/tests/test_portal_timesheet.py
addons/hr_timesheet/tests/test_project_project.py
addons/hr_timesheet/tests/test_project_task_quick_create.py
addons/hr_timesheet/tests/test_project_template.py
addons/hr_timesheet/tests/test_timesheet.py
addons/hr_timesheet/tests/test_timesheet_import_template.py
addons/hr_timesheet/wizard/__init__.py
addons/hr_timesheet/wizard/hr_employee_delete_wizard.py
addons/hr_timesheet_attendance/__init__.py
addons/hr_timesheet_attendance/__manifest__.py
addons/hr_timesheet_attendance/models/__init__.py
addons/hr_timesheet_attendance/models/ir_ui_menu.py
addons/hr_timesheet_attendance/report/__init__.py
addons/hr_timesheet_attendance/report/hr_timesheet_attendance_report.py
addons/hr_timesheet_attendance/tests/__init__.py
addons/hr_timesheet_attendance/tests/test_timesheet_attendance.py
addons/hr_work_entry/__init__.py
addons/hr_work_entry/__manifest__.py
addons/hr_work_entry/models/__init__.py
addons/hr_work_entry/models/hr_employee.py
addons/hr_work_entry/models/hr_user_work_entry_employee.py
addons/hr_work_entry/models/hr_version.py
addons/hr_work_entry/models/hr_work_entry.py
addons/hr_work_entry/models/hr_work_entry_type.py
addons/hr_work_entry/models/resource_calendar.py
addons/hr_work_entry/models/resource_calendar_attendance.py
addons/hr_work_entry/models/resource_calendar_leaves.py
addons/hr_work_entry/tests/__init__.py
addons/hr_work_entry/tests/common.py
addons/hr_work_entry/tests/test_global_time_off.py
addons/hr_work_entry/tests/test_hr_work_entry.py
addons/hr_work_entry/tests/test_work_entry.py
addons/hr_work_entry/tests/test_work_entry_type_data.py
addons/hr_work_entry/wizard/__init__.py
addons/hr_work_entry/wizard/hr_work_entry_regeneration_wizard.py
addons/hr_work_entry_holidays/__init__.py
addons/hr_work_entry_holidays/__manifest__.py
addons/hr_work_entry_holidays/models/__init__.py
addons/hr_work_entry_holidays/models/hr_leave.py
addons/hr_work_entry_holidays/models/hr_version.py
addons/hr_work_entry_holidays/models/hr_work_entry.py
addons/hr_work_entry_holidays/tests/__init__.py
addons/hr_work_entry_holidays/tests/common.py
addons/hr_work_entry_holidays/tests/test_leave.py
addons/hr_work_entry_holidays/tests/test_multi_contract.py
addons/hr_work_entry_holidays/tests/test_payslip_holidays_computation.py
addons/hr_work_entry_holidays/tests/test_performance.py
addons/hr_work_entry_holidays/tests/test_work_entry.py
addons/html_builder/__init__.py
addons/html_builder/__manifest__.py
addons/html_builder/tests/__init__.py
addons/html_builder/tests/test_html_builder_assets_bundle.py
addons/html_editor/__init__.py
addons/html_editor/__manifest__.py
addons/html_editor/tools.py
addons/html_editor/controllers/__init__.py
addons/html_editor/controllers/main.py
addons/html_editor/models/__init__.py
addons/html_editor/models/diff_utils.py
addons/html_editor/models/html_field_history_mixin.py
addons/html_editor/models/ir_attachment.py
addons/html_editor/models/ir_http.py
addons/html_editor/models/ir_qweb_fields.py
addons/html_editor/models/ir_ui_view.py
addons/html_editor/models/ir_websocket.py
addons/html_editor/models/models.py
addons/html_editor/models/test_models.py
addons/html_editor/tests/__init__.py
addons/html_editor/tests/test_controller.py
addons/html_editor/tests/test_converter.py
addons/html_editor/tests/test_diff_utils.py
addons/html_editor/tests/test_tools.py
addons/html_editor/tests/test_views.py
addons/http_routing/__init__.py
addons/http_routing/__manifest__.py
addons/http_routing/controllers/__init__.py
addons/http_routing/controllers/main.py
addons/http_routing/models/__init__.py
addons/http_routing/models/ir_http.py
addons/http_routing/models/ir_qweb.py
addons/http_routing/models/res_lang.py
addons/http_routing/tests/__init__.py
addons/http_routing/tests/common.py
addons/http_routing/tests/test_res_lang.py
addons/iap/__init__.py
addons/iap/__manifest__.py
addons/iap/models/__init__.py
addons/iap/models/iap_account.py
addons/iap/models/iap_enrich_api.py
addons/iap/models/iap_service.py
addons/iap/tests/__init__.py
addons/iap/tests/common.py
addons/iap/tests/test_iap.py
addons/iap/tools/__init__.py
addons/iap/tools/iap_tools.py
addons/iap_crm/__init__.py
addons/iap_crm/__manifest__.py
addons/iap_crm/models/__init__.py
addons/iap_crm/models/crm_lead.py
addons/iap_mail/__init__.py
addons/iap_mail/__manifest__.py
addons/iap_mail/models/__init__.py
addons/iap_mail/models/iap_account.py
addons/im_livechat/__init__.py
addons/im_livechat/__manifest__.py
addons/im_livechat/controllers/__init__.py
addons/im_livechat/controllers/attachment.py
addons/im_livechat/controllers/channel.py
addons/im_livechat/controllers/chatbot.py
addons/im_livechat/controllers/main.py
addons/im_livechat/controllers/thread.py
addons/im_livechat/controllers/webclient.py
addons/im_livechat/controllers/cors/__init__.py
addons/im_livechat/controllers/cors/attachment.py
addons/im_livechat/controllers/cors/channel.py
addons/im_livechat/controllers/cors/chatbot.py
addons/im_livechat/controllers/cors/link_preview.py
addons/im_livechat/controllers/cors/main.py
addons/im_livechat/controllers/cors/message_reaction.py
addons/im_livechat/controllers/cors/rtc.py
addons/im_livechat/controllers/cors/thread.py
addons/im_livechat/controllers/cors/webclient.py
addons/im_livechat/models/__init__.py
addons/im_livechat/models/chatbot_message.py
addons/im_livechat/models/chatbot_script.py
addons/im_livechat/models/chatbot_script_answer.py
addons/im_livechat/models/chatbot_script_step.py
addons/im_livechat/models/digest.py
addons/im_livechat/models/discuss_call_history.py
addons/im_livechat/models/discuss_channel.py
addons/im_livechat/models/discuss_channel_member.py
addons/im_livechat/models/discuss_channel_rtc_session.py
addons/im_livechat/models/im_livechat_channel.py
addons/im_livechat/models/im_livechat_channel_member_history.py
addons/im_livechat/models/im_livechat_conversation_tag.py
addons/im_livechat/models/im_livechat_expertise.py
addons/im_livechat/models/ir_binary.py
addons/im_livechat/models/ir_websocket.py
addons/im_livechat/models/mail_message.py
addons/im_livechat/models/rating_rating.py
addons/im_livechat/models/res_groups.py
addons/im_livechat/models/res_partner.py
addons/im_livechat/models/res_users.py
addons/im_livechat/models/res_users_settings.py
addons/im_livechat/report/__init__.py
addons/im_livechat/report/im_livechat_report_channel.py
addons/im_livechat/tests/__init__.py
addons/im_livechat/tests/chatbot_common.py
addons/im_livechat/tests/common.py
addons/im_livechat/tests/test_chatbot_form_ui.py
addons/im_livechat/tests/test_chatbot_internals.py
addons/im_livechat/tests/test_cors_livechat.py
addons/im_livechat/tests/test_digest.py
addons/im_livechat/tests/test_discuss_channel.py
addons/im_livechat/tests/test_get_discuss_channel.py
addons/im_livechat/tests/test_get_operator.py
addons/im_livechat/tests/test_im_livechat_calls.py
addons/im_livechat/tests/test_im_livechat_channel.py
addons/im_livechat/tests/test_im_livechat_report.py
addons/im_livechat/tests/test_im_livechat_support_page.py
addons/im_livechat/tests/test_js.py
addons/im_livechat/tests/test_member_history.py
addons/im_livechat/tests/test_message.py
addons/im_livechat/tests/test_res_users.py
addons/im_livechat/tests/test_session_views.py
addons/im_livechat/tests/test_transcript.py
addons/im_livechat/tests/test_upload_attachment.py
addons/im_livechat/tests/test_user_livechat_username.py
addons/im_livechat/tools/__init__.py
addons/im_livechat/tools/misc.py
addons/iot_base/__init__.py
addons/iot_base/__manifest__.py
addons/iot_box_image/__init__.py
addons/iot_box_image/__manifest__.py
addons/iot_drivers/__init__.py
addons/iot_drivers/__manifest__.py
addons/iot_drivers/browser.py
addons/iot_drivers/connection_manager.py
addons/iot_drivers/driver.py
addons/iot_drivers/event_manager.py
addons/iot_drivers/exception_logger.py
addons/iot_drivers/http.py
addons/iot_drivers/interface.py
addons/iot_drivers/main.py
addons/iot_drivers/server_logger.py
addons/iot_drivers/webrtc_client.py
addons/iot_drivers/websocket_client.py
addons/iot_drivers/cli/genproxytoken.py
addons/iot_drivers/controllers/__init__.py
addons/iot_drivers/controllers/driver.py
addons/iot_drivers/controllers/homepage.py
addons/iot_drivers/controllers/proxy.py
addons/iot_drivers/iot_handlers/drivers/display_driver_L.py
addons/iot_drivers/iot_handlers/drivers/keyboard_usb_driver_L.py
addons/iot_drivers/iot_handlers/drivers/l10n_eg_drivers.py
addons/iot_drivers/iot_handlers/drivers/l10n_ke_edi_serial_driver.py
addons/iot_drivers/iot_handlers/drivers/printer_driver_L.py
addons/iot_drivers/iot_handlers/drivers/printer_driver_W.py
addons/iot_drivers/iot_handlers/drivers/printer_driver_base.py
addons/iot_drivers/iot_handlers/drivers/serial_base_driver.py
addons/iot_drivers/iot_handlers/drivers/serial_scale_driver.py
addons/iot_drivers/iot_handlers/interfaces/display_interface_L.py
addons/iot_drivers/iot_handlers/interfaces/printer_interface_L.py
addons/iot_drivers/iot_handlers/interfaces/printer_interface_W.py
addons/iot_drivers/iot_handlers/interfaces/serial_interface.py
addons/iot_drivers/iot_handlers/interfaces/usb_interface_L.py
addons/iot_drivers/tools/__init__.py
addons/iot_drivers/tools/certificate.py
addons/iot_drivers/tools/helpers.py
addons/iot_drivers/tools/route.py
addons/iot_drivers/tools/system.py
addons/iot_drivers/tools/upgrade.py
addons/iot_drivers/tools/wifi.py
addons/l10n_account_edi_ubl_cii_tests/__init__.py
addons/l10n_account_edi_ubl_cii_tests/__manifest__.py
addons/l10n_account_edi_ubl_cii_tests/tests/__init__.py
addons/l10n_account_edi_ubl_cii_tests/tests/common.py
addons/l10n_account_edi_ubl_cii_tests/tests/test_xml_cii_fr.py
addons/l10n_account_edi_ubl_cii_tests/tests/test_xml_cii_us.py
addons/l10n_account_edi_ubl_cii_tests/tests/test_xml_ubl_attacheddocument.py
addons/l10n_account_edi_ubl_cii_tests/tests/test_xml_ubl_au.py
addons/l10n_account_edi_ubl_cii_tests/tests/test_xml_ubl_be.py
addons/l10n_account_edi_ubl_cii_tests/tests/test_xml_ubl_de.py
addons/l10n_account_edi_ubl_cii_tests/tests/test_xml_ubl_nl.py
addons/l10n_account_withholding_tax/__init__.py
addons/l10n_account_withholding_tax/__manifest__.py
addons/l10n_account_withholding_tax/models/__init__.py
addons/l10n_account_withholding_tax/models/account_payment.py
addons/l10n_account_withholding_tax/models/account_payment_withholding_line.py
addons/l10n_account_withholding_tax/models/account_tax.py
addons/l10n_account_withholding_tax/models/account_withholding_line.py
addons/l10n_account_withholding_tax/models/product_template.py
addons/l10n_account_withholding_tax/models/res_company.py
addons/l10n_account_withholding_tax/models/res_config_settings.py
addons/l10n_account_withholding_tax/tests/__init__.py
addons/l10n_account_withholding_tax/tests/test_account_withholding_amounts.py
addons/l10n_account_withholding_tax/tests/test_account_withholding_flows.py
addons/l10n_account_withholding_tax/wizards/__init__.py
addons/l10n_account_withholding_tax/wizards/account_payment_register.py
addons/l10n_account_withholding_tax/wizards/account_payment_register_withholding_line.py
addons/l10n_account_withholding_tax_pos/__init__.py
addons/l10n_account_withholding_tax_pos/__manifest__.py
addons/l10n_account_withholding_tax_pos/models/__init__.py
addons/l10n_account_withholding_tax_pos/models/account_tax.py
addons/l10n_ae/__init__.py
addons/l10n_ae/__manifest__.py
addons/l10n_ae/models/__init__.py
addons/l10n_ae/models/account_move.py
addons/l10n_ae/models/template_ae.py
addons/l10n_ae_pos/__init__.py
addons/l10n_ae_pos/__manifest__.py
addons/l10n_anz_ubl_pint/__init__.py
addons/l10n_anz_ubl_pint/__manifest__.py
addons/l10n_anz_ubl_pint/models/__init__.py
addons/l10n_anz_ubl_pint/models/account_edi_xml_pint_anz.py
addons/l10n_anz_ubl_pint/models/res_partner.py
addons/l10n_anz_ubl_pint/tests/__init__.py
addons/l10n_anz_ubl_pint/tests/test_anz_ubl_pint.py
addons/l10n_ar/__init__.py
addons/l10n_ar/__manifest__.py
addons/l10n_ar/controllers/__init__.py
addons/l10n_ar/controllers/portal.py
addons/l10n_ar/demo/__init__.py
addons/l10n_ar/demo/account_demo.py
addons/l10n_ar/models/__init__.py
addons/l10n_ar/models/account_chart_template.py
addons/l10n_ar/models/account_fiscal_position.py
addons/l10n_ar/models/account_journal.py
addons/l10n_ar/models/account_move.py
addons/l10n_ar/models/account_move_line.py
addons/l10n_ar/models/account_tax_group.py
addons/l10n_ar/models/l10n_ar_afip_responsibility_type.py
addons/l10n_ar/models/l10n_latam_document_type.py
addons/l10n_ar/models/l10n_latam_identification_type.py
addons/l10n_ar/models/res_company.py
addons/l10n_ar/models/res_country.py
addons/l10n_ar/models/res_currency.py
addons/l10n_ar/models/res_partner.py
addons/l10n_ar/models/res_partner_bank.py
addons/l10n_ar/models/template_ar_base.py
addons/l10n_ar/models/template_ar_ex.py
addons/l10n_ar/models/template_ar_ri.py
addons/l10n_ar/models/uom_uom.py
addons/l10n_ar/report/__init__.py
addons/l10n_ar/report/invoice_report.py
addons/l10n_ar/tests/__init__.py
addons/l10n_ar/tests/common.py
addons/l10n_ar/tests/test_manual.py
addons/l10n_ar_pos/__init__.py
addons/l10n_ar_pos/__manifest__.py
addons/l10n_ar_pos/models/__init__.py
addons/l10n_ar_pos/models/l10n_ar_afip_responsibility_type.py
addons/l10n_ar_pos/models/l10n_latam_identification_type.py
addons/l10n_ar_pos/models/pos_config.py
addons/l10n_ar_pos/models/pos_session.py
addons/l10n_ar_pos/models/res_partner.py
addons/l10n_ar_pos/tests/__init__.py
addons/l10n_ar_pos/tests/test_pos_ar.py
addons/l10n_ar_stock/__init__.py
addons/l10n_ar_stock/__manifest__.py
addons/l10n_ar_stock/models/__init__.py
addons/l10n_ar_stock/models/stock_picking.py
addons/l10n_ar_stock/models/stock_picking_type.py
addons/l10n_ar_stock/tests/__init__.py
addons/l10n_ar_stock/tests/test_l10n_ar_delivery_guide.py
addons/l10n_ar_website_sale/__init__.py
addons/l10n_ar_website_sale/__manifest__.py
addons/l10n_ar_website_sale/models/__init__.py
addons/l10n_ar_website_sale/models/product_template.py
addons/l10n_ar_website_sale/models/res_config_settings.py
addons/l10n_ar_website_sale/models/website.py
addons/l10n_ar_website_sale/tests/__init__.py
addons/l10n_ar_website_sale/tests/test_l10n_ar_website_sale.py
addons/l10n_ar_withholding/__init__.py
addons/l10n_ar_withholding/__manifest__.py
addons/l10n_ar_withholding/demo/__init__.py
addons/l10n_ar_withholding/demo/account_demo.py
addons/l10n_ar_withholding/models/__init__.py
addons/l10n_ar_withholding/models/account_chart_template.py
addons/l10n_ar_withholding/models/account_move.py
addons/l10n_ar_withholding/models/account_payment.py
addons/l10n_ar_withholding/models/account_tax.py
addons/l10n_ar_withholding/models/l10n_ar_earnings_scale.py
addons/l10n_ar_withholding/models/l10n_ar_partner_tax.py
addons/l10n_ar_withholding/models/res_company.py
addons/l10n_ar_withholding/models/res_config_settings.py
addons/l10n_ar_withholding/models/res_partner.py
addons/l10n_ar_withholding/tests/__init__.py
addons/l10n_ar_withholding/tests/test_withholding_ar_ri.py
addons/l10n_ar_withholding/wizards/__init__.py
addons/l10n_ar_withholding/wizards/account_payment_register.py
addons/l10n_ar_withholding/wizards/l10n_ar_payment_register_withholding.py
addons/l10n_at/__init__.py
addons/l10n_at/__manifest__.py
addons/l10n_at/models/__init__.py
addons/l10n_at/models/account_journal.py
addons/l10n_at/models/template_at.py
addons/l10n_au/__init__.py
addons/l10n_au/__manifest__.py
addons/l10n_au/models/__init__.py
addons/l10n_au/models/account_move.py
addons/l10n_au/models/account_payment.py
addons/l10n_au/models/res_company.py
addons/l10n_au/models/res_partner.py
addons/l10n_au/models/res_partner_bank.py
addons/l10n_au/models/template_au.py
addons/l10n_bd/__init__.py
addons/l10n_bd/__manifest__.py
addons/l10n_bd/models/__init__.py
addons/l10n_bd/models/template_bd.py
addons/l10n_be/__init__.py
addons/l10n_be/__manifest__.py
addons/l10n_be/demo/__init__.py
addons/l10n_be/demo/account_demo.py
addons/l10n_be/models/__init__.py
addons/l10n_be/models/account_journal.py
addons/l10n_be/models/account_move.py
addons/l10n_be/models/account_tax.py
addons/l10n_be/models/res_partner.py
addons/l10n_be/models/template_be.py
addons/l10n_be/models/template_be_asso.py
addons/l10n_be/models/template_be_comp.py
addons/l10n_be_pos_restaurant/__init__.py
addons/l10n_be_pos_restaurant/__manifest__.py
addons/l10n_be_pos_restaurant/models/__init__.py
addons/l10n_be_pos_restaurant/models/pos_config.py
addons/l10n_be_pos_restaurant/models/template_be.py
addons/l10n_be_pos_sale/__init__.py
addons/l10n_be_pos_sale/__manifest__.py
addons/l10n_be_pos_sale/models/__init__.py
addons/l10n_be_pos_sale/models/pos_config.py
addons/l10n_be_pos_sale/tests/__init__.py
addons/l10n_be_pos_sale/tests/test_l10n_be_pos_sale.py
addons/l10n_bf/__init__.py
addons/l10n_bf/__manifest__.py
addons/l10n_bf/models/__init__.py
addons/l10n_bf/models/template_bf.py
addons/l10n_bf/models/template_bf_syscebnl.py
addons/l10n_bg/__init__.py
addons/l10n_bg/__manifest__.py
addons/l10n_bg/models/__init__.py
addons/l10n_bg/models/template_bg.py
addons/l10n_bg_ledger/__init__.py
addons/l10n_bg_ledger/__manifest__.py
addons/l10n_bg_ledger/models/__init__.py
addons/l10n_bg_ledger/models/account_journal.py
addons/l10n_bg_ledger/models/account_move.py
addons/l10n_bh/__init__.py
addons/l10n_bh/__manifest__.py
addons/l10n_bh/models/__init__.py
addons/l10n_bh/models/template_bh.py
addons/l10n_bj/__init__.py
addons/l10n_bj/__manifest__.py
addons/l10n_bj/models/__init__.py
addons/l10n_bj/models/template_bj.py
addons/l10n_bj/models/template_bj_syscebnl.py
addons/l10n_bo/__init__.py
addons/l10n_bo/__manifest__.py
addons/l10n_bo/models/__init__.py
addons/l10n_bo/models/template_bo.py
addons/l10n_br/__init__.py
addons/l10n_br/__manifest__.py
addons/l10n_br/controllers/__init__.py
addons/l10n_br/controllers/portal.py
addons/l10n_br/demo/__init__.py
addons/l10n_br/demo/account_demo.py
addons/l10n_br/models/__init__.py
addons/l10n_br/models/account.py
addons/l10n_br/models/account_fiscal_position.py
addons/l10n_br/models/account_journal.py
addons/l10n_br/models/account_move.py
addons/l10n_br/models/l10n_br_zip_range.py
addons/l10n_br/models/res_city.py
addons/l10n_br/models/res_company.py
addons/l10n_br/models/res_partner.py
addons/l10n_br/models/res_partner_bank.py
addons/l10n_br/models/template_br.py
addons/l10n_br/tests/__init__.py
addons/l10n_br/tests/test_l10n_br_pix.py
addons/l10n_br/wizard/__init__.py
addons/l10n_br/wizard/account_move_reversal.py
addons/l10n_br_sales/__init__.py
addons/l10n_br_sales/__manifest__.py
addons/l10n_br_sales/models/__init__.py
addons/l10n_br_sales/models/sale_order.py
addons/l10n_br_website_sale/__init__.py
addons/l10n_br_website_sale/__manifest__.py
addons/l10n_br_website_sale/models/__init__.py
addons/l10n_br_website_sale/models/website.py
addons/l10n_ca/__init__.py
addons/l10n_ca/__manifest__.py
addons/l10n_ca/models/__init__.py
addons/l10n_ca/models/res_company.py
addons/l10n_ca/models/res_partner.py
addons/l10n_ca/models/template_ca.py
addons/l10n_cd/__init__.py
addons/l10n_cd/__manifest__.py
addons/l10n_cd/models/__init__.py
addons/l10n_cd/models/template_cd.py
addons/l10n_cd/models/template_cd_syscebnl.py
addons/l10n_cf/__init__.py
addons/l10n_cf/__manifest__.py
addons/l10n_cf/models/__init__.py
addons/l10n_cf/models/template_cf.py
addons/l10n_cf/models/template_cf_syscebnl.py
addons/l10n_cg/__init__.py
addons/l10n_cg/__manifest__.py
addons/l10n_cg/models/__init__.py
addons/l10n_cg/models/template_cg.py
addons/l10n_cg/models/template_cg_syscebnl.py
addons/l10n_ch/__init__.py
addons/l10n_ch/__manifest__.py
addons/l10n_ch/models/__init__.py
addons/l10n_ch/models/account_invoice.py
addons/l10n_ch/models/account_journal.py
addons/l10n_ch/models/account_payment.py
addons/l10n_ch/models/ir_actions_report.py
addons/l10n_ch/models/res_bank.py
addons/l10n_ch/models/template_ch.py
addons/l10n_ch/report/__init__.py
addons/l10n_ch/report/swissqr_report.py
addons/l10n_ch/tests/__init__.py
addons/l10n_ch/tests/test_ch_qr_code.py
addons/l10n_ch/tests/test_gen_qrr_reference.py
addons/l10n_ch/tests/test_l10n_ch_qr_print.py
addons/l10n_ch/tests/test_swissqr.py
addons/l10n_ch/wizard/__init__.py
addons/l10n_ch/wizard/qr_invoice_wizard.py
addons/l10n_ch/wizard/setup_wizards.py
addons/l10n_ch_pos/__init__.py
addons/l10n_ch_pos/__manifest__.py
addons/l10n_ch_pos/models/__init__.py
addons/l10n_ch_pos/models/pos_order.py
addons/l10n_ci/__init__.py
addons/l10n_ci/__manifest__.py
addons/l10n_ci/models/__init__.py
addons/l10n_ci/models/template_ci.py
addons/l10n_ci/models/template_ci_syscebnl.py
addons/l10n_cl/__init__.py
addons/l10n_cl/__manifest__.py
addons/l10n_cl/demo/__init__.py
addons/l10n_cl/demo/account_demo.py
addons/l10n_cl/models/__init__.py
addons/l10n_cl/models/account_move.py
addons/l10n_cl/models/account_move_line.py
addons/l10n_cl/models/account_tax.py
addons/l10n_cl/models/l10n_latam_document_type.py
addons/l10n_cl/models/res_company.py
addons/l10n_cl/models/res_country.py
addons/l10n_cl/models/res_currency.py
addons/l10n_cl/models/res_partner.py
addons/l10n_cl/models/res_partner_bank.py
addons/l10n_cl/models/template_cl.py
addons/l10n_cl/models/uom_uom.py
addons/l10n_cl/tests/__init__.py
addons/l10n_cl/tests/test_latam_document_type.py
addons/l10n_cm/__init__.py
addons/l10n_cm/__manifest__.py
addons/l10n_cm/models/__init__.py
addons/l10n_cm/models/template_cm.py
addons/l10n_cm/models/template_cm_syscebnl.py
addons/l10n_cn/__init__.py
addons/l10n_cn/__manifest__.py
addons/l10n_cn/models/__init__.py
addons/l10n_cn/models/account_move.py
addons/l10n_cn/models/template_cn.py
addons/l10n_cn/models/template_cn_common.py
addons/l10n_cn/models/template_cn_large_bis.py
addons/l10n_cn_city/__init__.py
addons/l10n_cn_city/__manifest__.py
addons/l10n_co/__init__.py
addons/l10n_co/__manifest__.py
addons/l10n_co/models/__init__.py
addons/l10n_co/models/l10n_latam_identification_type.py
addons/l10n_co/models/template_co.py
addons/l10n_co_pos/__init__.py
addons/l10n_co_pos/__manifest__.py
addons/l10n_co_pos/models/__init__.py
addons/l10n_co_pos/models/pos_order.py
addons/l10n_cr/__init__.py
addons/l10n_cr/__manifest__.py
addons/l10n_cr/models/__init__.py
addons/l10n_cr/models/template_cr.py
addons/l10n_cy/__init__.py
addons/l10n_cy/__manifest__.py
addons/l10n_cy/models/__init__.py
addons/l10n_cy/models/template_cy.py
addons/l10n_cz/__init__.py
addons/l10n_cz/__manifest__.py
addons/l10n_cz/models/__init__.py
addons/l10n_cz/models/account_move.py
addons/l10n_cz/models/l10n_cz_tax_office.py
addons/l10n_cz/models/res_company.py
addons/l10n_cz/models/template_cz.py
addons/l10n_cz/tests/__init__.py
addons/l10n_cz/tests/test_moves.py
addons/l10n_de/__init__.py
addons/l10n_de/__manifest__.py
addons/l10n_de/models/__init__.py
addons/l10n_de/models/account_account.py
addons/l10n_de/models/account_journal.py
addons/l10n_de/models/account_move.py
addons/l10n_de/models/chart_template.py
addons/l10n_de/models/datev.py
addons/l10n_de/models/ir_actions_report.py
addons/l10n_de/models/res_company.py
addons/l10n_de/models/template_de_skr03.py
addons/l10n_de/models/template_de_skr04.py
addons/l10n_de/tests/__init__.py
addons/l10n_de/tests/test_account_move.py
addons/l10n_de/tests/test_audit_trail.py
addons/l10n_din5008/__init__.py
addons/l10n_din5008/__manifest__.py
addons/l10n_din5008/models/__init__.py
addons/l10n_din5008/models/base_document_layout.py
addons/l10n_din5008/models/res_company.py
addons/l10n_din5008/models/res_config_settings.py
addons/l10n_din5008_purchase/__init__.py
addons/l10n_din5008_purchase/__manifest__.py
addons/l10n_din5008_repair/__init__.py
addons/l10n_din5008_repair/__manifest__.py
addons/l10n_din5008_repair/models/__init__.py
addons/l10n_din5008_repair/models/repair.py
addons/l10n_din5008_sale/__init__.py
addons/l10n_din5008_sale/__manifest__.py
addons/l10n_din5008_stock/__init__.py
addons/l10n_din5008_stock/__manifest__.py
addons/l10n_dk/__init__.py
addons/l10n_dk/__manifest__.py
addons/l10n_dk/models/__init__.py
addons/l10n_dk/models/account_account.py
addons/l10n_dk/models/account_journal.py
addons/l10n_dk/models/res_partner.py
addons/l10n_dk/models/template_dk.py
addons/l10n_dk_nemhandel/__init__.py
addons/l10n_dk_nemhandel/__manifest__.py
addons/l10n_dk_nemhandel/controllers/__init__.py
addons/l10n_dk_nemhandel/controllers/webhooks.py
addons/l10n_dk_nemhandel/models/__init__.py
addons/l10n_dk_nemhandel/models/account_edi_proxy_user.py
addons/l10n_dk_nemhandel/models/account_edi_xml_oioubl_21.py
addons/l10n_dk_nemhandel/models/account_journal.py
addons/l10n_dk_nemhandel/models/account_move.py
addons/l10n_dk_nemhandel/models/account_move_send.py
addons/l10n_dk_nemhandel/models/res_company.py
addons/l10n_dk_nemhandel/models/res_config_settings.py
addons/l10n_dk_nemhandel/models/res_partner.py
addons/l10n_dk_nemhandel/tests/__init__.py
addons/l10n_dk_nemhandel/tests/test_nemhandel_messages.py
addons/l10n_dk_nemhandel/tests/test_nemhandel_participant.py
addons/l10n_dk_nemhandel/tests/test_xml_oioubl_21.py
addons/l10n_dk_nemhandel/tools/__init__.py
addons/l10n_dk_nemhandel/tools/demo_utils.py
addons/l10n_dk_nemhandel/wizard/__init__.py
addons/l10n_dk_nemhandel/wizard/account_move_send_batch_wizard.py
addons/l10n_dk_nemhandel/wizard/account_move_send_wizard.py
addons/l10n_dk_nemhandel/wizard/nemhandel_registration.py
addons/l10n_dk_oioubl/__init__.py
addons/l10n_dk_oioubl/__manifest__.py
addons/l10n_dk_oioubl/models/__init__.py
addons/l10n_dk_oioubl/models/account_edi_xml_oioubl_201.py
addons/l10n_dk_oioubl/models/account_move.py
addons/l10n_dk_oioubl/models/res_partner.py
addons/l10n_dk_oioubl/tests/__init__.py
addons/l10n_dk_oioubl/tests/test_xml_oioubl_dk.py
addons/l10n_do/__init__.py
addons/l10n_do/__manifest__.py
addons/l10n_do/models/__init__.py
addons/l10n_do/models/template_do.py
addons/l10n_dz/__init__.py
addons/l10n_dz/__manifest__.py
addons/l10n_dz/models/__init__.py
addons/l10n_dz/models/template_dz.py
addons/l10n_ec/__init__.py
addons/l10n_ec/__manifest__.py
addons/l10n_ec/demo/__init__.py
addons/l10n_ec/demo/account_demo.py
addons/l10n_ec/models/__init__.py
addons/l10n_ec/models/account_journal.py
addons/l10n_ec/models/account_move.py
addons/l10n_ec/models/account_tax.py
addons/l10n_ec/models/account_tax_group.py
addons/l10n_ec/models/l10n_ec_sri_payment.py
addons/l10n_ec/models/l10n_latam_document_type.py
addons/l10n_ec/models/res_company.py
addons/l10n_ec/models/res_partner.py
addons/l10n_ec/models/template_ec.py
addons/l10n_ec/tests/__init__.py
addons/l10n_ec/tests/test_account_move.py
addons/l10n_ec_sale/__init__.py
addons/l10n_ec_sale/__manifest__.py
addons/l10n_ec_sale/controllers/__init__.py
addons/l10n_ec_sale/controllers/portal.py
addons/l10n_ec_sale/models/__init__.py
addons/l10n_ec_sale/models/payment_method.py
addons/l10n_ec_sale/models/sale_order.py
addons/l10n_ec_sale/tests/__init__.py
addons/l10n_ec_sale/tests/test_l10n_ec_account_sale.py
addons/l10n_ec_stock/__init__.py
addons/l10n_ec_stock/__manifest__.py
addons/l10n_ec_stock/models/__init__.py
addons/l10n_ec_stock/models/account_chart_template.py
addons/l10n_ee/__init__.py
addons/l10n_ee/__manifest__.py
addons/l10n_ee/models/__init__.py
addons/l10n_ee/models/account_tax.py
addons/l10n_ee/models/res_company.py
addons/l10n_ee/models/template_ee.py
addons/l10n_eg/__init__.py
addons/l10n_eg/__manifest__.py
addons/l10n_eg/models/__init__.py
addons/l10n_eg/models/account_tax.py
addons/l10n_eg/models/template_eg.py
addons/l10n_eg_edi_eta/__init__.py
addons/l10n_eg_edi_eta/__manifest__.py
addons/l10n_eg_edi_eta/models/__init__.py
addons/l10n_eg_edi_eta/models/account_edi_format.py
addons/l10n_eg_edi_eta/models/account_journal.py
addons/l10n_eg_edi_eta/models/account_move.py
addons/l10n_eg_edi_eta/models/eta_activity_type.py
addons/l10n_eg_edi_eta/models/eta_thumb_drive.py
addons/l10n_eg_edi_eta/models/product_template.py
addons/l10n_eg_edi_eta/models/res_company.py
addons/l10n_eg_edi_eta/models/res_config_settings.py
addons/l10n_eg_edi_eta/models/res_currency_rate.py
addons/l10n_eg_edi_eta/models/res_partner.py
addons/l10n_eg_edi_eta/models/uom_uom.py
addons/l10n_eg_edi_eta/tests/__init__.py
addons/l10n_eg_edi_eta/tests/common.py
addons/l10n_eg_edi_eta/tests/test_edi_json.py
addons/l10n_es/__init__.py
addons/l10n_es/__manifest__.py
addons/l10n_es/controllers/__init__.py
addons/l10n_es/controllers/portal.py
addons/l10n_es/models/__init__.py
addons/l10n_es/models/account_move.py
addons/l10n_es/models/account_tax.py
addons/l10n_es/models/res_company.py
addons/l10n_es/models/res_config_settings.py
addons/l10n_es/models/res_partner.py
addons/l10n_es/models/template_es_assec.py
addons/l10n_es/models/template_es_canary_assoc.py
addons/l10n_es/models/template_es_canary_common.py
addons/l10n_es/models/template_es_canary_full.py
addons/l10n_es/models/template_es_canary_pymes.py
addons/l10n_es/models/template_es_common.py
addons/l10n_es/models/template_es_common_mainland.py
addons/l10n_es/models/template_es_coop_full.py
addons/l10n_es/models/template_es_coop_pymes.py
addons/l10n_es/models/template_es_full.py
addons/l10n_es/models/template_es_pymes.py
addons/l10n_es_edi_facturae/__init__.py
addons/l10n_es_edi_facturae/__manifest__.py
addons/l10n_es_edi_facturae/xml_utils.py
addons/l10n_es_edi_facturae/models/__init__.py
addons/l10n_es_edi_facturae/models/account_chart_template.py
addons/l10n_es_edi_facturae/models/account_move.py
addons/l10n_es_edi_facturae/models/account_move_send.py
addons/l10n_es_edi_facturae/models/account_tax.py
addons/l10n_es_edi_facturae/models/certificate.py
addons/l10n_es_edi_facturae/models/res_company.py
addons/l10n_es_edi_facturae/models/res_partner.py
addons/l10n_es_edi_facturae/models/uom_uom.py
addons/l10n_es_edi_facturae/tests/__init__.py
addons/l10n_es_edi_facturae/tests/test_edi_xml.py
addons/l10n_es_edi_facturae/wizard/__init__.py
addons/l10n_es_edi_facturae/wizard/account_move_reversal.py
addons/l10n_es_edi_facturae/wizard/account_move_send_wizard.py
addons/l10n_es_edi_sii/__init__.py
addons/l10n_es_edi_sii/__manifest__.py
addons/l10n_es_edi_sii/models/__init__.py
addons/l10n_es_edi_sii/models/account_edi_format.py
addons/l10n_es_edi_sii/models/account_move.py
addons/l10n_es_edi_sii/models/account_move_send.py
addons/l10n_es_edi_sii/models/certificate.py
addons/l10n_es_edi_sii/models/res_company.py
addons/l10n_es_edi_sii/models/res_config_settings.py
addons/l10n_es_edi_sii/tests/__init__.py
addons/l10n_es_edi_sii/tests/common.py
addons/l10n_es_edi_sii/tests/test_edi_web_services.py
addons/l10n_es_edi_sii/tests/test_edi_xml.py
addons/l10n_es_edi_sii/tests/test_resequence.py
addons/l10n_es_edi_sii/wizards/__init__.py
addons/l10n_es_edi_sii/wizards/account_resequence_wizard.py
addons/l10n_es_edi_tbai/__init__.py
addons/l10n_es_edi_tbai/__manifest__.py
addons/l10n_es_edi_tbai/models/__init__.py
addons/l10n_es_edi_tbai/models/account_move.py
addons/l10n_es_edi_tbai/models/account_move_line.py
addons/l10n_es_edi_tbai/models/account_move_send.py
addons/l10n_es_edi_tbai/models/certificate.py
addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_agencies.py
addons/l10n_es_edi_tbai/models/l10n_es_edi_tbai_document.py
addons/l10n_es_edi_tbai/models/res_company.py
addons/l10n_es_edi_tbai/models/res_config_settings.py
addons/l10n_es_edi_tbai/models/xml_utils.py
addons/l10n_es_edi_tbai/tests/__init__.py
addons/l10n_es_edi_tbai/tests/common.py
addons/l10n_es_edi_tbai/tests/test_edi_tbai_send_bill_bizkaia.py
addons/l10n_es_edi_tbai/tests/test_edi_tbai_send_invoice.py
addons/l10n_es_edi_tbai/tests/test_edi_tbai_send_invoice_bizkaia.py
addons/l10n_es_edi_tbai/tests/test_edi_tbai_user_errors.py
addons/l10n_es_edi_tbai/tests/test_edi_web_services.py
addons/l10n_es_edi_tbai/tests/test_edi_xml.py
addons/l10n_es_edi_tbai/tests/test_move_reversal.py
addons/l10n_es_edi_tbai/wizards/__init__.py
addons/l10n_es_edi_tbai/wizards/account_move_reversal.py
addons/l10n_es_edi_tbai_pos/__init__.py
addons/l10n_es_edi_tbai_pos/__manifest__.py
addons/l10n_es_edi_tbai_pos/models/__init__.py
addons/l10n_es_edi_tbai_pos/models/pos_config.py
addons/l10n_es_edi_tbai_pos/models/pos_order.py
addons/l10n_es_edi_tbai_pos/models/res_company.py
addons/l10n_es_edi_tbai_pos/tests/__init__.py
addons/l10n_es_edi_tbai_pos/tests/common.py
addons/l10n_es_edi_tbai_pos/tests/test_tbai_pos.py
addons/l10n_es_edi_verifactu/__init__.py
addons/l10n_es_edi_verifactu/__manifest__.py
addons/l10n_es_edi_verifactu/models/__init__.py
addons/l10n_es_edi_verifactu/models/account_chart_template.py
addons/l10n_es_edi_verifactu/models/account_move.py
addons/l10n_es_edi_verifactu/models/account_move_send.py
addons/l10n_es_edi_verifactu/models/account_tax.py
addons/l10n_es_edi_verifactu/models/certificate.py
addons/l10n_es_edi_verifactu/models/res_company.py
addons/l10n_es_edi_verifactu/models/res_config_settings.py
addons/l10n_es_edi_verifactu/models/res_partner.py
addons/l10n_es_edi_verifactu/models/verifactu_document.py
addons/l10n_es_edi_verifactu/tests/__init__.py
addons/l10n_es_edi_verifactu/tests/common.py
addons/l10n_es_edi_verifactu/tests/test_document.py
addons/l10n_es_edi_verifactu/tests/test_json.py
addons/l10n_es_edi_verifactu/wizard/__init__.py
addons/l10n_es_edi_verifactu/wizard/account_move_reversal.py
addons/l10n_es_edi_verifactu_pos/__init__.py
addons/l10n_es_edi_verifactu_pos/__manifest__.py
addons/l10n_es_edi_verifactu_pos/models/__init__.py
addons/l10n_es_edi_verifactu_pos/models/account_move.py
addons/l10n_es_edi_verifactu_pos/models/pos_config.py
addons/l10n_es_edi_verifactu_pos/models/pos_order.py
addons/l10n_es_edi_verifactu_pos/models/res_company.py
addons/l10n_es_edi_verifactu_pos/models/verifactu_document.py
addons/l10n_es_edi_verifactu_pos/tests/__init__.py
addons/l10n_es_edi_verifactu_pos/tests/common.py
addons/l10n_es_edi_verifactu_pos/tests/test_pos_order.py
addons/l10n_es_pos/__init__.py
addons/l10n_es_pos/__manifest__.py
addons/l10n_es_pos/models/__init__.py
addons/l10n_es_pos/models/account_move.py
addons/l10n_es_pos/models/pos_config.py
addons/l10n_es_pos/models/pos_order.py
addons/l10n_es_pos/models/res_company.py
addons/l10n_es_pos/models/res_config_settings.py
addons/l10n_es_pos/tests/__init__.py
addons/l10n_es_pos/tests/test_frontend.py
addons/l10n_et/__init__.py
addons/l10n_et/__manifest__.py
addons/l10n_et/models/__init__.py
addons/l10n_et/models/template_et.py
addons/l10n_eu_oss/__init__.py
addons/l10n_eu_oss/__manifest__.py
addons/l10n_eu_oss/models/__init__.py
addons/l10n_eu_oss/models/eu_account_map.py
addons/l10n_eu_oss/models/eu_field_map.py
addons/l10n_eu_oss/models/eu_tag_map.py
addons/l10n_eu_oss/models/eu_tax_map.py
addons/l10n_eu_oss/models/res_company.py
addons/l10n_eu_oss/models/res_config_settings.py
addons/l10n_eu_oss/tests/__init__.py
addons/l10n_eu_oss/tests/test_oss.py
addons/l10n_fi/__init__.py
addons/l10n_fi/__manifest__.py
addons/l10n_fi/models/__init__.py
addons/l10n_fi/models/account_journal.py
addons/l10n_fi/models/account_move.py
addons/l10n_fi/models/template_fi.py
addons/l10n_fi/tests/__init__.py
addons/l10n_fi/tests/test_get_reference.py
addons/l10n_fi/tests/test_references.py
addons/l10n_fi_sale/__init__.py
addons/l10n_fi_sale/__manifest__.py
addons/l10n_fi_sale/models/__init__.py
addons/l10n_fi_sale/models/sale.py
addons/l10n_fr/__init__.py
addons/l10n_fr/__manifest__.py
addons/l10n_fr/models/__init__.py
addons/l10n_fr/models/res_company.py
addons/l10n_fr/models/res_partner.py
addons/l10n_fr_account/__init__.py
addons/l10n_fr_account/__manifest__.py
addons/l10n_fr_account/models/__init__.py
addons/l10n_fr_account/models/account_move.py
addons/l10n_fr_account/models/base_document_layout.py
addons/l10n_fr_account/models/res_company.py
addons/l10n_fr_account/models/res_partner.py
addons/l10n_fr_account/models/template_fr.py
addons/l10n_fr_account/models/template_mc.py
addons/l10n_fr_account/tests/__init__.py
addons/l10n_fr_account/tests/test_fec_export.py
addons/l10n_fr_account/wizard/__init__.py
addons/l10n_fr_account/wizard/account_fr_fec_export_wizard.py
addons/l10n_fr_facturx_chorus_pro/__init__.py
addons/l10n_fr_facturx_chorus_pro/__manifest__.py
addons/l10n_fr_facturx_chorus_pro/models/__init__.py
addons/l10n_fr_facturx_chorus_pro/models/account_edi_xml_ubl_bis3.py
addons/l10n_fr_facturx_chorus_pro/models/account_move.py
addons/l10n_fr_facturx_chorus_pro/tests/__init__.py
addons/l10n_fr_facturx_chorus_pro/tests/test_chorus_pro_xml.py
addons/l10n_fr_hr_holidays/__init__.py
addons/l10n_fr_hr_holidays/__manifest__.py
addons/l10n_fr_hr_holidays/models/__init__.py
addons/l10n_fr_hr_holidays/models/hr_leave.py
addons/l10n_fr_hr_holidays/models/res_company.py
addons/l10n_fr_hr_holidays/models/res_config_settings.py
addons/l10n_fr_hr_holidays/tests/__init__.py
addons/l10n_fr_hr_holidays/tests/test_french_leaves.py
addons/l10n_fr_hr_work_entry_holidays/__init__.py
addons/l10n_fr_hr_work_entry_holidays/__manifest__.py
addons/l10n_fr_hr_work_entry_holidays/models/__init__.py
addons/l10n_fr_hr_work_entry_holidays/models/hr_version.py
addons/l10n_fr_hr_work_entry_holidays/models/hr_work_entry.py
addons/l10n_fr_hr_work_entry_holidays/tests/__init__.py
addons/l10n_fr_hr_work_entry_holidays/tests/test_french_work_entries.py
addons/l10n_fr_pos_cert/__init__.py
addons/l10n_fr_pos_cert/__manifest__.py
addons/l10n_fr_pos_cert/models/__init__.py
addons/l10n_fr_pos_cert/models/account_closing.py
addons/l10n_fr_pos_cert/models/account_fiscal_position.py
addons/l10n_fr_pos_cert/models/pos.py
addons/l10n_fr_pos_cert/models/res_company.py
addons/l10n_fr_pos_cert/report/__init__.py
addons/l10n_fr_pos_cert/report/pos_hash_integrity.py
addons/l10n_fr_pos_cert/tests/__init__.py
addons/l10n_fr_pos_cert/tests/test_frontend.py
addons/l10n_fr_pos_cert/tests/test_string_to_hash.py
addons/l10n_ga/__init__.py
addons/l10n_ga/__manifest__.py
addons/l10n_ga/models/__init__.py
addons/l10n_ga/models/template_ga.py
addons/l10n_ga/models/template_ga_syscebnl.py
addons/l10n_gcc_invoice/__init__.py
addons/l10n_gcc_invoice/__manifest__.py
addons/l10n_gcc_invoice/models/__init__.py
addons/l10n_gcc_invoice/models/account_move.py
addons/l10n_gcc_invoice/models/product.py
addons/l10n_gcc_invoice/models/res_company.py
addons/l10n_gcc_invoice/models/res_config_settings.py
addons/l10n_gcc_invoice/tests/__init__.py
addons/l10n_gcc_invoice/tests/test_gcc_invoice.py
addons/l10n_gcc_invoice_stock_account/__init__.py
addons/l10n_gcc_invoice_stock_account/__manifest__.py
addons/l10n_gcc_pos/__init__.py
addons/l10n_gcc_pos/__manifest__.py
addons/l10n_gcc_pos/models/__init__.py
addons/l10n_gcc_pos/models/pos_config.py
addons/l10n_gcc_pos/models/res_config_settings.py
addons/l10n_gn/__init__.py
addons/l10n_gn/__manifest__.py
addons/l10n_gn/models/__init__.py
addons/l10n_gn/models/template_gn.py
addons/l10n_gn/models/template_gn_syscebnl.py
addons/l10n_gq/__init__.py
addons/l10n_gq/__manifest__.py
addons/l10n_gq/models/__init__.py
addons/l10n_gq/models/template_gq.py
addons/l10n_gq/models/template_gq_syscebnl.py
addons/l10n_gr/__init__.py
addons/l10n_gr/__manifest__.py
addons/l10n_gr/models/__init__.py
addons/l10n_gr/models/template_gr.py
addons/l10n_gr_edi/__init__.py
addons/l10n_gr_edi/__manifest__.py
addons/l10n_gr_edi/models/__init__.py
addons/l10n_gr_edi/models/account_fiscal_position.py
addons/l10n_gr_edi/models/account_move.py
addons/l10n_gr_edi/models/account_move_line.py
addons/l10n_gr_edi/models/account_move_send.py
addons/l10n_gr_edi/models/account_tax.py
addons/l10n_gr_edi/models/l10n_gr_edi_document.py
addons/l10n_gr_edi/models/preferred_classification.py
addons/l10n_gr_edi/models/product_template.py
addons/l10n_gr_edi/models/res_company.py
addons/l10n_gr_edi/models/res_config_settings.py
addons/l10n_gr_edi/models/res_partner.py
addons/l10n_gr_edi/tests/__init__.py
addons/l10n_gr_edi/tests/test_mydata_invoice.py
addons/l10n_gr_edi/wizard/__init__.py
addons/l10n_gr_edi/wizard/account_move_reversal.py
addons/l10n_gt/__init__.py
addons/l10n_gt/__manifest__.py
addons/l10n_gt/models/__init__.py
addons/l10n_gt/models/template_gt.py
addons/l10n_gw/__init__.py
addons/l10n_gw/__manifest__.py
addons/l10n_gw/models/__init__.py
addons/l10n_gw/models/template_gw.py
addons/l10n_gw/models/template_gw_syscebnl.py
addons/l10n_hk/__init__.py
addons/l10n_hk/__manifest__.py
addons/l10n_hk/models/__init__.py
addons/l10n_hk/models/res_bank.py
addons/l10n_hk/models/template_hk.py
addons/l10n_hk/tests/__init__.py
addons/l10n_hk/tests/test_l10n_hk_emv_qr.py
addons/l10n_hn/__init__.py
addons/l10n_hn/__manifest__.py
addons/l10n_hn/models/__init__.py
addons/l10n_hn/models/template_hn.py
addons/l10n_hr/__init__.py
addons/l10n_hr/__manifest__.py
addons/l10n_hr/models/__init__.py
addons/l10n_hr/models/template_hr.py
addons/l10n_hr_kuna/__init__.py
addons/l10n_hr_kuna/__manifest__.py
addons/l10n_hr_kuna/models/__init__.py
addons/l10n_hr_kuna/models/template_hr_kuna.py
addons/l10n_hu/__init__.py
addons/l10n_hu/__manifest__.py
addons/l10n_hu/models/__init__.py
addons/l10n_hu/models/account_move.py
addons/l10n_hu/models/res_partner.py
addons/l10n_hu/models/template_hu.py
addons/l10n_hu_edi/__init__.py
addons/l10n_hu_edi/__manifest__.py
addons/l10n_hu_edi/models/__init__.py
addons/l10n_hu_edi/models/account_move.py
addons/l10n_hu_edi/models/account_move_send.py
addons/l10n_hu_edi/models/account_tax.py
addons/l10n_hu_edi/models/l10n_hu_edi_connection.py
addons/l10n_hu_edi/models/product.py
addons/l10n_hu_edi/models/res_company.py
addons/l10n_hu_edi/models/res_config_settings.py
addons/l10n_hu_edi/models/res_partner.py
addons/l10n_hu_edi/models/template_hu.py
addons/l10n_hu_edi/models/uom_uom.py
addons/l10n_hu_edi/tests/__init__.py
addons/l10n_hu_edi/tests/common.py
addons/l10n_hu_edi/tests/test_credit_debit_notes.py
addons/l10n_hu_edi/tests/test_flows_live.py
addons/l10n_hu_edi/tests/test_flows_mocked.py
addons/l10n_hu_edi/tests/test_invoice_xml.py
addons/l10n_hu_edi/wizard/__init__.py
addons/l10n_hu_edi/wizard/account_move_debit.py
addons/l10n_hu_edi/wizard/account_move_reversal.py
addons/l10n_hu_edi/wizard/l10n_hu_edi_cancellation.py
addons/l10n_hu_edi/wizard/l10n_hu_edi_tax_audit_export.py
addons/l10n_id/__init__.py
addons/l10n_id/__manifest__.py
addons/l10n_id/controllers/__init__.py
addons/l10n_id/controllers/portal.py
addons/l10n_id/models/__init__.py
addons/l10n_id/models/account_move.py
addons/l10n_id/models/qris_transaction.py
addons/l10n_id/models/res_bank.py
addons/l10n_id/models/template_id.py
addons/l10n_id/tests/__init__.py
addons/l10n_id/tests/test_qris.py
addons/l10n_id/tests/test_qris_transaction.py
addons/l10n_id_efaktur_coretax/__init__.py
addons/l10n_id_efaktur_coretax/__manifest__.py
addons/l10n_id_efaktur_coretax/controllers/__init__.py
addons/l10n_id_efaktur_coretax/controllers/download_efaktur.py
addons/l10n_id_efaktur_coretax/models/__init__.py
addons/l10n_id_efaktur_coretax/models/account_move.py
addons/l10n_id_efaktur_coretax/models/account_move_line.py
addons/l10n_id_efaktur_coretax/models/efaktur_document.py
addons/l10n_id_efaktur_coretax/models/product_code.py
addons/l10n_id_efaktur_coretax/models/product_template.py
addons/l10n_id_efaktur_coretax/models/res_partner.py
addons/l10n_id_efaktur_coretax/models/uom_code.py
addons/l10n_id_efaktur_coretax/models/uom_uom.py
addons/l10n_id_efaktur_coretax/tests/__init__.py
addons/l10n_id_efaktur_coretax/tests/test_l10n_id_efaktur_coretax.py
addons/l10n_id_efaktur_coretax/tests/test_l10n_id_efaktur_download.py
addons/l10n_id_pos/__init__.py
addons/l10n_id_pos/__manifest__.py
addons/l10n_id_pos/models/__init__.py
addons/l10n_id_pos/models/pos_order.py
addons/l10n_id_pos/models/pos_payment_method.py
addons/l10n_id_pos/models/qris_transaction.py
addons/l10n_id_pos/tests/__init__.py
addons/l10n_id_pos/tests/test_qris_pos.py
addons/l10n_ie/__init__.py
addons/l10n_ie/__manifest__.py
addons/l10n_ie/models/__init__.py
addons/l10n_ie/models/template_ie.py
addons/l10n_il/__init__.py
addons/l10n_il/__manifest__.py
addons/l10n_il/models/__init__.py
addons/l10n_il/models/template_il.py
addons/l10n_in/__init__.py
addons/l10n_in/__manifest__.py
addons/l10n_in/demo/__init__.py
addons/l10n_in/demo/account_demo.py
addons/l10n_in/models/__init__.py
addons/l10n_in/models/account_account.py
addons/l10n_in/models/account_invoice.py
addons/l10n_in/models/account_journal.py
addons/l10n_in/models/account_move_line.py
addons/l10n_in/models/account_payment.py
addons/l10n_in/models/account_tax.py
addons/l10n_in/models/company.py
addons/l10n_in/models/iap_account.py
addons/l10n_in/models/l10n_in_pan_entity.py
addons/l10n_in/models/l10n_in_report_handler.py
addons/l10n_in/models/l10n_in_section_alert.py
addons/l10n_in/models/port_code.py
addons/l10n_in/models/product_template.py
addons/l10n_in/models/res_config_settings.py
addons/l10n_in/models/res_country_state.py
addons/l10n_in/models/res_partner.py
addons/l10n_in/models/template_in.py
addons/l10n_in/models/uom_uom.py
addons/l10n_in/tests/__init__.py
addons/l10n_in/tests/common.py
addons/l10n_in/tests/test_check_status.py
addons/l10n_in/tests/test_gstr_section.py
addons/l10n_in/tests/test_hsn_summary.py
addons/l10n_in/tests/test_l10n_in_fiscal_position.py
addons/l10n_in/tests/test_partner_details_on_invoice.py
addons/l10n_in/tests/test_tds_tcs_alert.py
addons/l10n_in/wizard/__init__.py
addons/l10n_in/wizard/l10n_in_withhold_wizard.py
addons/l10n_in_edi/__init__.py
addons/l10n_in_edi/__manifest__.py
addons/l10n_in_edi/models/__init__.py
addons/l10n_in_edi/models/account_move.py
addons/l10n_in_edi/models/account_move_line.py
addons/l10n_in_edi/models/account_move_send.py
addons/l10n_in_edi/models/ir_attachment.py
addons/l10n_in_edi/models/res_company.py
addons/l10n_in_edi/models/res_config_settings.py
addons/l10n_in_edi/models/res_partner.py
addons/l10n_in_edi/tests/__init__.py
addons/l10n_in_edi/tests/test_edi_json.py
addons/l10n_in_edi/wizard/__init__.py
addons/l10n_in_edi/wizard/l10n_in_edi_cancel.py
addons/l10n_in_ewaybill/__init__.py
addons/l10n_in_ewaybill/__manifest__.py
addons/l10n_in_ewaybill/demo/__init__.py
addons/l10n_in_ewaybill/demo/account_demo.py
addons/l10n_in_ewaybill/models/__init__.py
addons/l10n_in_ewaybill/models/account_move.py
addons/l10n_in_ewaybill/models/error_codes.py
addons/l10n_in_ewaybill/models/ewaybill_type.py
addons/l10n_in_ewaybill/models/ir_attachment.py
addons/l10n_in_ewaybill/models/l10n_in_ewaybill.py
addons/l10n_in_ewaybill/models/res_company.py
addons/l10n_in_ewaybill/models/res_config_settings.py
addons/l10n_in_ewaybill/tests/__init__.py
addons/l10n_in_ewaybill/tests/test_ewaybill_json.py
addons/l10n_in_ewaybill/tools/__init__.py
addons/l10n_in_ewaybill/tools/ewaybill_api.py
addons/l10n_in_ewaybill/wizard/__init__.py
addons/l10n_in_ewaybill/wizard/l10n_in_ewaybill_cancel.py
addons/l10n_in_ewaybill_irn/__init__.py
addons/l10n_in_ewaybill_irn/__manifest__.py
addons/l10n_in_ewaybill_irn/models/__init__.py
addons/l10n_in_ewaybill_irn/models/l10n_in_ewaybill.py
addons/l10n_in_ewaybill_irn/tests/__init__.py
addons/l10n_in_ewaybill_irn/tests/test_ewaybill_irn.py
addons/l10n_in_ewaybill_stock/__init__.py
addons/l10n_in_ewaybill_stock/__manifest__.py
addons/l10n_in_ewaybill_stock/models/__init__.py
addons/l10n_in_ewaybill_stock/models/l10n_in_ewaybill.py
addons/l10n_in_ewaybill_stock/models/stock_move.py
addons/l10n_in_ewaybill_stock/models/stock_picking.py
addons/l10n_in_ewaybill_stock/tests/__init__.py
addons/l10n_in_ewaybill_stock/tests/test_ewaybill_stock.py
addons/l10n_in_hr_holidays/__init__.py
addons/l10n_in_hr_holidays/__manifest__.py
addons/l10n_in_hr_holidays/models/__init__.py
addons/l10n_in_hr_holidays/models/hr_employee.py
addons/l10n_in_hr_holidays/models/hr_leave.py
addons/l10n_in_hr_holidays/models/hr_leave_type.py
addons/l10n_in_hr_holidays/models/l10n_in_hr_leave_optional_holiday.py
addons/l10n_in_hr_holidays/tests/__init__.py
addons/l10n_in_hr_holidays/tests/test_optional_holiday.py
addons/l10n_in_hr_holidays/tests/test_sandwich_leave.py
addons/l10n_in_pos/__init__.py
addons/l10n_in_pos/__manifest__.py
addons/l10n_in_pos/models/__init__.py
addons/l10n_in_pos/models/account_move.py
addons/l10n_in_pos/models/account_move_line.py
addons/l10n_in_pos/models/account_tax.py
addons/l10n_in_pos/models/pos_bill.py
addons/l10n_in_pos/models/pos_config.py
addons/l10n_in_pos/models/pos_order.py
addons/l10n_in_pos/models/pos_order_line.py
addons/l10n_in_pos/models/pos_session.py
addons/l10n_in_pos/models/product_template.py
addons/l10n_in_pos/tests/__init__.py
addons/l10n_in_pos/tests/common.py
addons/l10n_in_pos/tests/test_gstr_section.py
addons/l10n_in_pos/tests/test_hsn_summary.py
addons/l10n_in_pos/tests/test_pos_flow.py
addons/l10n_in_pos/tests/test_taxes_tax_totals_summary.py
addons/l10n_in_purchase_stock/__init__.py
addons/l10n_in_purchase_stock/__manifest__.py
addons/l10n_in_purchase_stock/models/__init__.py
addons/l10n_in_purchase_stock/models/account_move.py
addons/l10n_in_purchase_stock/models/stock_move.py
addons/l10n_in_purchase_stock/models/stock_picking.py
addons/l10n_in_sale/__init__.py
addons/l10n_in_sale/__manifest__.py
addons/l10n_in_sale/models/__init__.py
addons/l10n_in_sale/models/sale_order.py
addons/l10n_in_sale/tests/__init__.py
addons/l10n_in_sale/tests/test_l10n_in_sale_fiscal_position.py
addons/l10n_in_sale/wizard/__init__.py
addons/l10n_in_sale/wizard/sale_make_invoice_advance.py
addons/l10n_in_sale_stock/__init__.py
addons/l10n_in_sale_stock/__manifest__.py
addons/l10n_in_sale_stock/models/__init__.py
addons/l10n_in_sale_stock/models/account_move.py
addons/l10n_in_sale_stock/models/stock_move.py
addons/l10n_in_sale_stock/models/stock_picking.py
addons/l10n_in_stock/__init__.py
addons/l10n_in_stock/__manifest__.py
addons/l10n_in_stock/models/__init__.py
addons/l10n_in_stock/models/stock_move.py
addons/l10n_in_stock/models/stock_picking.py
addons/l10n_iq/__init__.py
addons/l10n_iq/__manifest__.py
addons/l10n_iq/models/__init__.py
addons/l10n_iq/models/template_iq.py
addons/l10n_it/__init__.py
addons/l10n_it/__manifest__.py
addons/l10n_it/models/__init__.py
addons/l10n_it/models/account_move.py
addons/l10n_it/models/account_report.py
addons/l10n_it/models/account_tax.py
addons/l10n_it/models/template_it.py
addons/l10n_it_edi/__init__.py
addons/l10n_it_edi/__manifest__.py
addons/l10n_it_edi/controllers/__init__.py
addons/l10n_it_edi/controllers/portal.py
addons/l10n_it_edi/models/__init__.py
addons/l10n_it_edi/models/account_edi_proxy_user.py
addons/l10n_it_edi/models/account_move.py
addons/l10n_it_edi/models/account_move_send.py
addons/l10n_it_edi/models/account_payment_method_line.py
addons/l10n_it_edi/models/account_tax.py
addons/l10n_it_edi/models/ddt.py
addons/l10n_it_edi/models/l10n_it_document_type.py
addons/l10n_it_edi/models/res_company.py
addons/l10n_it_edi/models/res_config_settings.py
addons/l10n_it_edi/models/res_partner.py
addons/l10n_it_edi/tests/__init__.py
addons/l10n_it_edi/tests/common.py
addons/l10n_it_edi/tests/test_account_move_document_type.py
addons/l10n_it_edi/tests/test_account_move_payment_method.py
addons/l10n_it_edi/tests/test_account_move_send.py
addons/l10n_it_edi/tests/test_edi_address.py
addons/l10n_it_edi/tests/test_edi_export.py
addons/l10n_it_edi/tests/test_edi_import.py
addons/l10n_it_edi/tests/test_edi_pa.py
addons/l10n_it_edi/tests/test_edi_reverse_charge.py
addons/l10n_it_edi/tests/test_it_document_type.py
addons/l10n_it_edi/tests/test_res_partner.py
addons/l10n_it_edi/tests/test_withholding.py
addons/l10n_it_edi/tools/__init__.py
addons/l10n_it_edi/tools/remove_signature.py
addons/l10n_it_edi_doi/__init__.py
addons/l10n_it_edi_doi/__manifest__.py
addons/l10n_it_edi_doi/models/__init__.py
addons/l10n_it_edi_doi/models/account_chart_template.py
addons/l10n_it_edi_doi/models/account_fiscal_position.py
addons/l10n_it_edi_doi/models/account_move.py
addons/l10n_it_edi_doi/models/account_tax.py
addons/l10n_it_edi_doi/models/declaration_of_intent.py
addons/l10n_it_edi_doi/models/res_company.py
addons/l10n_it_edi_doi/models/res_partner.py
addons/l10n_it_edi_doi/models/sale_order.py
addons/l10n_it_edi_doi/tests/__init__.py
addons/l10n_it_edi_doi/tests/common.py
addons/l10n_it_edi_doi/tests/test_amounts_and_warnings.py
addons/l10n_it_edi_sale/__init__.py
addons/l10n_it_edi_sale/__manifest__.py
addons/l10n_it_edi_sale/models/__init__.py
addons/l10n_it_edi_sale/models/sale_order.py
addons/l10n_it_edi_sale/tests/__init__.py
addons/l10n_it_edi_sale/tests/test_edi_sale_order_pa.py
addons/l10n_it_stock_ddt/__init__.py
addons/l10n_it_stock_ddt/__manifest__.py
addons/l10n_it_stock_ddt/models/__init__.py
addons/l10n_it_stock_ddt/models/account_invoice.py
addons/l10n_it_stock_ddt/models/stock_picking.py
addons/l10n_it_stock_ddt/tests/__init__.py
addons/l10n_it_stock_ddt/tests/test_ddt.py
addons/l10n_it_stock_ddt/tests/test_edi.py
addons/l10n_jo/__init__.py
addons/l10n_jo/__manifest__.py
addons/l10n_jo/models/__init__.py
addons/l10n_jo/models/template_jo_standard.py
addons/l10n_jo_edi/__init__.py
addons/l10n_jo_edi/__manifest__.py
addons/l10n_jo_edi/models/__init__.py
addons/l10n_jo_edi/models/account_edi_xml_ubl_21_jo.py
addons/l10n_jo_edi/models/account_move.py
addons/l10n_jo_edi/models/account_move_send.py
addons/l10n_jo_edi/models/account_tax.py
addons/l10n_jo_edi/models/ir_attachment.py
addons/l10n_jo_edi/models/res_company.py
addons/l10n_jo_edi/models/res_config_settings.py
addons/l10n_jo_edi/tests/__init__.py
addons/l10n_jo_edi/tests/jo_edi_common.py
addons/l10n_jo_edi/tests/test_jo_edi_invoice_codes.py
addons/l10n_jo_edi/tests/test_jo_edi_precision.py
addons/l10n_jo_edi/tests/test_jo_edi_types.py
addons/l10n_jp/__init__.py
addons/l10n_jp/__manifest__.py
addons/l10n_jp/models/__init__.py
addons/l10n_jp/models/template_jp.py
addons/l10n_jp_ubl_pint/__init__.py
addons/l10n_jp_ubl_pint/__manifest__.py
addons/l10n_jp_ubl_pint/models/__init__.py
addons/l10n_jp_ubl_pint/models/account_edi_xml_pint_jp.py
addons/l10n_jp_ubl_pint/models/res_partner.py
addons/l10n_jp_ubl_pint/tests/__init__.py
addons/l10n_jp_ubl_pint/tests/test_jp_ubl_pint.py
addons/l10n_ke/__init__.py
addons/l10n_ke/__manifest__.py
addons/l10n_ke/models/__init__.py
addons/l10n_ke/models/account_move.py
addons/l10n_ke/models/account_tax.py
addons/l10n_ke/models/l10n_ke_item_code.py
addons/l10n_ke/models/res_company.py
addons/l10n_ke/models/template_ke.py
addons/l10n_ke_edi_tremol/__init__.py
addons/l10n_ke_edi_tremol/__manifest__.py
addons/l10n_ke_edi_tremol/models/__init__.py
addons/l10n_ke_edi_tremol/models/account_move.py
addons/l10n_ke_edi_tremol/models/account_move_send.py
addons/l10n_ke_edi_tremol/models/res_company.py
addons/l10n_ke_edi_tremol/models/res_config_settings.py
addons/l10n_ke_edi_tremol/models/res_partner.py
addons/l10n_ke_edi_tremol/tests/__init__.py
addons/l10n_ke_edi_tremol/tests/test_account_move_send.py
addons/l10n_ke_edi_tremol/tests/test_move_export.py
addons/l10n_ke_edi_tremol/wizard/__init__.py
addons/l10n_ke_edi_tremol/wizard/account_move_send_wizard.py
addons/l10n_kh/__init__.py
addons/l10n_kh/__manifest__.py
addons/l10n_kh/models/__init__.py
addons/l10n_kh/models/res_bank.py
addons/l10n_kh/models/template_kh.py
addons/l10n_km/__init__.py
addons/l10n_km/__manifest__.py
addons/l10n_km/models/__init__.py
addons/l10n_km/models/template_km.py
addons/l10n_km/models/template_km_syscebnl.py
addons/l10n_kr/__init__.py
addons/l10n_kr/__manifest__.py
addons/l10n_kr/models/__init__.py
addons/l10n_kr/models/template_kr.py
addons/l10n_kw/__init__.py
addons/l10n_kw/__manifest__.py
addons/l10n_kw/models/__init__.py
addons/l10n_kw/models/template_kw.py
addons/l10n_kz/__init__.py
addons/l10n_kz/__manifest__.py
addons/l10n_kz/models/__init__.py
addons/l10n_kz/models/template_kz.py
addons/l10n_latam_base/__init__.py
addons/l10n_latam_base/__manifest__.py
addons/l10n_latam_base/controllers/__init__.py
addons/l10n_latam_base/controllers/portal.py
addons/l10n_latam_base/models/__init__.py
addons/l10n_latam_base/models/l10n_latam_identification_type.py
addons/l10n_latam_base/models/res_company.py
addons/l10n_latam_base/models/res_partner.py
addons/l10n_latam_check/__init__.py
addons/l10n_latam_check/__manifest__.py
addons/l10n_latam_check/models/__init__.py
addons/l10n_latam_check/models/account_chart_template.py
addons/l10n_latam_check/models/account_journal.py
addons/l10n_latam_check/models/account_move.py
addons/l10n_latam_check/models/account_move_line.py
addons/l10n_latam_check/models/account_payment.py
addons/l10n_latam_check/models/account_payment_method.py
addons/l10n_latam_check/models/l10n_latam_check.py
addons/l10n_latam_check/tests/__init__.py
addons/l10n_latam_check/tests/common.py
addons/l10n_latam_check/tests/test_own_checks.py
addons/l10n_latam_check/tests/test_third_party_checks.py
addons/l10n_latam_check/wizards/__init__.py
addons/l10n_latam_check/wizards/account_payment_register.py
addons/l10n_latam_check/wizards/l10n_latam_payment_mass_transfer.py
addons/l10n_latam_check/wizards/l10n_latam_payment_register_check.py
addons/l10n_latam_invoice_document/__init__.py
addons/l10n_latam_invoice_document/__manifest__.py
addons/l10n_latam_invoice_document/models/__init__.py
addons/l10n_latam_invoice_document/models/account_chart_template.py
addons/l10n_latam_invoice_document/models/account_journal.py
addons/l10n_latam_invoice_document/models/account_move.py
addons/l10n_latam_invoice_document/models/account_move_line.py
addons/l10n_latam_invoice_document/models/l10n_latam_document_type.py
addons/l10n_latam_invoice_document/models/res_company.py
addons/l10n_latam_invoice_document/report/__init__.py
addons/l10n_latam_invoice_document/report/invoice_report.py
addons/l10n_latam_invoice_document/wizards/__init__.py
addons/l10n_latam_invoice_document/wizards/account_debit_note.py
addons/l10n_latam_invoice_document/wizards/account_move_reversal.py
addons/l10n_lb_account/__init__.py
addons/l10n_lb_account/__manifest__.py
addons/l10n_lb_account/models/__init__.py
addons/l10n_lb_account/models/template_lb.py
addons/l10n_lt/__init__.py
addons/l10n_lt/__manifest__.py
addons/l10n_lt/models/__init__.py
addons/l10n_lt/models/account_journal.py
addons/l10n_lt/models/account_tax.py
addons/l10n_lt/models/template_lt.py
addons/l10n_lu/__init__.py
addons/l10n_lu/__manifest__.py
addons/l10n_lu/models/__init__.py
addons/l10n_lu/models/template_lu.py
addons/l10n_lv/__init__.py
addons/l10n_lv/__manifest__.py
addons/l10n_lv/models/__init__.py
addons/l10n_lv/models/template_lv.py
addons/l10n_ma/__init__.py
addons/l10n_ma/__manifest__.py
addons/l10n_ma/models/__init__.py
addons/l10n_ma/models/base_document_layout.py
addons/l10n_ma/models/res_partner.py
addons/l10n_ma/models/template_ma.py
addons/l10n_mc/__init__.py
addons/l10n_mc/__manifest__.py
addons/l10n_ml/__init__.py
addons/l10n_ml/__manifest__.py
addons/l10n_ml/models/__init__.py
addons/l10n_ml/models/template_ml.py
addons/l10n_ml/models/template_ml_syscebnl.py
addons/l10n_mn/__init__.py
addons/l10n_mn/__manifest__.py
addons/l10n_mn/models/__init__.py
addons/l10n_mn/models/template_mn.py
addons/l10n_mt/__init__.py
addons/l10n_mt/__manifest__.py
addons/l10n_mt/models/__init__.py
addons/l10n_mt/models/template_mt.py
addons/l10n_mt_pos/__init__.py
addons/l10n_mt_pos/__manifest__.py
addons/l10n_mt_pos/wizards/__init__.py
addons/l10n_mt_pos/wizards/compliance_letter.py
addons/l10n_mu_account/__init__.py
addons/l10n_mu_account/__manifest__.py
addons/l10n_mu_account/models/__init__.py
addons/l10n_mu_account/models/account_move.py
addons/l10n_mu_account/models/base_document_layout.py
addons/l10n_mu_account/models/template_mu.py
addons/l10n_mx/__init__.py
addons/l10n_mx/__manifest__.py
addons/l10n_mx/models/__init__.py
addons/l10n_mx/models/account_account.py
addons/l10n_mx/models/account_move_line.py
addons/l10n_mx/models/account_tax.py
addons/l10n_mx/models/res_bank.py
addons/l10n_mx/models/res_company.py
addons/l10n_mx/models/res_config_settings.py
addons/l10n_mx/models/template_mx.py
addons/l10n_mx/tests/__init__.py
addons/l10n_mx/tests/common.py
addons/l10n_mx/tests/test_account_move.py
addons/l10n_my/__init__.py
addons/l10n_my/__manifest__.py
addons/l10n_my/models/__init__.py
addons/l10n_my/models/product_template.py
addons/l10n_my/models/template_my.py
addons/l10n_my_edi/__init__.py
addons/l10n_my_edi/__manifest__.py
addons/l10n_my_edi/controllers/__init__.py
addons/l10n_my_edi/controllers/portal.py
addons/l10n_my_edi/models/__init__.py
addons/l10n_my_edi/models/account_edi_proxy_user.py
addons/l10n_my_edi/models/account_edi_xml_ubl_my.py
addons/l10n_my_edi/models/account_move.py
addons/l10n_my_edi/models/account_move_line.py
addons/l10n_my_edi/models/account_move_send.py
addons/l10n_my_edi/models/account_tax.py
addons/l10n_my_edi/models/l10n_my_edi_industry_classification.py
addons/l10n_my_edi/models/myinvois_document.py
addons/l10n_my_edi/models/product_template.py
addons/l10n_my_edi/models/res_company.py
addons/l10n_my_edi/models/res_config_settings.py
addons/l10n_my_edi/models/res_partner.py
addons/l10n_my_edi/tests/__init__.py
addons/l10n_my_edi/tests/test_file_generation.py
addons/l10n_my_edi/tests/test_submissions.py
addons/l10n_my_edi/wizard/__init__.py
addons/l10n_my_edi/wizard/myinvois_consolidate_invoice_wizard.py
addons/l10n_my_edi/wizard/myinvois_document_status_update_wizard.py
addons/l10n_my_edi_pos/__init__.py
addons/l10n_my_edi_pos/__manifest__.py
addons/l10n_my_edi_pos/models/__init__.py
addons/l10n_my_edi_pos/models/account_edi_xml_ubl_my.py
addons/l10n_my_edi_pos/models/myinvois_document_pos.py
addons/l10n_my_edi_pos/models/pos_order.py
addons/l10n_my_edi_pos/tests/__init__.py
addons/l10n_my_edi_pos/tests/test_myinvois_pos.py
addons/l10n_my_edi_pos/wizard/__init__.py
addons/l10n_my_edi_pos/wizard/myinvois_consolidate_invoice_wizard.py
addons/l10n_my_ubl_pint/__init__.py
addons/l10n_my_ubl_pint/__manifest__.py
addons/l10n_my_ubl_pint/models/__init__.py
addons/l10n_my_ubl_pint/models/account_edi_xml_pint_my.py
addons/l10n_my_ubl_pint/models/res_company.py
addons/l10n_my_ubl_pint/models/res_partner.py
addons/l10n_my_ubl_pint/tests/__init__.py
addons/l10n_my_ubl_pint/tests/test_my_ubl_pint.py
addons/l10n_mz/__init__.py
addons/l10n_mz/__manifest__.py
addons/l10n_mz/models/__init__.py
addons/l10n_mz/models/template_mz.py
addons/l10n_ne/__init__.py
addons/l10n_ne/__manifest__.py
addons/l10n_ne/models/__init__.py
addons/l10n_ne/models/template_ne.py
addons/l10n_ne/models/template_ne_syscebnl.py
addons/l10n_ng/__init__.py
addons/l10n_ng/__manifest__.py
addons/l10n_ng/models/__init__.py
addons/l10n_ng/models/template_ng.py
addons/l10n_nl/__init__.py
addons/l10n_nl/__manifest__.py
addons/l10n_nl/models/__init__.py
addons/l10n_nl/models/account_chart_template.py
addons/l10n_nl/models/account_journal.py
addons/l10n_nl/models/res_company.py
addons/l10n_nl/models/template_nl.py
addons/l10n_no/__init__.py
addons/l10n_no/__manifest__.py
addons/l10n_no/models/__init__.py
addons/l10n_no/models/account_journal.py
addons/l10n_no/models/account_move.py
addons/l10n_no/models/account_tax.py
addons/l10n_no/models/res_company.py
addons/l10n_no/models/res_partner.py
addons/l10n_no/models/template_no.py
addons/l10n_nz/__init__.py
addons/l10n_nz/__manifest__.py
addons/l10n_nz/models/__init__.py
addons/l10n_nz/models/account_move.py
addons/l10n_nz/models/account_payment.py
addons/l10n_nz/models/res_partner.py
addons/l10n_nz/models/template_nz.py
addons/l10n_om/__init__.py
addons/l10n_om/__manifest__.py
addons/l10n_om/models/__init__.py
addons/l10n_om/models/template_om.py
addons/l10n_pa/__init__.py
addons/l10n_pa/__manifest__.py
addons/l10n_pa/models/__init__.py
addons/l10n_pa/models/template_pa.py
addons/l10n_pe/__init__.py
addons/l10n_pe/__manifest__.py
addons/l10n_pe/controllers/__init__.py
addons/l10n_pe/controllers/portal.py
addons/l10n_pe/demo/__init__.py
addons/l10n_pe/demo/account_demo.py
addons/l10n_pe/models/__init__.py
addons/l10n_pe/models/account_move.py
addons/l10n_pe/models/account_tax.py
addons/l10n_pe/models/l10n_latam_identification_type.py
addons/l10n_pe/models/res_bank.py
addons/l10n_pe/models/res_city.py
addons/l10n_pe/models/res_city_district.py
addons/l10n_pe/models/res_company.py
addons/l10n_pe/models/res_partner.py
addons/l10n_pe/models/template_pe.py
addons/l10n_pe_pos/__init__.py
addons/l10n_pe_pos/__manifest__.py
addons/l10n_pe_pos/models/__init__.py
addons/l10n_pe_pos/models/l10n_latam_identification_type.py
addons/l10n_pe_pos/models/l10n_pe_res_city_district.py
addons/l10n_pe_pos/models/pos_config.py
addons/l10n_pe_pos/models/pos_session.py
addons/l10n_pe_pos/models/res_city.py
addons/l10n_pe_pos/models/res_partner.py
addons/l10n_ph/__init__.py
addons/l10n_ph/__manifest__.py
addons/l10n_ph/utils.py
addons/l10n_ph/models/__init__.py
addons/l10n_ph/models/account_move.py
addons/l10n_ph/models/account_payment.py
addons/l10n_ph/models/account_tax.py
addons/l10n_ph/models/res_company.py
addons/l10n_ph/models/res_partner.py
addons/l10n_ph/models/template_ph.py
addons/l10n_ph/tests/__init__.py
addons/l10n_ph/tests/common.py
addons/l10n_ph/tests/test_bir_2307_generation.py
addons/l10n_ph/wizard/__init__.py
addons/l10n_ph/wizard/generate_2307_wizard.py
addons/l10n_pk/__init__.py
addons/l10n_pk/__manifest__.py
addons/l10n_pk/demo/__init__.py
addons/l10n_pk/demo/account_demo.py
addons/l10n_pk/models/__init__.py
addons/l10n_pk/models/template_pk.py
addons/l10n_pl/__init__.py
addons/l10n_pl/__manifest__.py
addons/l10n_pl/models/__init__.py
addons/l10n_pl/models/account_move.py
addons/l10n_pl/models/l10n_pl_tax_office.py
addons/l10n_pl/models/product.py
addons/l10n_pl/models/res_company.py
addons/l10n_pl/models/res_config_settings.py
addons/l10n_pl/models/res_partner.py
addons/l10n_pl/models/template_pl.py
addons/l10n_pl/tests/__init__.py
addons/l10n_pl/tests/test_taxable_supply_date_with_lock_dates.py
addons/l10n_pt/__init__.py
addons/l10n_pt/__manifest__.py
addons/l10n_pt/models/__init__.py
addons/l10n_pt/models/account_account.py
addons/l10n_pt/models/template_pt.py
addons/l10n_qa/__init__.py
addons/l10n_qa/__manifest__.py
addons/l10n_qa/models/__init__.py
addons/l10n_qa/models/template_qa.py
addons/l10n_ro/__init__.py
addons/l10n_ro/__manifest__.py
addons/l10n_ro/models/__init__.py
addons/l10n_ro/models/res_partner.py
addons/l10n_ro/models/template_ro.py
addons/l10n_ro_edi/__init__.py
addons/l10n_ro_edi/__manifest__.py
addons/l10n_ro_edi/controllers/__init__.py
addons/l10n_ro_edi/controllers/main.py
addons/l10n_ro_edi/models/__init__.py
addons/l10n_ro_edi/models/account_edi_xml_ubl_ciusro.py
addons/l10n_ro_edi/models/account_move.py
addons/l10n_ro_edi/models/account_move_send.py
addons/l10n_ro_edi/models/ciusro_document.py
addons/l10n_ro_edi/models/res_company.py
addons/l10n_ro_edi/models/res_config_settings.py
addons/l10n_ro_edi/models/res_partner.py
addons/l10n_ro_edi/models/utils.py
addons/l10n_ro_edi/tests/__init__.py
addons/l10n_ro_edi/tests/test_xml_ubl_ro.py
addons/l10n_ro_edi/wizard/__init__.py
addons/l10n_ro_edi/wizard/account_move_send_wizard.py
addons/l10n_ro_edi_stock/__init__.py
addons/l10n_ro_edi_stock/__manifest__.py
addons/l10n_ro_edi_stock/models/__init__.py
addons/l10n_ro_edi_stock/models/delivery_carrier.py
addons/l10n_ro_edi_stock/models/etransport_api.py
addons/l10n_ro_edi_stock/models/l10n_ro_edi_stock_document.py
addons/l10n_ro_edi_stock/models/stock_picking.py
addons/l10n_ro_edi_stock/tests/__init__.py
addons/l10n_ro_edi_stock/tests/common.py
addons/l10n_ro_edi_stock/tests/test_etransport_flows.py
addons/l10n_ro_edi_stock_batch/__init__.py
addons/l10n_ro_edi_stock_batch/__manifest__.py
addons/l10n_ro_edi_stock_batch/models/__init__.py
addons/l10n_ro_edi_stock_batch/models/l10n_ro_edi_stock_document.py
addons/l10n_ro_edi_stock_batch/models/stock_picking.py
addons/l10n_ro_edi_stock_batch/models/stock_picking_batch.py
addons/l10n_rs/__init__.py
addons/l10n_rs/__manifest__.py
addons/l10n_rs/models/__init__.py
addons/l10n_rs/models/account_move.py
addons/l10n_rs/models/template_rs.py
addons/l10n_rs_edi/__init__.py
addons/l10n_rs_edi/__manifest__.py
addons/l10n_rs_edi/models/__init__.py
addons/l10n_rs_edi/models/account_edi_xml_ubl_21_rs.py
addons/l10n_rs_edi/models/account_move.py
addons/l10n_rs_edi/models/account_move_send.py
addons/l10n_rs_edi/models/res_company.py
addons/l10n_rs_edi/models/res_config_settings.py
addons/l10n_rs_edi/models/res_partner.py
addons/l10n_rs_edi/tests/__init__.py
addons/l10n_rs_edi/tests/test_xml_ubl_rs.py
addons/l10n_rs_edi/wizard/__init__.py
addons/l10n_rs_edi/wizard/account_move_send_wizard.py
addons/l10n_rw/__init__.py
addons/l10n_rw/__manifest__.py
addons/l10n_rw/models/__init__.py
addons/l10n_rw/models/template_rw.py
addons/l10n_sa/__init__.py
addons/l10n_sa/__manifest__.py
addons/l10n_sa/models/__init__.py
addons/l10n_sa/models/account_move.py
addons/l10n_sa/models/ir_attachment.py
addons/l10n_sa/models/template_sa.py
addons/l10n_sa/wizard/__init__.py
addons/l10n_sa/wizard/account_debit_note.py
addons/l10n_sa/wizard/account_move_reversal.py
addons/l10n_sa_edi/__init__.py
addons/l10n_sa_edi/__manifest__.py
addons/l10n_sa_edi/models/__init__.py
addons/l10n_sa_edi/models/account_edi_document.py
addons/l10n_sa_edi/models/account_edi_format.py
addons/l10n_sa_edi/models/account_edi_xml_ubl_21_zatca.py
addons/l10n_sa_edi/models/account_journal.py
addons/l10n_sa_edi/models/account_move.py
addons/l10n_sa_edi/models/account_move_send.py
addons/l10n_sa_edi/models/account_tax.py
addons/l10n_sa_edi/models/certificate.py
addons/l10n_sa_edi/models/ir_attachment.py
addons/l10n_sa_edi/models/res_company.py
addons/l10n_sa_edi/models/res_config_settings.py
addons/l10n_sa_edi/models/res_partner.py
addons/l10n_sa_edi/tests/__init__.py
addons/l10n_sa_edi/tests/common.py
addons/l10n_sa_edi/tests/test_edi_zatca.py
addons/l10n_sa_edi/wizard/__init__.py
addons/l10n_sa_edi/wizard/base_document_layout.py
addons/l10n_sa_edi/wizard/l10n_sa_edi_otp_wizard.py
addons/l10n_sa_edi_pos/__init__.py
addons/l10n_sa_edi_pos/__manifest__.py
addons/l10n_sa_edi_pos/models/__init__.py
addons/l10n_sa_edi_pos/models/account_edi_xml_ubl_21_zatca.py
addons/l10n_sa_edi_pos/models/pos_config.py
addons/l10n_sa_edi_pos/models/pos_order.py
addons/l10n_sa_edi_pos/models/res_company.py
addons/l10n_sa_pos/__init__.py
addons/l10n_sa_pos/__manifest__.py
addons/l10n_sa_pos/models/__init__.py
addons/l10n_sa_pos/models/pos_config.py
addons/l10n_sa_pos/models/pos_order.py
addons/l10n_sa_pos/tests/__init__.py
addons/l10n_sa_pos/tests/test_sa_pos.py
addons/l10n_sa_withholding_tax/__init__.py
addons/l10n_sa_withholding_tax/__manifest__.py
addons/l10n_se/__init__.py
addons/l10n_se/__manifest__.py
addons/l10n_se/models/__init__.py
addons/l10n_se/models/account_journal.py
addons/l10n_se/models/account_move.py
addons/l10n_se/models/res_company.py
addons/l10n_se/models/res_partner.py
addons/l10n_se/models/template_se.py
addons/l10n_se/models/template_se_K2.py
addons/l10n_se/models/template_se_K3.py
addons/l10n_sg/__init__.py
addons/l10n_sg/__manifest__.py
addons/l10n_sg/models/__init__.py
addons/l10n_sg/models/account_move.py
addons/l10n_sg/models/res_bank.py
addons/l10n_sg/models/res_company.py
addons/l10n_sg/models/res_partner.py
addons/l10n_sg/models/template_sg.py
addons/l10n_sg/tests/__init__.py
addons/l10n_sg/tests/test_l10n_sg_emv_qr.py
addons/l10n_sg_ubl_pint/__init__.py
addons/l10n_sg_ubl_pint/__manifest__.py
addons/l10n_sg_ubl_pint/models/__init__.py
addons/l10n_sg_ubl_pint/models/account_edi_xml_pint_sg.py
addons/l10n_sg_ubl_pint/models/account_move.py
addons/l10n_sg_ubl_pint/models/account_tax.py
addons/l10n_sg_ubl_pint/models/res_partner.py
addons/l10n_sg_ubl_pint/tests/__init__.py
addons/l10n_sg_ubl_pint/tests/test_sg_ubl_pint.py
addons/l10n_si/__init__.py
addons/l10n_si/__manifest__.py
addons/l10n_si/models/__init__.py
addons/l10n_si/models/account_journal.py
addons/l10n_si/models/account_move.py
addons/l10n_si/models/template_si.py
addons/l10n_si/tests/__init__.py
addons/l10n_si/tests/test_get_reference.py
addons/l10n_sk/__init__.py
addons/l10n_sk/__manifest__.py
addons/l10n_sk/models/__init__.py
addons/l10n_sk/models/account_move.py
addons/l10n_sk/models/res_company.py
addons/l10n_sk/models/template_sk.py
addons/l10n_sn/__init__.py
addons/l10n_sn/__manifest__.py
addons/l10n_sn/models/__init__.py
addons/l10n_sn/models/template_sn.py
addons/l10n_sn/models/template_sn_syscebnl.py
addons/l10n_syscohada/__init__.py
addons/l10n_syscohada/__manifest__.py
addons/l10n_syscohada/models/__init__.py
addons/l10n_syscohada/models/template_syscebnl.py
addons/l10n_syscohada/models/template_syscohada.py
addons/l10n_td/__init__.py
addons/l10n_td/__manifest__.py
addons/l10n_td/models/__init__.py
addons/l10n_td/models/template_td.py
addons/l10n_td/models/template_td_syscebnl.py
addons/l10n_test_pos_qr_payment/__init__.py
addons/l10n_test_pos_qr_payment/__manifest__.py
addons/l10n_test_pos_qr_payment/tests/__init__.py
addons/l10n_test_pos_qr_payment/tests/common.py
addons/l10n_test_pos_qr_payment/tests/test_pos_qr_payment.py
addons/l10n_tg/__init__.py
addons/l10n_tg/__manifest__.py
addons/l10n_tg/models/__init__.py
addons/l10n_tg/models/template_tg.py
addons/l10n_tg/models/template_tg_syscebnl.py
addons/l10n_th/__init__.py
addons/l10n_th/__manifest__.py
addons/l10n_th/models/__init__.py
addons/l10n_th/models/account_move.py
addons/l10n_th/models/ir_actions_report.py
addons/l10n_th/models/res_bank.py
addons/l10n_th/models/res_partner.py
addons/l10n_th/models/template_th.py
addons/l10n_th/tests/__init__.py
addons/l10n_th/tests/test_l10n_th_emv_qr.py
addons/l10n_tn/__init__.py
addons/l10n_tn/__manifest__.py
addons/l10n_tn/models/__init__.py
addons/l10n_tn/models/template_tn.py
addons/l10n_tr/__init__.py
addons/l10n_tr/__manifest__.py
addons/l10n_tr/models/__init__.py
addons/l10n_tr/models/account_journal.py
addons/l10n_tr/models/account_move_line.py
addons/l10n_tr/models/product.py
addons/l10n_tr/models/template_tr.py
addons/l10n_tr_nilvera/__init__.py
addons/l10n_tr_nilvera/__manifest__.py
addons/l10n_tr_nilvera/lib/nilvera_client.py
addons/l10n_tr_nilvera/models/__init__.py
addons/l10n_tr_nilvera/models/account_journal.py
addons/l10n_tr_nilvera/models/l10n_tr_nilvera_alias.py
addons/l10n_tr_nilvera/models/res_company.py
addons/l10n_tr_nilvera/models/res_config_settings.py
addons/l10n_tr_nilvera/models/res_partner.py
addons/l10n_tr_nilvera/models/uom_uom.py
addons/l10n_tr_nilvera_edispatch/__init__.py
addons/l10n_tr_nilvera_edispatch/__manifest__.py
addons/l10n_tr_nilvera_edispatch/models/__init__.py
addons/l10n_tr_nilvera_edispatch/models/l10n_tr_nilvera_trailer_plate.py
addons/l10n_tr_nilvera_edispatch/models/res_partner.py
addons/l10n_tr_nilvera_edispatch/models/stock_picking.py
addons/l10n_tr_nilvera_edispatch/models/stock_picking_type.py
addons/l10n_tr_nilvera_einvoice/__init__.py
addons/l10n_tr_nilvera_einvoice/__manifest__.py
addons/l10n_tr_nilvera_einvoice/models/__init__.py
addons/l10n_tr_nilvera_einvoice/models/account_edi_xml_ubl_tr.py
addons/l10n_tr_nilvera_einvoice/models/account_journal.py
addons/l10n_tr_nilvera_einvoice/models/account_move.py
addons/l10n_tr_nilvera_einvoice/models/account_move_send.py
addons/l10n_tr_nilvera_einvoice/models/res_partner_category.py
addons/l10n_tr_nilvera_einvoice/tests/__init__.py
addons/l10n_tr_nilvera_einvoice/tests/test_xml_ubl_tr.py
addons/l10n_tw/__init__.py
addons/l10n_tw/__manifest__.py
addons/l10n_tw/models/__init__.py
addons/l10n_tw/models/template_tw.py
addons/l10n_tw_edi_ecpay/__init__.py
addons/l10n_tw_edi_ecpay/__manifest__.py
addons/l10n_tw_edi_ecpay/utils.py
addons/l10n_tw_edi_ecpay/controllers/__init__.py
addons/l10n_tw_edi_ecpay/controllers/main.py
addons/l10n_tw_edi_ecpay/models/__init__.py
addons/l10n_tw_edi_ecpay/models/account_move.py
addons/l10n_tw_edi_ecpay/models/account_move_line.py
addons/l10n_tw_edi_ecpay/models/account_move_send.py
addons/l10n_tw_edi_ecpay/models/account_tax.py
addons/l10n_tw_edi_ecpay/models/res_company.py
addons/l10n_tw_edi_ecpay/models/res_config_settings.py
addons/l10n_tw_edi_ecpay/models/res_partner.py
addons/l10n_tw_edi_ecpay/tests/__init__.py
addons/l10n_tw_edi_ecpay/tests/test_edi.py
addons/l10n_tw_edi_ecpay/wizard/__init__.py
addons/l10n_tw_edi_ecpay/wizard/account_move_reversal.py
addons/l10n_tw_edi_ecpay/wizard/l10n_tw_edi_invoice_cancel.py
addons/l10n_tw_edi_ecpay/wizard/l10n_tw_edi_invoice_print.py
addons/l10n_tz_account/__init__.py
addons/l10n_tz_account/__manifest__.py
addons/l10n_tz_account/models/__init__.py
addons/l10n_tz_account/models/template_tz.py
addons/l10n_ua/__init__.py
addons/l10n_ua/__manifest__.py
addons/l10n_ua/models/__init__.py
addons/l10n_ua/models/template_ua_psbo.py
addons/l10n_ug/__init__.py
addons/l10n_ug/__manifest__.py
addons/l10n_ug/models/__init__.py
addons/l10n_ug/models/template_ug.py
addons/l10n_uk/__init__.py
addons/l10n_uk/__manifest__.py
addons/l10n_uk/models/__init__.py
addons/l10n_uk/models/template_uk.py
addons/l10n_us/__init__.py
addons/l10n_us/__manifest__.py
addons/l10n_us/models/__init__.py
addons/l10n_us/models/res_partner_bank.py
addons/l10n_us_account/__init__.py
addons/l10n_us_account/__manifest__.py
addons/l10n_us_account/models/__init__.py
addons/l10n_us_account/models/res_bank.py
addons/l10n_us_account/models/template_us.py
addons/l10n_uy/__init__.py
addons/l10n_uy/__manifest__.py
addons/l10n_uy/controllers/__init__.py
addons/l10n_uy/controllers/portal.py
addons/l10n_uy/demo/__init__.py
addons/l10n_uy/demo/account_demo.py
addons/l10n_uy/models/__init__.py
addons/l10n_uy/models/account_move.py
addons/l10n_uy/models/account_tax.py
addons/l10n_uy/models/l10n_latam_document_type.py
addons/l10n_uy/models/l10n_latam_identification_type.py
addons/l10n_uy/models/res_company.py
addons/l10n_uy/models/res_partner.py
addons/l10n_uy/models/template_uy.py
addons/l10n_uy/tests/__init__.py
addons/l10n_uy/tests/test_check_vat.py
addons/l10n_uy/tests/test_doc_types.py
addons/l10n_uy_pos/__init__.py
addons/l10n_uy_pos/__manifest__.py
addons/l10n_ve/__init__.py
addons/l10n_ve/__manifest__.py
addons/l10n_ve/models/__init__.py
addons/l10n_ve/models/template_ve.py
addons/l10n_vn/__init__.py
addons/l10n_vn/__manifest__.py
addons/l10n_vn/models/__init__.py
addons/l10n_vn/models/account_move.py
addons/l10n_vn/models/res_bank.py
addons/l10n_vn/models/template_vn.py
addons/l10n_vn/tests/__init__.py
addons/l10n_vn/tests/test_l10n_vn_emv_qr.py
addons/l10n_vn_edi_viettel/__init__.py
addons/l10n_vn_edi_viettel/__manifest__.py
addons/l10n_vn_edi_viettel/models/__init__.py
addons/l10n_vn_edi_viettel/models/account_move.py
addons/l10n_vn_edi_viettel/models/account_move_send.py
addons/l10n_vn_edi_viettel/models/res_company.py
addons/l10n_vn_edi_viettel/models/res_config_settings.py
addons/l10n_vn_edi_viettel/models/res_partner.py
addons/l10n_vn_edi_viettel/models/sinvoice.py
addons/l10n_vn_edi_viettel/tests/__init__.py
addons/l10n_vn_edi_viettel/tests/test_edi.py
addons/l10n_vn_edi_viettel/wizard/__init__.py
addons/l10n_vn_edi_viettel/wizard/account_move_reversal.py
addons/l10n_vn_edi_viettel/wizard/l10n_vn_edi_cancellation_request.py
addons/l10n_za/__init__.py
addons/l10n_za/__manifest__.py
addons/l10n_za/models/__init__.py
addons/l10n_za/models/template_za.py
addons/l10n_zm_account/__init__.py
addons/l10n_zm_account/__manifest__.py
addons/l10n_zm_account/models/__init__.py
addons/l10n_zm_account/models/account_move.py
addons/l10n_zm_account/models/template_zm.py
addons/link_tracker/__init__.py
addons/link_tracker/__manifest__.py
addons/link_tracker/controller/__init__.py
addons/link_tracker/controller/main.py
addons/link_tracker/models/__init__.py
addons/link_tracker/models/link_tracker.py
addons/link_tracker/models/mail_render_mixin.py
addons/link_tracker/models/utm.py
addons/link_tracker/tests/__init__.py
addons/link_tracker/tests/common.py
addons/link_tracker/tests/test_link_tracker.py
addons/link_tracker/tests/test_mail_render_mixin.py
addons/link_tracker/tests/test_tracker_http_requests.py
addons/link_tracker/tools/__init__.py
addons/link_tracker/tools/html.py
addons/loyalty/__init__.py
addons/loyalty/__manifest__.py
addons/loyalty/controllers/__init__.py
addons/loyalty/controllers/portal.py
addons/loyalty/models/__init__.py
addons/loyalty/models/loyalty_card.py
addons/loyalty/models/loyalty_history.py
addons/loyalty/models/loyalty_mail.py
addons/loyalty/models/loyalty_program.py
addons/loyalty/models/loyalty_reward.py
addons/loyalty/models/loyalty_rule.py
addons/loyalty/models/product_pricelist.py
addons/loyalty/models/product_product.py
addons/loyalty/models/product_template.py
addons/loyalty/models/res_partner.py
addons/loyalty/tests/__init__.py
addons/loyalty/tests/test_loyalty.py
addons/loyalty/wizard/__init__.py
addons/loyalty/wizard/base_partner_merge.py
addons/loyalty/wizard/loyalty_card_update_balance.py
addons/loyalty/wizard/loyalty_generate_wizard.py
addons/lunch/__init__.py
addons/lunch/__manifest__.py
addons/lunch/controllers/__init__.py
addons/lunch/controllers/main.py
addons/lunch/models/__init__.py
addons/lunch/models/lunch_alert.py
addons/lunch/models/lunch_cashmove.py
addons/lunch/models/lunch_location.py
addons/lunch/models/lunch_order.py
addons/lunch/models/lunch_product.py
addons/lunch/models/lunch_product_category.py
addons/lunch/models/lunch_supplier.py
addons/lunch/models/lunch_topping.py
addons/lunch/models/res_company.py
addons/lunch/models/res_config_settings.py
addons/lunch/models/res_users.py
addons/lunch/report/__init__.py
addons/lunch/report/lunch_cashmove_report.py
addons/lunch/tests/__init__.py
addons/lunch/tests/common.py
addons/lunch/tests/test_alert.py
addons/lunch/tests/test_supplier.py
addons/lunch/tests/test_ui.py
addons/mail/__init__.py
addons/mail/__manifest__.py
addons/mail/controllers/__init__.py
addons/mail/controllers/attachment.py
addons/mail/controllers/google_translate.py
addons/mail/controllers/guest.py
addons/mail/controllers/im_status.py
addons/mail/controllers/link_preview.py
addons/mail/controllers/mail.py
addons/mail/controllers/mailbox.py
addons/mail/controllers/message_reaction.py
addons/mail/controllers/thread.py
addons/mail/controllers/webclient.py
addons/mail/controllers/webmanifest.py
addons/mail/controllers/websocket.py
addons/mail/controllers/discuss/__init__.py
addons/mail/controllers/discuss/channel.py
addons/mail/controllers/discuss/gif.py
addons/mail/controllers/discuss/public_page.py
addons/mail/controllers/discuss/rtc.py
addons/mail/controllers/discuss/search.py
addons/mail/controllers/discuss/settings.py
addons/mail/controllers/discuss/voice.py
addons/mail/models/__init__.py
addons/mail/models/fetchmail.py
addons/mail/models/ir_action_act_window.py
addons/mail/models/ir_actions_server.py
addons/mail/models/ir_attachment.py
addons/mail/models/ir_binary.py
addons/mail/models/ir_config_parameter.py
addons/mail/models/ir_cron.py
addons/mail/models/ir_http.py
addons/mail/models/ir_mail_server.py
addons/mail/models/ir_model.py
addons/mail/models/ir_model_fields.py
addons/mail/models/ir_qweb.py
addons/mail/models/ir_ui_menu.py
addons/mail/models/ir_ui_view.py
addons/mail/models/ir_websocket.py
addons/mail/models/mail_activity.py
addons/mail/models/mail_activity_mixin.py
addons/mail/models/mail_activity_plan.py
addons/mail/models/mail_activity_plan_template.py
addons/mail/models/mail_activity_type.py
addons/mail/models/mail_alias.py
addons/mail/models/mail_alias_domain.py
addons/mail/models/mail_alias_mixin.py
addons/mail/models/mail_alias_mixin_optional.py
addons/mail/models/mail_blacklist.py
addons/mail/models/mail_canned_response.py
addons/mail/models/mail_composer_mixin.py
addons/mail/models/mail_followers.py
addons/mail/models/mail_gateway_allowed.py
addons/mail/models/mail_ice_server.py
addons/mail/models/mail_link_preview.py
addons/mail/models/mail_mail.py
addons/mail/models/mail_message.py
addons/mail/models/mail_message_link_preview.py
addons/mail/models/mail_message_reaction.py
addons/mail/models/mail_message_schedule.py
addons/mail/models/mail_message_subtype.py
addons/mail/models/mail_message_translation.py
addons/mail/models/mail_notification.py
addons/mail/models/mail_presence.py
addons/mail/models/mail_push.py
addons/mail/models/mail_push_device.py
addons/mail/models/mail_render_mixin.py
addons/mail/models/mail_scheduled_message.py
addons/mail/models/mail_template.py
addons/mail/models/mail_thread.py
addons/mail/models/mail_thread_blacklist.py
addons/mail/models/mail_thread_cc.py
addons/mail/models/mail_thread_main_attachment.py
addons/mail/models/mail_tracking_duration_mixin.py
addons/mail/models/mail_tracking_value.py
addons/mail/models/models.py
addons/mail/models/res_company.py
addons/mail/models/res_config_settings.py
addons/mail/models/res_partner.py
addons/mail/models/res_role.py
addons/mail/models/res_users.py
addons/mail/models/res_users_settings.py
addons/mail/models/res_users_settings_volumes.py
addons/mail/models/template_reset_mixin.py
addons/mail/models/update.py
addons/mail/models/discuss/__init__.py
addons/mail/models/discuss/bus_listener_mixin.py
addons/mail/models/discuss/discuss_call_history.py
addons/mail/models/discuss/discuss_channel.py
addons/mail/models/discuss/discuss_channel_member.py
addons/mail/models/discuss/discuss_channel_rtc_session.py
addons/mail/models/discuss/discuss_gif_favorite.py
addons/mail/models/discuss/discuss_voice_metadata.py
addons/mail/models/discuss/ir_attachment.py
addons/mail/models/discuss/ir_binary.py
addons/mail/models/discuss/ir_websocket.py
addons/mail/models/discuss/mail_guest.py
addons/mail/models/discuss/mail_message.py
addons/mail/models/discuss/res_groups.py
addons/mail/models/discuss/res_partner.py
addons/mail/models/discuss/res_users.py
addons/mail/static/scripts/odoo-mailgate.py
addons/mail/tests/__init__.py
addons/mail/tests/common.py
addons/mail/tests/common_activity.py
addons/mail/tests/common_controllers.py
addons/mail/tests/common_tracking.py
addons/mail/tests/test_discuss_tools.py
addons/mail/tests/test_fetchmail.py
addons/mail/tests/test_font_to_img.py
addons/mail/tests/test_ir_mail_server.py
addons/mail/tests/test_ir_ui_menu.py
addons/mail/tests/test_ir_websocket.py
addons/mail/tests/test_link_preview.py
addons/mail/tests/test_mail_activity.py
addons/mail/tests/test_mail_composer.py
addons/mail/tests/test_mail_mail.py
addons/mail/tests/test_mail_message.py
addons/mail/tests/test_mail_message_translate.py
addons/mail/tests/test_mail_presence.py
addons/mail/tests/test_mail_render.py
addons/mail/tests/test_mail_template.py
addons/mail/tests/test_mail_tools.py
addons/mail/tests/test_res_partner.py
addons/mail/tests/test_res_role.py
addons/mail/tests/test_res_users.py
addons/mail/tests/test_uninstall.py
addons/mail/tests/test_update_notification.py
addons/mail/tests/test_websocket_controller.py
addons/mail/tests/discuss/__init__.py
addons/mail/tests/discuss/test_avatar_acl.py
addons/mail/tests/discuss/test_discuss_action.py
addons/mail/tests/discuss/test_discuss_attachment_controller.py
addons/mail/tests/discuss/test_discuss_binary_controller.py
addons/mail/tests/discuss/test_discuss_channel.py
addons/mail/tests/discuss/test_discuss_channel_access.py
addons/mail/tests/discuss/test_discuss_channel_as_guest.py
addons/mail/tests/discuss/test_discuss_channel_invite.py
addons/mail/tests/discuss/test_discuss_channel_member.py
addons/mail/tests/discuss/test_discuss_mail_presence.py
addons/mail/tests/discuss/test_discuss_message_update_controller.py
addons/mail/tests/discuss/test_discuss_reaction_controller.py
addons/mail/tests/discuss/test_discuss_res_role.py
addons/mail/tests/discuss/test_discuss_sub_channels.py
addons/mail/tests/discuss/test_discuss_thread_controller.py
addons/mail/tests/discuss/test_guest.py
addons/mail/tests/discuss/test_guest_feature.py
addons/mail/tests/discuss/test_load_messages.py
addons/mail/tests/discuss/test_message_controller.py
addons/mail/tests/discuss/test_rtc.py
addons/mail/tests/discuss/test_toggle_upload.py
addons/mail/tests/discuss/test_ui.py
addons/mail/tools/__init__.py
addons/mail/tools/alias_error.py
addons/mail/tools/discuss.py
addons/mail/tools/jwt.py
addons/mail/tools/link_preview.py
addons/mail/tools/mail_validation.py
addons/mail/tools/parser.py
addons/mail/tools/web_push.py
addons/mail/wizard/__init__.py
addons/mail/wizard/base_module_uninstall.py
addons/mail/wizard/base_partner_merge_automatic_wizard.py
addons/mail/wizard/mail_activity_schedule.py
addons/mail/wizard/mail_activity_schedule_summary.py
addons/mail/wizard/mail_blacklist_remove.py
addons/mail/wizard/mail_compose_message.py
addons/mail/wizard/mail_followers_edit.py
addons/mail/wizard/mail_template_preview.py
addons/mail/wizard/mail_template_reset.py
addons/mail_bot/__init__.py
addons/mail_bot/__manifest__.py
addons/mail_bot/controllers/__init__.py
addons/mail_bot/controllers/thread.py
addons/mail_bot/models/__init__.py
addons/mail_bot/models/discuss_channel.py
addons/mail_bot/models/mail_bot.py
addons/mail_bot/models/res_users.py
addons/mail_bot_hr/__init__.py
addons/mail_bot_hr/__manifest__.py
addons/mail_group/__init__.py
addons/mail_group/__manifest__.py
addons/mail_group/controllers/__init__.py
addons/mail_group/controllers/portal.py
addons/mail_group/models/__init__.py
addons/mail_group/models/mail_group.py
addons/mail_group/models/mail_group_member.py
addons/mail_group/models/mail_group_message.py
addons/mail_group/models/mail_group_moderation.py
addons/mail_group/tests/__init__.py
addons/mail_group/tests/common.py
addons/mail_group/tests/data.py
addons/mail_group/tests/test_mail_group.py
addons/mail_group/tests/test_mail_group_mailing.py
addons/mail_group/tests/test_mail_group_message.py
addons/mail_group/tests/test_mail_group_moderation.py
addons/mail_group/wizard/__init__.py
addons/mail_group/wizard/mail_group_message_reject.py
addons/mail_plugin/__init__.py
addons/mail_plugin/__manifest__.py
addons/mail_plugin/controllers/__init__.py
addons/mail_plugin/controllers/authenticate.py
addons/mail_plugin/controllers/mail_plugin.py
addons/mail_plugin/models/__init__.py
addons/mail_plugin/models/ir_http.py
addons/mail_plugin/models/res_partner.py
addons/mail_plugin/models/res_partner_iap.py
addons/mail_plugin/tests/__init__.py
addons/mail_plugin/tests/common.py
addons/mail_plugin/tests/test_controller.py
addons/mail_plugin/tests/test_res_partner_iap.py
addons/maintenance/__init__.py
addons/maintenance/__manifest__.py
addons/maintenance/models/__init__.py
addons/maintenance/models/maintenance.py
addons/maintenance/models/res_config_settings.py
addons/maintenance/tests/__init__.py
addons/maintenance/tests/test_calendar_with_recurrence.py
addons/maintenance/tests/test_maintenance.py
addons/maintenance/tests/test_maintenance_multicompany.py
addons/marketing_card/__init__.py
addons/marketing_card/__manifest__.py
addons/marketing_card/controllers/__init__.py
addons/marketing_card/controllers/marketing_card.py
addons/marketing_card/models/__init__.py
addons/marketing_card/models/card_campaign.py
addons/marketing_card/models/card_campaign_tag.py
addons/marketing_card/models/card_card.py
addons/marketing_card/models/card_template.py
addons/marketing_card/models/ir_model.py
addons/marketing_card/models/mailing_mailing.py
addons/marketing_card/models/utm_source.py
addons/marketing_card/tests/__init__.py
addons/marketing_card/tests/common.py
addons/marketing_card/tests/test_campaign.py
addons/marketing_card/wizards/__init__.py
addons/marketing_card/wizards/mail_compose_message.py
addons/mass_mailing/__init__.py
addons/mass_mailing/__manifest__.py
addons/mass_mailing/controllers/__init__.py
addons/mass_mailing/controllers/legacy.py
addons/mass_mailing/controllers/main.py
addons/mass_mailing/models/__init__.py
addons/mass_mailing/models/ir_http.py
addons/mass_mailing/models/ir_mail_server.py
addons/mass_mailing/models/ir_model.py
addons/mass_mailing/models/link_tracker.py
addons/mass_mailing/models/mail_blacklist.py
addons/mass_mailing/models/mail_mail.py
addons/mass_mailing/models/mail_render_mixin.py
addons/mass_mailing/models/mail_thread.py
addons/mass_mailing/models/mailing.py
addons/mass_mailing/models/mailing_contact.py
addons/mass_mailing/models/mailing_filter.py
addons/mass_mailing/models/mailing_list.py
addons/mass_mailing/models/mailing_subscription.py
addons/mass_mailing/models/mailing_subscription_optout.py
addons/mass_mailing/models/mailing_trace.py
addons/mass_mailing/models/res_company.py
addons/mass_mailing/models/res_config_settings.py
addons/mass_mailing/models/res_partner.py
addons/mass_mailing/models/res_users.py
addons/mass_mailing/models/utm_campaign.py
addons/mass_mailing/models/utm_medium.py
addons/mass_mailing/models/utm_source.py
addons/mass_mailing/report/__init__.py
addons/mass_mailing/report/mailing_trace_report.py
addons/mass_mailing/tests/__init__.py
addons/mass_mailing/tests/common.py
addons/mass_mailing/tests/test_mailing_ab_testing.py
addons/mass_mailing/tests/test_mailing_controllers.py
addons/mass_mailing/tests/test_mailing_internals.py
addons/mass_mailing/tests/test_mailing_list.py
addons/mass_mailing/tests/test_mailing_mailing_schedule_date.py
addons/mass_mailing/tests/test_mailing_retry.py
addons/mass_mailing/tests/test_mailing_ui.py
addons/mass_mailing/tests/test_utm.py
addons/mass_mailing/wizard/__init__.py
addons/mass_mailing/wizard/mail_compose_message.py
addons/mass_mailing/wizard/mailing_contact_import.py
addons/mass_mailing/wizard/mailing_contact_to_list.py
addons/mass_mailing/wizard/mailing_list_merge.py
addons/mass_mailing/wizard/mailing_mailing_schedule_date.py
addons/mass_mailing/wizard/mailing_mailing_test.py
addons/mass_mailing_crm/__init__.py
addons/mass_mailing_crm/__manifest__.py
addons/mass_mailing_crm/models/__init__.py
addons/mass_mailing_crm/models/crm_lead.py
addons/mass_mailing_crm/models/mailing_mailing.py
addons/mass_mailing_crm/models/utm.py
addons/mass_mailing_crm_sms/__init__.py
addons/mass_mailing_crm_sms/__manifest__.py
addons/mass_mailing_crm_sms/models/__init__.py
addons/mass_mailing_crm_sms/models/utm.py
addons/mass_mailing_event/__init__.py
addons/mass_mailing_event/__manifest__.py
addons/mass_mailing_event/models/__init__.py
addons/mass_mailing_event/models/event_event.py
addons/mass_mailing_event/models/event_registration.py
addons/mass_mailing_event_sms/__init__.py
addons/mass_mailing_event_sms/__manifest__.py
addons/mass_mailing_event_sms/models/__init__.py
addons/mass_mailing_event_sms/models/event.py
addons/mass_mailing_event_sms/tests/__init__.py
addons/mass_mailing_event_sms/tests/test_mailing_event.py
addons/mass_mailing_event_track/__init__.py
addons/mass_mailing_event_track/__manifest__.py
addons/mass_mailing_event_track/models/__init__.py
addons/mass_mailing_event_track/models/event_event.py
addons/mass_mailing_event_track/models/event_track.py
addons/mass_mailing_event_track_sms/__init__.py
addons/mass_mailing_event_track_sms/__manifest__.py
addons/mass_mailing_event_track_sms/models/__init__.py
addons/mass_mailing_event_track_sms/models/event.py
addons/mass_mailing_sale/__init__.py
addons/mass_mailing_sale/__manifest__.py
addons/mass_mailing_sale/models/__init__.py
addons/mass_mailing_sale/models/mailing_mailing.py
addons/mass_mailing_sale/models/sale_order.py
addons/mass_mailing_sale/models/utm.py
addons/mass_mailing_sale_sms/__init__.py
addons/mass_mailing_sale_sms/__manifest__.py
addons/mass_mailing_sale_sms/models/__init__.py
addons/mass_mailing_sale_sms/models/utm.py
addons/mass_mailing_slides/__init__.py
addons/mass_mailing_slides/__manifest__.py
addons/mass_mailing_slides/models/__init__.py
addons/mass_mailing_slides/models/slide_channel.py
addons/mass_mailing_sms/__init__.py
addons/mass_mailing_sms/__manifest__.py
addons/mass_mailing_sms/controllers/__init__.py
addons/mass_mailing_sms/controllers/main.py
addons/mass_mailing_sms/models/__init__.py
addons/mass_mailing_sms/models/mailing_contact.py
addons/mass_mailing_sms/models/mailing_list.py
addons/mass_mailing_sms/models/mailing_mailing.py
addons/mass_mailing_sms/models/mailing_trace.py
addons/mass_mailing_sms/models/res_users.py
addons/mass_mailing_sms/models/sms_sms.py
addons/mass_mailing_sms/models/sms_tracker.py
addons/mass_mailing_sms/models/utm.py
addons/mass_mailing_sms/tests/__init__.py
addons/mass_mailing_sms/tests/common.py
addons/mass_mailing_sms/tests/test_mailing_controllers.py
addons/mass_mailing_sms/tests/test_mailing_internals.py
addons/mass_mailing_sms/tests/test_mailing_list.py
addons/mass_mailing_sms/tests/test_mailing_retry.py
addons/mass_mailing_sms/tests/test_mailing_sms_ab_testing.py
addons/mass_mailing_sms/tests/test_mailing_ui.py
addons/mass_mailing_sms/wizard/__init__.py
addons/mass_mailing_sms/wizard/mailing_sms_test.py
addons/mass_mailing_sms/wizard/sms_composer.py
addons/mass_mailing_themes/__init__.py
addons/mass_mailing_themes/__manifest__.py
addons/microsoft_account/__init__.py
addons/microsoft_account/__manifest__.py
addons/microsoft_account/controllers/__init__.py
addons/microsoft_account/controllers/main.py
addons/microsoft_account/models/__init__.py
addons/microsoft_account/models/microsoft_service.py
addons/microsoft_account/models/res_users.py
addons/microsoft_calendar/__init__.py
addons/microsoft_calendar/__manifest__.py
addons/microsoft_calendar/controllers/__init__.py
addons/microsoft_calendar/controllers/main.py
addons/microsoft_calendar/models/__init__.py
addons/microsoft_calendar/models/calendar.py
addons/microsoft_calendar/models/calendar_alarm_manager.py
addons/microsoft_calendar/models/calendar_attendee.py
addons/microsoft_calendar/models/calendar_recurrence_rule.py
addons/microsoft_calendar/models/microsoft_sync.py
addons/microsoft_calendar/models/res_config_settings.py
addons/microsoft_calendar/models/res_users.py
addons/microsoft_calendar/models/res_users_settings.py
addons/microsoft_calendar/tests/__init__.py
addons/microsoft_calendar/tests/common.py
addons/microsoft_calendar/tests/test_answer_events.py
addons/microsoft_calendar/tests/test_create_events.py
addons/microsoft_calendar/tests/test_delete_events.py
addons/microsoft_calendar/tests/test_microsoft_event.py
addons/microsoft_calendar/tests/test_microsoft_service.py
addons/microsoft_calendar/tests/test_sync_odoo2microsoft_mail.py
addons/microsoft_calendar/tests/test_update_events.py
addons/microsoft_calendar/utils/__init__.py
addons/microsoft_calendar/utils/microsoft_calendar.py
addons/microsoft_calendar/utils/microsoft_event.py
addons/microsoft_calendar/wizard/__init__.py
addons/microsoft_calendar/wizard/reset_account.py
addons/microsoft_outlook/__init__.py
addons/microsoft_outlook/__manifest__.py
addons/microsoft_outlook/controllers/__init__.py
addons/microsoft_outlook/controllers/main.py
addons/microsoft_outlook/models/__init__.py
addons/microsoft_outlook/models/fetchmail_server.py
addons/microsoft_outlook/models/ir_mail_server.py
addons/microsoft_outlook/models/microsoft_outlook_mixin.py
addons/microsoft_outlook/models/res_config_settings.py
addons/microsoft_outlook/models/res_users.py
addons/microsoft_outlook/tests/__init__.py
addons/microsoft_outlook/tests/test_fetchmail_outlook.py
addons/mrp/__init__.py
addons/mrp/__manifest__.py
addons/mrp/controller/__init__.py
addons/mrp/controller/main.py
addons/mrp/models/__init__.py
addons/mrp/models/ir_attachment.py
addons/mrp/models/mrp_bom.py
addons/mrp/models/mrp_production.py
addons/mrp/models/mrp_routing.py
addons/mrp/models/mrp_unbuild.py
addons/mrp/models/mrp_workcenter.py
addons/mrp/models/mrp_workorder.py
addons/mrp/models/product.py
addons/mrp/models/product_document.py
addons/mrp/models/res_company.py
addons/mrp/models/res_config_settings.py
addons/mrp/models/stock_lot.py
addons/mrp/models/stock_move.py
addons/mrp/models/stock_move_line.py
addons/mrp/models/stock_orderpoint.py
addons/mrp/models/stock_picking.py
addons/mrp/models/stock_quant.py
addons/mrp/models/stock_reference.py
addons/mrp/models/stock_replenish_mixin.py
addons/mrp/models/stock_rule.py
addons/mrp/models/stock_scrap.py
addons/mrp/models/stock_traceability.py
addons/mrp/models/stock_warehouse.py
addons/mrp/report/__init__.py
addons/mrp/report/mrp_report_bom_structure.py
addons/mrp/report/mrp_report_mo_overview.py
addons/mrp/report/report_stock_reception.py
addons/mrp/report/report_stock_rule.py
addons/mrp/report/stock_forecasted.py
addons/mrp/tests/__init__.py
addons/mrp/tests/common.py
addons/mrp/tests/test_backorder.py
addons/mrp/tests/test_bom.py
addons/mrp/tests/test_byproduct.py
addons/mrp/tests/test_cancel_mo.py
addons/mrp/tests/test_consume_component.py
addons/mrp/tests/test_manual_consumption.py
addons/mrp/tests/test_multicompany.py
addons/mrp/tests/test_oee.py
addons/mrp/tests/test_order.py
addons/mrp/tests/test_performance.py
addons/mrp/tests/test_procurement.py
addons/mrp/tests/test_replenish.py
addons/mrp/tests/test_stock.py
addons/mrp/tests/test_stock_report.py
addons/mrp/tests/test_traceability.py
addons/mrp/tests/test_unbuild.py
addons/mrp/tests/test_warehouse_multistep_manufacturing.py
addons/mrp/tests/test_workcenter.py
addons/mrp/wizard/__init__.py
addons/mrp/wizard/change_production_qty.py
addons/mrp/wizard/mrp_consumption_warning.py
addons/mrp/wizard/mrp_production_backorder.py
addons/mrp/wizard/mrp_production_serial_numbers.py
addons/mrp/wizard/mrp_production_split.py
addons/mrp/wizard/product_replenish.py
addons/mrp/wizard/stock_label_type.py
addons/mrp/wizard/stock_replenishment_info.py
addons/mrp/wizard/stock_warn_insufficient_qty.py
addons/mrp_account/__init__.py
addons/mrp_account/__manifest__.py
addons/mrp_account/models/__init__.py
addons/mrp_account/models/account_move.py
addons/mrp_account/models/analytic_account.py
addons/mrp_account/models/mrp_production.py
addons/mrp_account/models/mrp_workcenter.py
addons/mrp_account/models/mrp_workorder.py
addons/mrp_account/models/product.py
addons/mrp_account/models/stock_move.py
addons/mrp_account/report/__init__.py
addons/mrp_account/report/mrp_report_mo_overview.py
addons/mrp_account/report/stock_valuation_report.py
addons/mrp_account/tests/__init__.py
addons/mrp_account/tests/test_analytic_account.py
addons/mrp_account/tests/test_bom_price.py
addons/mrp_account/tests/test_mrp_account.py
addons/mrp_account/tests/test_valuation_layers.py
addons/mrp_account/wizard/__init__.py
addons/mrp_account/wizard/mrp_wip_accounting.py
addons/mrp_landed_costs/__init__.py
addons/mrp_landed_costs/__manifest__.py
addons/mrp_landed_costs/models/__init__.py
addons/mrp_landed_costs/models/stock_landed_cost.py
addons/mrp_landed_costs/tests/__init__.py
addons/mrp_landed_costs/tests/test_stock_landed_costs_mrp.py
addons/mrp_product_expiry/__init__.py
addons/mrp_product_expiry/__manifest__.py
addons/mrp_product_expiry/models/__init__.py
addons/mrp_product_expiry/models/mrp_production.py
addons/mrp_product_expiry/tests/__init__.py
addons/mrp_product_expiry/tests/test_mrp_product_expiry.py
addons/mrp_product_expiry/wizard/__init__.py
addons/mrp_product_expiry/wizard/confirm_expiry.py
addons/mrp_repair/__init__.py
addons/mrp_repair/__manifest__.py
addons/mrp_repair/models/__init__.py
addons/mrp_repair/models/production.py
addons/mrp_repair/models/repair.py
addons/mrp_repair/models/stock_move.py
addons/mrp_repair/tests/__init__.py
addons/mrp_repair/tests/test_mrp_repair_flow.py
addons/mrp_repair/tests/test_tracability.py
addons/mrp_subcontracting/__init__.py
addons/mrp_subcontracting/__manifest__.py
addons/mrp_subcontracting/controllers/__init__.py
addons/mrp_subcontracting/controllers/portal.py
addons/mrp_subcontracting/models/__init__.py
addons/mrp_subcontracting/models/mrp_bom.py
addons/mrp_subcontracting/models/mrp_production.py
addons/mrp_subcontracting/models/product.py
addons/mrp_subcontracting/models/res_company.py
addons/mrp_subcontracting/models/res_partner.py
addons/mrp_subcontracting/models/stock_location.py
addons/mrp_subcontracting/models/stock_move.py
addons/mrp_subcontracting/models/stock_move_line.py
addons/mrp_subcontracting/models/stock_picking.py
addons/mrp_subcontracting/models/stock_quant.py
addons/mrp_subcontracting/models/stock_rule.py
addons/mrp_subcontracting/models/stock_warehouse.py
addons/mrp_subcontracting/report/__init__.py
addons/mrp_subcontracting/report/mrp_report_bom_structure.py
addons/mrp_subcontracting/tests/__init__.py
addons/mrp_subcontracting/tests/common.py
addons/mrp_subcontracting/tests/test_subcontracting.py
addons/mrp_subcontracting/tests/test_subcontracting_portal_ui.py
addons/mrp_subcontracting/wizard/__init__.py
addons/mrp_subcontracting/wizard/change_production_qty.py
addons/mrp_subcontracting/wizard/mrp_production_serial_numbers.py
addons/mrp_subcontracting/wizard/stock_picking_return.py
addons/mrp_subcontracting_account/__init__.py
addons/mrp_subcontracting_account/__manifest__.py
addons/mrp_subcontracting_account/models/__init__.py
addons/mrp_subcontracting_account/models/mrp_production.py
addons/mrp_subcontracting_account/models/product_product.py
addons/mrp_subcontracting_account/tests/__init__.py
addons/mrp_subcontracting_account/tests/test_subcontracting_account.py
addons/mrp_subcontracting_dropshipping/__init__.py
addons/mrp_subcontracting_dropshipping/__manifest__.py
addons/mrp_subcontracting_dropshipping/models/__init__.py
addons/mrp_subcontracting_dropshipping/models/purchase.py
addons/mrp_subcontracting_dropshipping/models/res_company.py
addons/mrp_subcontracting_dropshipping/models/stock_move.py
addons/mrp_subcontracting_dropshipping/models/stock_orderpoint.py
addons/mrp_subcontracting_dropshipping/models/stock_picking.py
addons/mrp_subcontracting_dropshipping/models/stock_replenish_mixin.py
addons/mrp_subcontracting_dropshipping/models/stock_rule.py
addons/mrp_subcontracting_dropshipping/models/stock_warehouse.py
addons/mrp_subcontracting_dropshipping/tests/__init__.py
addons/mrp_subcontracting_dropshipping/tests/test_anglo_saxon_valuation.py
addons/mrp_subcontracting_dropshipping/tests/test_purchase_subcontracting.py
addons/mrp_subcontracting_dropshipping/tests/test_sale_dropshipping.py
addons/mrp_subcontracting_landed_costs/__init__.py
addons/mrp_subcontracting_landed_costs/__manifest__.py
addons/mrp_subcontracting_landed_costs/tests/__init__.py
addons/mrp_subcontracting_landed_costs/tests/test_subcontracting_landed_costs.py
addons/mrp_subcontracting_purchase/__init__.py
addons/mrp_subcontracting_purchase/__manifest__.py
addons/mrp_subcontracting_purchase/models/__init__.py
addons/mrp_subcontracting_purchase/models/account_move_line.py
addons/mrp_subcontracting_purchase/models/product_product.py
addons/mrp_subcontracting_purchase/models/purchase_order.py
addons/mrp_subcontracting_purchase/models/stock_move.py
addons/mrp_subcontracting_purchase/models/stock_picking.py
addons/mrp_subcontracting_purchase/models/stock_rule.py
addons/mrp_subcontracting_purchase/report/__init__.py
addons/mrp_subcontracting_purchase/report/mrp_report_bom_structure.py
addons/mrp_subcontracting_purchase/tests/__init__.py
addons/mrp_subcontracting_purchase/tests/test_mrp_subcontracting_purchase.py
addons/mrp_subcontracting_repair/__init__.py
addons/mrp_subcontracting_repair/__manifest__.py
addons/onboarding/__init__.py
addons/onboarding/__manifest__.py
addons/onboarding/models/__init__.py
addons/onboarding/models/onboarding_onboarding.py
addons/onboarding/models/onboarding_onboarding_step.py
addons/onboarding/models/onboarding_progress.py
addons/onboarding/models/onboarding_progress_step.py
addons/onboarding/tests/__init__.py
addons/onboarding/tests/case.py
addons/onboarding/tests/common.py
addons/onboarding/tests/test_onboarding.py
addons/onboarding/tests/test_onboarding_concurrency.py
addons/partner_autocomplete/__init__.py
addons/partner_autocomplete/__manifest__.py
addons/partner_autocomplete/models/__init__.py
addons/partner_autocomplete/models/iap_autocomplete_api.py
addons/partner_autocomplete/models/ir_http.py
addons/partner_autocomplete/models/res_company.py
addons/partner_autocomplete/models/res_config_settings.py
addons/partner_autocomplete/models/res_partner.py
addons/partner_autocomplete/tests/__init__.py
addons/partner_autocomplete/tests/common.py
addons/partner_autocomplete/tests/test_res_company.py
addons/partnership/__init__.py
addons/partnership/__manifest__.py
addons/partnership/models/__init__.py
addons/partnership/models/product_pricelist.py
addons/partnership/models/product_template.py
addons/partnership/models/res_company.py
addons/partnership/models/res_config_settings.py
addons/partnership/models/res_partner.py
addons/partnership/models/res_partner_grade.py
addons/partnership/models/sale_order.py
addons/partnership/tests/__init__.py
addons/partnership/tests/common.py
addons/partnership/tests/test_partnership.py
addons/payment/__init__.py
addons/payment/__manifest__.py
addons/payment/const.py
addons/payment/logging.py
addons/payment/utils.py
addons/payment/controllers/__init__.py
addons/payment/controllers/portal.py
addons/payment/controllers/post_processing.py
addons/payment/models/__init__.py
addons/payment/models/ir_http.py
addons/payment/models/payment_method.py
addons/payment/models/payment_provider.py
addons/payment/models/payment_token.py
addons/payment/models/payment_transaction.py
addons/payment/models/res_company.py
addons/payment/models/res_country.py
addons/payment/models/res_partner.py
addons/payment/tests/__init__.py
addons/payment/tests/common.py
addons/payment/tests/http_common.py
addons/payment/tests/test_flows.py
addons/payment/tests/test_multicompany_flows.py
addons/payment/tests/test_payment_capture_wizard.py
addons/payment/tests/test_payment_method.py
addons/payment/tests/test_payment_provider.py
addons/payment/tests/test_payment_token.py
addons/payment/tests/test_payment_transaction.py
addons/payment/tests/test_res_company.py
addons/payment/wizards/__init__.py
addons/payment/wizards/payment_capture_wizard.py
addons/payment/wizards/payment_link_wizard.py
addons/payment/wizards/res_config_settings.py
addons/payment_adyen/__init__.py
addons/payment_adyen/__manifest__.py
addons/payment_adyen/const.py
addons/payment_adyen/utils.py
addons/payment_adyen/controllers/__init__.py
addons/payment_adyen/controllers/main.py
addons/payment_adyen/models/__init__.py
addons/payment_adyen/models/payment_provider.py
addons/payment_adyen/models/payment_token.py
addons/payment_adyen/models/payment_transaction.py
addons/payment_adyen/tests/__init__.py
addons/payment_adyen/tests/common.py
addons/payment_adyen/tests/test_adyen.py
addons/payment_adyen/wizards/__init__.py
addons/payment_adyen/wizards/payment_capture_wizard.py
addons/payment_aps/__init__.py
addons/payment_aps/__manifest__.py
addons/payment_aps/const.py
addons/payment_aps/utils.py
addons/payment_aps/controllers/__init__.py
addons/payment_aps/controllers/main.py
addons/payment_aps/models/__init__.py
addons/payment_aps/models/payment_provider.py
addons/payment_aps/models/payment_transaction.py
addons/payment_aps/tests/__init__.py
addons/payment_aps/tests/common.py
addons/payment_aps/tests/test_payment_transaction.py
addons/payment_aps/tests/test_processing_flows.py
addons/payment_asiapay/__init__.py
addons/payment_asiapay/__manifest__.py
addons/payment_asiapay/const.py
addons/payment_asiapay/controllers/__init__.py
addons/payment_asiapay/controllers/main.py
addons/payment_asiapay/models/__init__.py
addons/payment_asiapay/models/payment_provider.py
addons/payment_asiapay/models/payment_transaction.py
addons/payment_asiapay/tests/__init__.py
addons/payment_asiapay/tests/common.py
addons/payment_asiapay/tests/test_payment_provider.py
addons/payment_asiapay/tests/test_payment_transaction.py
addons/payment_asiapay/tests/test_processing_flows.py
addons/payment_authorize/__init__.py
addons/payment_authorize/__manifest__.py
addons/payment_authorize/const.py
addons/payment_authorize/controllers/__init__.py
addons/payment_authorize/controllers/main.py
addons/payment_authorize/models/__init__.py
addons/payment_authorize/models/authorize_request.py
addons/payment_authorize/models/payment_provider.py
addons/payment_authorize/models/payment_token.py
addons/payment_authorize/models/payment_transaction.py
addons/payment_authorize/tests/__init__.py
addons/payment_authorize/tests/common.py
addons/payment_authorize/tests/test_authorize.py
addons/payment_authorize/tests/test_refund_flows.py
addons/payment_buckaroo/__init__.py
addons/payment_buckaroo/__manifest__.py
addons/payment_buckaroo/const.py
addons/payment_buckaroo/controllers/__init__.py
addons/payment_buckaroo/controllers/main.py
addons/payment_buckaroo/models/__init__.py
addons/payment_buckaroo/models/payment_provider.py
addons/payment_buckaroo/models/payment_transaction.py
addons/payment_buckaroo/tests/__init__.py
addons/payment_buckaroo/tests/common.py
addons/payment_buckaroo/tests/test_buckaroo.py
addons/payment_custom/__init__.py
addons/payment_custom/__manifest__.py
addons/payment_custom/const.py
addons/payment_custom/controllers/__init__.py
addons/payment_custom/controllers/main.py
addons/payment_custom/models/__init__.py
addons/payment_custom/models/payment_provider.py
addons/payment_custom/models/payment_transaction.py
addons/payment_custom/tests/__init__.py
addons/payment_custom/tests/common.py
addons/payment_custom/tests/test_payment_transaction.py
addons/payment_demo/__init__.py
addons/payment_demo/__manifest__.py
addons/payment_demo/const.py
addons/payment_demo/controllers/__init__.py
addons/payment_demo/controllers/main.py
addons/payment_demo/models/__init__.py
addons/payment_demo/models/payment_provider.py
addons/payment_demo/models/payment_token.py
addons/payment_demo/models/payment_transaction.py
addons/payment_demo/tests/__init__.py
addons/payment_demo/tests/common.py
addons/payment_demo/tests/test_payment_transaction.py
addons/payment_demo/tests/test_processing_flows.py
addons/payment_dpo/__init__.py
addons/payment_dpo/__manifest__.py
addons/payment_dpo/const.py
addons/payment_dpo/controllers/__init__.py
addons/payment_dpo/controllers/main.py
addons/payment_dpo/models/__init__.py
addons/payment_dpo/models/payment_provider.py
addons/payment_dpo/models/payment_transaction.py
addons/payment_dpo/tests/__init__.py
addons/payment_dpo/tests/common.py
addons/payment_dpo/tests/test_payment_transaction.py
addons/payment_dpo/tests/test_processing_flows.py
addons/payment_flutterwave/__init__.py
addons/payment_flutterwave/__manifest__.py
addons/payment_flutterwave/const.py
addons/payment_flutterwave/controllers/__init__.py
addons/payment_flutterwave/controllers/main.py
addons/payment_flutterwave/models/__init__.py
addons/payment_flutterwave/models/payment_provider.py
addons/payment_flutterwave/models/payment_token.py
addons/payment_flutterwave/models/payment_transaction.py
addons/payment_flutterwave/tests/__init__.py
addons/payment_flutterwave/tests/common.py
addons/payment_flutterwave/tests/test_payment_provider.py
addons/payment_flutterwave/tests/test_payment_transaction.py
addons/payment_flutterwave/tests/test_processing_flows.py
addons/payment_iyzico/__init__.py
addons/payment_iyzico/__manifest__.py
addons/payment_iyzico/const.py
addons/payment_iyzico/controllers/__init__.py
addons/payment_iyzico/controllers/main.py
addons/payment_iyzico/models/__init__.py
addons/payment_iyzico/models/payment_provider.py
addons/payment_iyzico/models/payment_transaction.py
addons/payment_iyzico/tests/__init__.py
addons/payment_iyzico/tests/common.py
addons/payment_iyzico/tests/test_payment_provider.py
addons/payment_iyzico/tests/test_payment_transaction.py
addons/payment_iyzico/tests/test_processing_flows.py
addons/payment_mercado_pago/__init__.py
addons/payment_mercado_pago/__manifest__.py
addons/payment_mercado_pago/const.py
addons/payment_mercado_pago/controllers/__init__.py
addons/payment_mercado_pago/controllers/onboarding.py
addons/payment_mercado_pago/controllers/payment.py
addons/payment_mercado_pago/models/__init__.py
addons/payment_mercado_pago/models/payment_provider.py
addons/payment_mercado_pago/models/payment_token.py
addons/payment_mercado_pago/models/payment_transaction.py
addons/payment_mercado_pago/tests/__init__.py
addons/payment_mercado_pago/tests/common.py
addons/payment_mercado_pago/tests/test_payment_provider.py
addons/payment_mercado_pago/tests/test_payment_transaction.py
addons/payment_mercado_pago/tests/test_processing_flows.py
addons/payment_mollie/__init__.py
addons/payment_mollie/__manifest__.py
addons/payment_mollie/const.py
addons/payment_mollie/controllers/__init__.py
addons/payment_mollie/controllers/main.py
addons/payment_mollie/models/__init__.py
addons/payment_mollie/models/payment_provider.py
addons/payment_mollie/models/payment_transaction.py
addons/payment_mollie/tests/__init__.py
addons/payment_mollie/tests/common.py
addons/payment_mollie/tests/test_mollie.py
addons/payment_nuvei/__init__.py
addons/payment_nuvei/__manifest__.py
addons/payment_nuvei/const.py
addons/payment_nuvei/controllers/__init__.py
addons/payment_nuvei/controllers/main.py
addons/payment_nuvei/models/__init__.py
addons/payment_nuvei/models/payment_provider.py
addons/payment_nuvei/models/payment_transaction.py
addons/payment_nuvei/tests/__init__.py
addons/payment_nuvei/tests/common.py
addons/payment_nuvei/tests/test_payment_provider.py
addons/payment_nuvei/tests/test_payment_transaction.py
addons/payment_nuvei/tests/test_processing_flows.py
addons/payment_paymob/__init__.py
addons/payment_paymob/__manifest__.py
addons/payment_paymob/const.py
addons/payment_paymob/controllers/__init__.py
addons/payment_paymob/controllers/main.py
addons/payment_paymob/models/__init__.py
addons/payment_paymob/models/payment_provider.py
addons/payment_paymob/models/payment_transaction.py
addons/payment_paymob/tests/__init__.py
addons/payment_paymob/tests/common.py
addons/payment_paymob/tests/test_paymob.py
addons/payment_paypal/__init__.py
addons/payment_paypal/__manifest__.py
addons/payment_paypal/const.py
addons/payment_paypal/utils.py
addons/payment_paypal/controllers/__init__.py
addons/payment_paypal/controllers/main.py
addons/payment_paypal/models/__init__.py
addons/payment_paypal/models/payment_provider.py
addons/payment_paypal/models/payment_transaction.py
addons/payment_paypal/tests/__init__.py
addons/payment_paypal/tests/common.py
addons/payment_paypal/tests/test_paypal.py
addons/payment_razorpay/__init__.py
addons/payment_razorpay/__manifest__.py
addons/payment_razorpay/const.py
addons/payment_razorpay/controllers/__init__.py
addons/payment_razorpay/controllers/main.py
addons/payment_razorpay/controllers/onboarding.py
addons/payment_razorpay/models/__init__.py
addons/payment_razorpay/models/payment_provider.py
addons/payment_razorpay/models/payment_token.py
addons/payment_razorpay/models/payment_transaction.py
addons/payment_razorpay/tests/__init__.py
addons/payment_razorpay/tests/common.py
addons/payment_razorpay/tests/test_payment_provider.py
addons/payment_razorpay/tests/test_payment_transaction.py
addons/payment_razorpay/tests/test_processing_flows.py
addons/payment_redsys/__init__.py
addons/payment_redsys/__manifest__.py
addons/payment_redsys/const.py
addons/payment_redsys/controllers/__init__.py
addons/payment_redsys/controllers/main.py
addons/payment_redsys/models/__init__.py
addons/payment_redsys/models/payment_provider.py
addons/payment_redsys/models/payment_transaction.py
addons/payment_redsys/tests/__init__.py
addons/payment_redsys/tests/common.py
addons/payment_redsys/tests/test_payment_transaction.py
addons/payment_redsys/tests/test_processing_flows.py
addons/payment_stripe/__init__.py
addons/payment_stripe/__manifest__.py
addons/payment_stripe/const.py
addons/payment_stripe/utils.py
addons/payment_stripe/controllers/__init__.py
addons/payment_stripe/controllers/main.py
addons/payment_stripe/controllers/onboarding.py
addons/payment_stripe/models/__init__.py
addons/payment_stripe/models/payment_provider.py
addons/payment_stripe/models/payment_token.py
addons/payment_stripe/models/payment_transaction.py
addons/payment_stripe/tests/__init__.py
addons/payment_stripe/tests/common.py
addons/payment_stripe/tests/test_refund_flows.py
addons/payment_stripe/tests/test_stripe.py
addons/payment_worldline/__init__.py
addons/payment_worldline/__manifest__.py
addons/payment_worldline/const.py
addons/payment_worldline/controllers/__init__.py
addons/payment_worldline/controllers/main.py
addons/payment_worldline/models/__init__.py
addons/payment_worldline/models/payment_provider.py
addons/payment_worldline/models/payment_transaction.py
addons/payment_worldline/tests/__init__.py
addons/payment_worldline/tests/common.py
addons/payment_worldline/tests/test_worldline.py
addons/payment_xendit/__init__.py
addons/payment_xendit/__manifest__.py
addons/payment_xendit/const.py
addons/payment_xendit/controllers/__init__.py
addons/payment_xendit/controllers/main.py
addons/payment_xendit/models/__init__.py
addons/payment_xendit/models/payment_provider.py
addons/payment_xendit/models/payment_transaction.py
addons/payment_xendit/tests/__init__.py
addons/payment_xendit/tests/common.py
addons/payment_xendit/tests/test_payment_provider.py
addons/payment_xendit/tests/test_payment_transaction.py
addons/payment_xendit/tests/test_processing_flows.py
addons/phone_validation/__init__.py
addons/phone_validation/__manifest__.py
addons/phone_validation/lib/__init__.py
addons/phone_validation/lib/phonemetadata.py
addons/phone_validation/lib/phonenumbers_patch/__init__.py
addons/phone_validation/lib/phonenumbers_patch/region_BR.py
addons/phone_validation/lib/phonenumbers_patch/region_CI.py
addons/phone_validation/lib/phonenumbers_patch/region_CO.py
addons/phone_validation/lib/phonenumbers_patch/region_IL.py
addons/phone_validation/lib/phonenumbers_patch/region_KE.py
addons/phone_validation/lib/phonenumbers_patch/region_MA.py
addons/phone_validation/lib/phonenumbers_patch/region_MU.py
addons/phone_validation/lib/phonenumbers_patch/region_PA.py
addons/phone_validation/lib/phonenumbers_patch/region_SN.py
addons/phone_validation/models/__init__.py
addons/phone_validation/models/mail_thread_phone.py
addons/phone_validation/models/models.py
addons/phone_validation/models/phone_blacklist.py
addons/phone_validation/models/res_partner.py
addons/phone_validation/models/res_users.py
addons/phone_validation/tests/__init__.py
addons/phone_validation/tests/test_phonenumbers.py
addons/phone_validation/tests/test_phonenumbers_blacklist.py
addons/phone_validation/tests/test_phonenumbers_patch.py
addons/phone_validation/tools/__init__.py
addons/phone_validation/tools/phone_validation.py
addons/phone_validation/wizard/__init__.py
addons/phone_validation/wizard/phone_blacklist_remove.py
addons/point_of_sale/__init__.py
addons/point_of_sale/__manifest__.py
addons/point_of_sale/controllers/__init__.py
addons/point_of_sale/controllers/customer_display.py
addons/point_of_sale/controllers/main.py
addons/point_of_sale/models/__init__.py
addons/point_of_sale/models/account_bank_statement.py
addons/point_of_sale/models/account_cash_rounding.py
addons/point_of_sale/models/account_fiscal_position.py
addons/point_of_sale/models/account_journal.py
addons/point_of_sale/models/account_move.py
addons/point_of_sale/models/account_payment.py
addons/point_of_sale/models/account_tax.py
addons/point_of_sale/models/account_tax_group.py
addons/point_of_sale/models/barcode_rule.py
addons/point_of_sale/models/binary.py
addons/point_of_sale/models/decimal_precision.py
addons/point_of_sale/models/digest.py
addons/point_of_sale/models/ir_http.py
addons/point_of_sale/models/ir_module_module.py
addons/point_of_sale/models/ir_sequence.py
addons/point_of_sale/models/pos_bill.py
addons/point_of_sale/models/pos_bus_mixin.py
addons/point_of_sale/models/pos_category.py
addons/point_of_sale/models/pos_config.py
addons/point_of_sale/models/pos_load_mixin.py
addons/point_of_sale/models/pos_note.py
addons/point_of_sale/models/pos_order.py
addons/point_of_sale/models/pos_payment.py
addons/point_of_sale/models/pos_payment_method.py
addons/point_of_sale/models/pos_preset.py
addons/point_of_sale/models/pos_printer.py
addons/point_of_sale/models/pos_session.py
addons/point_of_sale/models/product_attribute.py
addons/point_of_sale/models/product_category.py
addons/point_of_sale/models/product_combo.py
addons/point_of_sale/models/product_combo_item.py
addons/point_of_sale/models/product_pricelist.py
addons/point_of_sale/models/product_product.py
addons/point_of_sale/models/product_tag.py
addons/point_of_sale/models/product_template.py
addons/point_of_sale/models/product_uom.py
addons/point_of_sale/models/report_sale_details.py
addons/point_of_sale/models/res_company.py
addons/point_of_sale/models/res_config_settings.py
addons/point_of_sale/models/res_country.py
addons/point_of_sale/models/res_country_state.py
addons/point_of_sale/models/res_currency.py
addons/point_of_sale/models/res_lang.py
addons/point_of_sale/models/res_partner.py
addons/point_of_sale/models/res_users.py
addons/point_of_sale/models/resource_calendar_attendance.py
addons/point_of_sale/models/stock_picking.py
addons/point_of_sale/models/stock_reference.py
addons/point_of_sale/models/stock_warehouse.py
addons/point_of_sale/models/uom.py
addons/point_of_sale/report/__init__.py
addons/point_of_sale/report/pos_invoice.py
addons/point_of_sale/report/pos_order_report.py
addons/point_of_sale/tests/__init__.py
addons/point_of_sale/tests/common.py
addons/point_of_sale/tests/common_setup_methods.py
addons/point_of_sale/tests/test_anglo_saxon.py
addons/point_of_sale/tests/test_frontend.py
addons/point_of_sale/tests/test_performances.py
addons/point_of_sale/tests/test_point_of_sale.py
addons/point_of_sale/tests/test_point_of_sale_flow.py
addons/point_of_sale/tests/test_point_of_sale_ui.py
addons/point_of_sale/tests/test_pos_basic_config.py
addons/point_of_sale/tests/test_pos_cash_rounding.py
addons/point_of_sale/tests/test_pos_controller.py
addons/point_of_sale/tests/test_pos_invoice_consolidation.py
addons/point_of_sale/tests/test_pos_margin.py
addons/point_of_sale/tests/test_pos_multiple_receivable_accounts.py
addons/point_of_sale/tests/test_pos_other_currency_config.py
addons/point_of_sale/tests/test_pos_product_variants.py
addons/point_of_sale/tests/test_pos_products_with_tax.py
addons/point_of_sale/tests/test_pos_setup.py
addons/point_of_sale/tests/test_pos_simple_invoiced_orders.py
addons/point_of_sale/tests/test_pos_simple_orders.py
addons/point_of_sale/tests/test_pos_stock_account.py
addons/point_of_sale/tests/test_pos_with_fiscal_position.py
addons/point_of_sale/tests/test_report_pos_order.py
addons/point_of_sale/tests/test_report_session.py
addons/point_of_sale/tests/test_res_config_settings.py
addons/point_of_sale/wizard/__init__.py
addons/point_of_sale/wizard/pos_close_session_wizard.py
addons/point_of_sale/wizard/pos_confirmation_wizard.py
addons/point_of_sale/wizard/pos_daily_sales_reports.py
addons/point_of_sale/wizard/pos_details.py
addons/point_of_sale/wizard/pos_make_invoice.py
addons/point_of_sale/wizard/pos_payment.py
addons/portal/__init__.py
addons/portal/__manifest__.py
addons/portal/utils.py
addons/portal/controllers/__init__.py
addons/portal/controllers/mail.py
addons/portal/controllers/message_reaction.py
addons/portal/controllers/portal.py
addons/portal/controllers/portal_thread.py
addons/portal/controllers/thread.py
addons/portal/controllers/web.py
addons/portal/models/__init__.py
addons/portal/models/ir_http.py
addons/portal/models/ir_qweb.py
addons/portal/models/ir_ui_view.py
addons/portal/models/mail_message.py
addons/portal/models/mail_thread.py
addons/portal/models/portal_mixin.py
addons/portal/models/res_config_settings.py
addons/portal/models/res_partner.py
addons/portal/models/res_users_apikeys_description.py
addons/portal/tests/__init__.py
addons/portal/tests/test_addresses.py
addons/portal/tests/test_login.py
addons/portal/tests/test_message_format_portal.py
addons/portal/tests/test_pager.py
addons/portal/tests/test_portal.py
addons/portal/tests/test_portal_wizard.py
addons/portal/tests/test_tours.py
addons/portal/wizard/__init__.py
addons/portal/wizard/portal_share.py
addons/portal/wizard/portal_wizard.py
addons/portal_rating/__init__.py
addons/portal_rating/__manifest__.py
addons/portal_rating/controllers/__init__.py
addons/portal_rating/controllers/portal_chatter.py
addons/portal_rating/controllers/portal_rating.py
addons/portal_rating/models/__init__.py
addons/portal_rating/models/ir_http.py
addons/portal_rating/models/mail_message.py
addons/portal_rating/models/rating_rating.py
addons/pos_account_tax_python/__init__.py
addons/pos_account_tax_python/__manifest__.py
addons/pos_account_tax_python/models/__init__.py
addons/pos_account_tax_python/models/account_tax.py
addons/pos_adyen/__init__.py
addons/pos_adyen/__manifest__.py
addons/pos_adyen/controllers/__init__.py
addons/pos_adyen/controllers/main.py
addons/pos_adyen/models/__init__.py
addons/pos_adyen/models/pos_config.py
addons/pos_adyen/models/pos_payment_method.py
addons/pos_adyen/models/res_config_settings.py
addons/pos_adyen/tests/__init__.py
addons/pos_adyen/tests/test_basic.py
addons/pos_discount/__init__.py
addons/pos_discount/__manifest__.py
addons/pos_discount/models/__init__.py
addons/pos_discount/models/pos_config.py
addons/pos_discount/models/product_template.py
addons/pos_discount/models/res_config_settings.py
addons/pos_discount/tests/__init__.py
addons/pos_discount/tests/test_taxes_global_discount.py
addons/pos_event/__init__.py
addons/pos_event/__manifest__.py
addons/pos_event/models/__init__.py
addons/pos_event/models/event_event.py
addons/pos_event/models/event_question.py
addons/pos_event/models/event_question_answer.py
addons/pos_event/models/event_registration.py
addons/pos_event/models/event_registration_answer.py
addons/pos_event/models/event_slot.py
addons/pos_event/models/event_ticket.py
addons/pos_event/models/pos_config.py
addons/pos_event/models/pos_order.py
addons/pos_event/models/pos_order_line.py
addons/pos_event/models/pos_session.py
addons/pos_event/tests/__init__.py
addons/pos_event/tests/test_frontend.py
addons/pos_event_sale/__init__.py
addons/pos_event_sale/__manifest__.py
addons/pos_event_sale/models/__init__.py
addons/pos_event_sale/models/event_registration.py
addons/pos_event_sale/tests/__init__.py
addons/pos_event_sale/tests/test_frontend.py
addons/pos_glory_cash/__init__.py
addons/pos_glory_cash/__manifest__.py
addons/pos_glory_cash/models/__init__.py
addons/pos_glory_cash/models/pos_payment_method.py
addons/pos_hr/__init__.py
addons/pos_hr/__manifest__.py
addons/pos_hr/models/__init__.py
addons/pos_hr/models/account_bank_statement.py
addons/pos_hr/models/hr_employee.py
addons/pos_hr/models/pos_config.py
addons/pos_hr/models/pos_order.py
addons/pos_hr/models/pos_payment.py
addons/pos_hr/models/pos_session.py
addons/pos_hr/models/product_product.py
addons/pos_hr/models/res_config_settings.py
addons/pos_hr/models/single_employee_sales_report.py
addons/pos_hr/report/__init__.py
addons/pos_hr/report/pos_order_report.py
addons/pos_hr/tests/__init__.py
addons/pos_hr/tests/test_frontend.py
addons/pos_hr/wizard/__init__.py
addons/pos_hr/wizard/pos_daily_sales_reports.py
addons/pos_hr_restaurant/__init__.py
addons/pos_hr_restaurant/__manifest__.py
addons/pos_imin/__init__.py
addons/pos_imin/__manifest__.py
addons/pos_imin/models/__init__.py
addons/pos_imin/models/res_config_settings.py
addons/pos_loyalty/__init__.py
addons/pos_loyalty/__manifest__.py
addons/pos_loyalty/models/__init__.py
addons/pos_loyalty/models/barcode_rule.py
addons/pos_loyalty/models/loyalty_card.py
addons/pos_loyalty/models/loyalty_mail.py
addons/pos_loyalty/models/loyalty_program.py
addons/pos_loyalty/models/loyalty_reward.py
addons/pos_loyalty/models/loyalty_rule.py
addons/pos_loyalty/models/pos_config.py
addons/pos_loyalty/models/pos_order.py
addons/pos_loyalty/models/pos_order_line.py
addons/pos_loyalty/models/pos_session.py
addons/pos_loyalty/models/product_product.py
addons/pos_loyalty/models/product_template.py
addons/pos_loyalty/models/res_partner.py
addons/pos_loyalty/tests/__init__.py
addons/pos_loyalty/tests/common.py
addons/pos_loyalty/tests/test_frontend.py
addons/pos_loyalty/tests/test_loyalty_history.py
addons/pos_loyalty/tests/test_product_loading.py
addons/pos_loyalty/tests/test_unlink_reward.py
addons/pos_mercado_pago/__init__.py
addons/pos_mercado_pago/__manifest__.py
addons/pos_mercado_pago/controllers/__init__.py
addons/pos_mercado_pago/controllers/main.py
addons/pos_mercado_pago/models/__init__.py
addons/pos_mercado_pago/models/mercado_pago_pos_request.py
addons/pos_mercado_pago/models/pos_payment_method.py
addons/pos_mercado_pago/models/pos_session.py
addons/pos_mrp/__init__.py
addons/pos_mrp/__manifest__.py
addons/pos_mrp/models/__init__.py
addons/pos_mrp/models/pos_order.py
addons/pos_mrp/tests/__init__.py
addons/pos_mrp/tests/common.py
addons/pos_mrp/tests/test_frontend.py
addons/pos_mrp/tests/test_pos_mrp_flow.py
addons/pos_online_payment/__init__.py
addons/pos_online_payment/__manifest__.py
addons/pos_online_payment/controllers/__init__.py
addons/pos_online_payment/controllers/payment_portal.py
addons/pos_online_payment/models/__init__.py
addons/pos_online_payment/models/account_payment.py
addons/pos_online_payment/models/payment_transaction.py
addons/pos_online_payment/models/pos_config.py
addons/pos_online_payment/models/pos_order.py
addons/pos_online_payment/models/pos_payment.py
addons/pos_online_payment/models/pos_payment_method.py
addons/pos_online_payment/models/pos_session.py
addons/pos_online_payment/tests/__init__.py
addons/pos_online_payment/tests/online_payment_common.py
addons/pos_online_payment/tests/test_frontend.py
addons/pos_online_payment_self_order/__init__.py
addons/pos_online_payment_self_order/__manifest__.py
addons/pos_online_payment_self_order/controllers/__init__.py
addons/pos_online_payment_self_order/controllers/payment_portal.py
addons/pos_online_payment_self_order/models/__init__.py
addons/pos_online_payment_self_order/models/payment_transaction.py
addons/pos_online_payment_self_order/models/pos_config.py
addons/pos_online_payment_self_order/models/pos_order.py
addons/pos_online_payment_self_order/models/pos_payment_method.py
addons/pos_online_payment_self_order/models/res_config_settings.py
addons/pos_online_payment_self_order/tests/__init__.py
addons/pos_online_payment_self_order/tests/test_self_order_frontend.py
addons/pos_online_payment_self_order/tests/test_self_order_mobile.py
addons/pos_pine_labs/__init__.py
addons/pos_pine_labs/__manifest__.py
addons/pos_pine_labs/models/__init__.py
addons/pos_pine_labs/models/pine_labs_pos_request.py
addons/pos_pine_labs/models/pos_payment.py
addons/pos_pine_labs/models/pos_payment_method.py
addons/pos_qfpay/__init__.py
addons/pos_qfpay/__manifest__.py
addons/pos_qfpay/controllers/__init__.py
addons/pos_qfpay/controllers/main.py
addons/pos_qfpay/models/__init__.py
addons/pos_qfpay/models/pos_payment_method.py
addons/pos_qfpay/tests/__init__.py
addons/pos_qfpay/tests/test_basic.py
addons/pos_razorpay/__init__.py
addons/pos_razorpay/__manifest__.py
addons/pos_razorpay/models/__init__.py
addons/pos_razorpay/models/pos_payment.py
addons/pos_razorpay/models/pos_payment_method.py
addons/pos_razorpay/models/razorpay_pos_request.py
addons/pos_razorpay/tests/__init__.py
addons/pos_razorpay/tests/test_basic.py
addons/pos_repair/__init__.py
addons/pos_repair/__manifest__.py
addons/pos_repair/models/__init__.py
addons/pos_repair/models/sale_order_line.py
addons/pos_repair/models/stock_picking.py
addons/pos_repair/tests/__init__.py
addons/pos_repair/tests/test_frontend.py
addons/pos_restaurant/__init__.py
addons/pos_restaurant/__manifest__.py
addons/pos_restaurant/models/__init__.py
addons/pos_restaurant/models/pos_config.py
addons/pos_restaurant/models/pos_order.py
addons/pos_restaurant/models/pos_order_line.py
addons/pos_restaurant/models/pos_payment.py
addons/pos_restaurant/models/pos_preset.py
addons/pos_restaurant/models/pos_restaurant.py
addons/pos_restaurant/models/pos_session.py
addons/pos_restaurant/models/res_config_settings.py
addons/pos_restaurant/models/restaurant_order_course.py
addons/pos_restaurant/tests/__init__.py
addons/pos_restaurant/tests/test_devices_synchronization.py
addons/pos_restaurant/tests/test_frontend.py
addons/pos_restaurant/tests/test_pos_restaurant_flow.py
addons/pos_restaurant_adyen/__init__.py
addons/pos_restaurant_adyen/__manifest__.py
addons/pos_restaurant_adyen/controllers/__init__.py
addons/pos_restaurant_adyen/controllers/main.py
addons/pos_restaurant_adyen/models/__init__.py
addons/pos_restaurant_adyen/models/pos_order.py
addons/pos_restaurant_adyen/models/pos_payment.py
addons/pos_restaurant_adyen/models/pos_payment_method.py
addons/pos_restaurant_loyalty/__init__.py
addons/pos_restaurant_loyalty/__manifest__.py
addons/pos_restaurant_loyalty/tests/__init__.py
addons/pos_restaurant_loyalty/tests/test_pos_restaurant_loyalty.py
addons/pos_restaurant_stripe/__init__.py
addons/pos_restaurant_stripe/__manifest__.py
addons/pos_restaurant_stripe/models/__init__.py
addons/pos_restaurant_stripe/models/pos_payment.py
addons/pos_sale/__init__.py
addons/pos_sale/__manifest__.py
addons/pos_sale/models/__init__.py
addons/pos_sale/models/account_move.py
addons/pos_sale/models/crm_team.py
addons/pos_sale/models/pos_config.py
addons/pos_sale/models/pos_order.py
addons/pos_sale/models/pos_session.py
addons/pos_sale/models/product_template.py
addons/pos_sale/models/res_config_settings.py
addons/pos_sale/models/res_partner.py
addons/pos_sale/models/sale_order.py
addons/pos_sale/models/stock_picking.py
addons/pos_sale/report/__init__.py
addons/pos_sale/report/sale_report.py
addons/pos_sale/tests/__init__.py
addons/pos_sale/tests/test_pos_sale_flow.py
addons/pos_sale/tests/test_pos_sale_lot.py
addons/pos_sale/tests/test_pos_sale_report.py
addons/pos_sale/tests/test_taxes_downpayment.py
addons/pos_sale_loyalty/__init__.py
addons/pos_sale_loyalty/__manifest__.py
addons/pos_sale_loyalty/models/__init__.py
addons/pos_sale_loyalty/models/sale_order.py
addons/pos_sale_loyalty/tests/__init__.py
addons/pos_sale_loyalty/tests/test_pos_sale_loyalty.py
addons/pos_sale_margin/__init__.py
addons/pos_sale_margin/__manifest__.py
addons/pos_sale_margin/report/__init__.py
addons/pos_sale_margin/report/sale_report.py
addons/pos_sale_margin/tests/__init__.py
addons/pos_sale_margin/tests/test_pos_sale_margin_report.py
addons/pos_self_order/__init__.py
addons/pos_self_order/__manifest__.py
addons/pos_self_order/controllers/__init__.py
addons/pos_self_order/controllers/orders.py
addons/pos_self_order/controllers/self_entry.py
addons/pos_self_order/controllers/webmanifest.py
addons/pos_self_order/models/__init__.py
addons/pos_self_order/models/ir_binary.py
addons/pos_self_order/models/ir_http.py
addons/pos_self_order/models/mail_template.py
addons/pos_self_order/models/pos_category.py
addons/pos_self_order/models/pos_config.py
addons/pos_self_order/models/pos_load_mixin.py
addons/pos_self_order/models/pos_order.py
addons/pos_self_order/models/pos_payment_method.py
addons/pos_self_order/models/pos_preset.py
addons/pos_self_order/models/pos_restaurant.py
addons/pos_self_order/models/pos_self_order_custom_link.py
addons/pos_self_order/models/pos_session.py
addons/pos_self_order/models/product_product.py
addons/pos_self_order/models/product_tag.py
addons/pos_self_order/models/res_config_settings.py
addons/pos_self_order/models/res_country.py
addons/pos_self_order/models/res_partner.py
addons/pos_self_order/tests/__init__.py
addons/pos_self_order/tests/self_order_common_test.py
addons/pos_self_order/tests/test_frontend.py
addons/pos_self_order/tests/test_self_order_attribute.py
addons/pos_self_order/tests/test_self_order_combo.py
addons/pos_self_order/tests/test_self_order_common.py
addons/pos_self_order/tests/test_self_order_controller.py
addons/pos_self_order/tests/test_self_order_kiosk.py
addons/pos_self_order/tests/test_self_order_mobile.py
addons/pos_self_order/tests/test_self_order_preset.py
addons/pos_self_order/tests/test_self_order_sequence.py
addons/pos_self_order/tests/test_webmanifest.py
addons/pos_self_order_adyen/__init__.py
addons/pos_self_order_adyen/__manifest__.py
addons/pos_self_order_adyen/controllers/__init__.py
addons/pos_self_order_adyen/controllers/main.py
addons/pos_self_order_adyen/models/__init__.py
addons/pos_self_order_adyen/models/pos_payment_method.py
addons/pos_self_order_pine_labs/__init__.py
addons/pos_self_order_pine_labs/__manifest__.py
addons/pos_self_order_pine_labs/controllers/__init__.py
addons/pos_self_order_pine_labs/controllers/orders.py
addons/pos_self_order_pine_labs/models/__init__.py
addons/pos_self_order_pine_labs/models/pos_payment_method.py
addons/pos_self_order_qfpay/__init__.py
addons/pos_self_order_qfpay/__manifest__.py
addons/pos_self_order_qfpay/models/__init__.py
addons/pos_self_order_qfpay/models/pos_config.py
addons/pos_self_order_qfpay/models/pos_payment_method.py
addons/pos_self_order_qfpay/tests/__init__.py
addons/pos_self_order_qfpay/tests/test_basic.py
addons/pos_self_order_razorpay/__init__.py
addons/pos_self_order_razorpay/__manifest__.py
addons/pos_self_order_razorpay/controllers/__init__.py
addons/pos_self_order_razorpay/controllers/orders.py
addons/pos_self_order_razorpay/models/__init__.py
addons/pos_self_order_razorpay/models/pos_payment_method.py
addons/pos_self_order_sale/__init__.py
addons/pos_self_order_sale/__manifest__.py
addons/pos_self_order_sale/models/__init__.py
addons/pos_self_order_sale/models/res_config_settings.py
addons/pos_self_order_stripe/__init__.py
addons/pos_self_order_stripe/__manifest__.py
addons/pos_self_order_stripe/controllers/__init__.py
addons/pos_self_order_stripe/controllers/orders.py
addons/pos_self_order_stripe/models/__init__.py
addons/pos_self_order_stripe/models/pos_payment_method.py
addons/pos_self_order_stripe/tests/__init__.py
addons/pos_self_order_stripe/tests/test_self_order_kiosk_stripe.py
addons/pos_sms/__init__.py
addons/pos_sms/__manifest__.py
addons/pos_sms/models/__init__.py
addons/pos_sms/models/pos_config.py
addons/pos_sms/models/pos_order.py
addons/pos_sms/models/res_config_settings.py
addons/pos_sms/tests/__init__.py
addons/pos_sms/tests/test_frontend.py
addons/pos_stripe/__init__.py
addons/pos_stripe/__manifest__.py
addons/pos_stripe/models/__init__.py
addons/pos_stripe/models/pos_payment_method.py
addons/pos_viva_com/__init__.py
addons/pos_viva_com/__manifest__.py
addons/pos_viva_com/controllers/__init__.py
addons/pos_viva_com/controllers/main.py
addons/pos_viva_com/models/__init__.py
addons/pos_viva_com/models/pos_payment.py
addons/pos_viva_com/models/pos_payment_method.py
addons/pos_viva_com/tests/__init__.py
addons/pos_viva_com/tests/test_frontend.py
addons/privacy_lookup/__init__.py
addons/privacy_lookup/__manifest__.py
addons/privacy_lookup/models/__init__.py
addons/privacy_lookup/models/privacy_log.py
addons/privacy_lookup/models/res_partner.py
addons/privacy_lookup/tests/__init__.py
addons/privacy_lookup/tests/test_privacy_wizard.py
addons/privacy_lookup/wizard/__init__.py
addons/privacy_lookup/wizard/privacy_lookup_wizard.py
addons/product/__init__.py
addons/product/__manifest__.py
addons/product/controllers/__init__.py
addons/product/controllers/catalog.py
addons/product/controllers/pricelist_report.py
addons/product/controllers/product_document.py
addons/product/models/__init__.py
addons/product/models/ir_attachment.py
addons/product/models/product_attribute.py
addons/product/models/product_attribute_custom_value.py
addons/product/models/product_attribute_value.py
addons/product/models/product_catalog_mixin.py
addons/product/models/product_category.py
addons/product/models/product_combo.py
addons/product/models/product_combo_item.py
addons/product/models/product_document.py
addons/product/models/product_pricelist.py
addons/product/models/product_pricelist_item.py
addons/product/models/product_product.py
addons/product/models/product_supplierinfo.py
addons/product/models/product_tag.py
addons/product/models/product_template.py
addons/product/models/product_template_attribute_exclusion.py
addons/product/models/product_template_attribute_line.py
addons/product/models/product_template_attribute_value.py
addons/product/models/product_uom.py
addons/product/models/res_company.py
addons/product/models/res_config_settings.py
addons/product/models/res_country_group.py
addons/product/models/res_currency.py
addons/product/models/res_partner.py
addons/product/models/uom_uom.py
addons/product/report/__init__.py
addons/product/report/product_label_report.py
addons/product/report/product_pricelist_report.py
addons/product/tests/__init__.py
addons/product/tests/common.py
addons/product/tests/test_barcode.py
addons/product/tests/test_common.py
addons/product/tests/test_import_files.py
addons/product/tests/test_name.py
addons/product/tests/test_pricelist.py
addons/product/tests/test_pricelist_auto_creation.py
addons/product/tests/test_product_attribute_value_config.py
addons/product/tests/test_product_combo.py
addons/product/tests/test_product_pricelist.py
addons/product/tests/test_product_rounding.py
addons/product/tests/test_seller.py
addons/product/tests/test_update_pav_wizard.py
addons/product/tests/test_variants.py
addons/product/wizard/__init__.py
addons/product/wizard/product_label_layout.py
addons/product/wizard/update_product_attribute_value.py
addons/product_email_template/__init__.py
addons/product_email_template/__manifest__.py
addons/product_email_template/models/__init__.py
addons/product_email_template/models/account_move.py
addons/product_email_template/models/product.py
addons/product_email_template/tests/__init__.py
addons/product_email_template/tests/test_account_move.py
addons/product_expiry/__init__.py
addons/product_expiry/__manifest__.py
addons/product_expiry/models/__init__.py
addons/product_expiry/models/product_product.py
addons/product_expiry/models/production_lot.py
addons/product_expiry/models/res_config_settings.py
addons/product_expiry/models/stock_move.py
addons/product_expiry/models/stock_move_line.py
addons/product_expiry/models/stock_picking.py
addons/product_expiry/models/stock_quant.py
addons/product_expiry/models/stock_rule.py
addons/product_expiry/report/__init__.py
addons/product_expiry/report/report_stock_quantity.py
addons/product_expiry/report/stock_forecasted.py
addons/product_expiry/tests/__init__.py
addons/product_expiry/tests/test_generate_serial_numbers.py
addons/product_expiry/tests/test_stock_lot.py
addons/product_expiry/wizard/__init__.py
addons/product_expiry/wizard/confirm_expiry.py
addons/product_margin/__init__.py
addons/product_margin/__manifest__.py
addons/product_margin/models/__init__.py
addons/product_margin/models/product_product.py
addons/product_margin/tests/__init__.py
addons/product_margin/tests/test_product_margin.py
addons/product_margin/wizard/__init__.py
addons/product_margin/wizard/product_margin.py
addons/product_matrix/__init__.py
addons/product_matrix/__manifest__.py
addons/product_matrix/models/__init__.py
addons/product_matrix/models/product_template.py
addons/product_matrix/tests/__init__.py
addons/product_matrix/tests/common.py
addons/project/__init__.py
addons/project/__manifest__.py
addons/project/controllers/__init__.py
addons/project/controllers/portal.py
addons/project/controllers/project_sharing_chatter.py
addons/project/models/__init__.py
addons/project/models/account_analytic_account.py
addons/project/models/digest_digest.py
addons/project/models/ir_ui_menu.py
addons/project/models/mail_message.py
addons/project/models/project_collaborator.py
addons/project/models/project_milestone.py
addons/project/models/project_project.py
addons/project/models/project_project_stage.py
addons/project/models/project_role.py
addons/project/models/project_tags.py
addons/project/models/project_task.py
addons/project/models/project_task_recurrence.py
addons/project/models/project_task_stage_personal.py
addons/project/models/project_task_type.py
addons/project/models/project_update.py
addons/project/models/res_config_settings.py
addons/project/models/res_partner.py
addons/project/models/res_users.py
addons/project/report/__init__.py
addons/project/report/project_report.py
addons/project/report/project_task_burndown_chart_report.py
addons/project/tests/__init__.py
addons/project/tests/test_access_rights.py
addons/project/tests/test_burndown_chart.py
addons/project/tests/test_import_files.py
addons/project/tests/test_multicompany.py
addons/project/tests/test_personal_stages.py
addons/project/tests/test_portal.py
addons/project/tests/test_project_base.py
addons/project/tests/test_project_config.py
addons/project/tests/test_project_flow.py
addons/project/tests/test_project_mail_features.py
addons/project/tests/test_project_milestone.py
addons/project/tests/test_project_profitability.py
addons/project/tests/test_project_recurrence.py
addons/project/tests/test_project_report.py
addons/project/tests/test_project_sharing.py
addons/project/tests/test_project_sharing_portal_access.py
addons/project/tests/test_project_sharing_ui.py
addons/project/tests/test_project_stage_multicompany.py
addons/project/tests/test_project_subtasks.py
addons/project/tests/test_project_tags_filter.py
addons/project/tests/test_project_task_mail_tracking_duration.py
addons/project/tests/test_project_task_quick_create.py
addons/project/tests/test_project_task_type.py
addons/project/tests/test_project_template.py
addons/project/tests/test_project_template_ui.py
addons/project/tests/test_project_ui.py
addons/project/tests/test_project_update_access_rights.py
addons/project/tests/test_project_update_flow.py
addons/project/tests/test_project_update_ui.py
addons/project/tests/test_task_dependencies.py
addons/project/tests/test_task_follow.py
addons/project/tests/test_task_link_preview_name.py
addons/project/tests/test_task_state.py
addons/project/tests/test_task_templates.py
addons/project/tests/test_task_templates_ui.py
addons/project/tests/test_task_tracking.py
addons/project/wizard/__init__.py
addons/project/wizard/portal_share.py
addons/project/wizard/project_project_stage_delete.py
addons/project/wizard/project_share_collaborator_wizard.py
addons/project/wizard/project_share_wizard.py
addons/project/wizard/project_task_share_wizard.py
addons/project/wizard/project_task_type_delete.py
addons/project/wizard/project_template_create_wizard.py
addons/project_account/__init__.py
addons/project_account/__manifest__.py
addons/project_account/models/__init__.py
addons/project_account/models/project_project.py
addons/project_account/tests/__init__.py
addons/project_account/tests/test_project_profitability.py
addons/project_hr_expense/__init__.py
addons/project_hr_expense/__manifest__.py
addons/project_hr_expense/models/__init__.py
addons/project_hr_expense/models/hr_expense.py
addons/project_hr_expense/models/project_project.py
addons/project_hr_expense/tests/__init__.py
addons/project_hr_expense/tests/test_analytics.py
addons/project_hr_expense/tests/test_project_profitability.py
addons/project_hr_skills/__init__.py
addons/project_hr_skills/__manifest__.py
addons/project_hr_skills/models/__init__.py
addons/project_hr_skills/models/project_task.py
addons/project_hr_skills/models/res_users.py
addons/project_hr_skills/report/__init__.py
addons/project_hr_skills/report/report_project_task_user.py
addons/project_mail_plugin/__init__.py
addons/project_mail_plugin/__manifest__.py
addons/project_mail_plugin/controllers/__init__.py
addons/project_mail_plugin/controllers/mail_plugin.py
addons/project_mail_plugin/controllers/project_client.py
addons/project_mail_plugin/tests/__init__.py
addons/project_mail_plugin/tests/test_controller.py
addons/project_mrp/__init__.py
addons/project_mrp/__manifest__.py
addons/project_mrp/models/__init__.py
addons/project_mrp/models/mrp_bom.py
addons/project_mrp/models/mrp_production.py
addons/project_mrp/models/project_project.py
addons/project_mrp/models/stock.py
addons/project_mrp_account/__init__.py
addons/project_mrp_account/__manifest__.py
addons/project_mrp_account/models/__init__.py
addons/project_mrp_account/models/mrp_production.py
addons/project_mrp_account/models/mrp_workorder.py
addons/project_mrp_account/models/project_project.py
addons/project_mrp_account/models/stock_move.py
addons/project_mrp_account/models/stock_rule.py
addons/project_mrp_account/tests/__init__.py
addons/project_mrp_account/tests/test_analytic_account.py
addons/project_mrp_account/tests/test_project_profitability.py
addons/project_mrp_account/tests/test_project_stock.py
addons/project_mrp_sale/__init__.py
addons/project_mrp_sale/__manifest__.py
addons/project_mrp_sale/tests/__init__.py
addons/project_mrp_sale/tests/test_sale_mrp_account.py
addons/project_mrp_stock_landed_costs/__init__.py
addons/project_mrp_stock_landed_costs/__manifest__.py
addons/project_mrp_stock_landed_costs/models/__init__.py
addons/project_mrp_stock_landed_costs/models/stock_landed_costs.py
addons/project_purchase/__init__.py
addons/project_purchase/__manifest__.py
addons/project_purchase/controllers/__init__.py
addons/project_purchase/controllers/catalog.py
addons/project_purchase/models/__init__.py
addons/project_purchase/models/project_project.py
addons/project_purchase/models/purchase_order.py
addons/project_purchase/models/purchase_order_line.py
addons/project_purchase/tests/__init__.py
addons/project_purchase/tests/test_project_profitability.py
addons/project_purchase/tests/test_project_purchase.py
addons/project_purchase_stock/__init__.py
addons/project_purchase_stock/__manifest__.py
addons/project_purchase_stock/models/__init__.py
addons/project_purchase_stock/models/purchase_order.py
addons/project_purchase_stock/models/stock_rule.py
addons/project_purchase_stock/tests/__init__.py
addons/project_purchase_stock/tests/test_reordering_rule.py
addons/project_sale_expense/__init__.py
addons/project_sale_expense/__manifest__.py
addons/project_sale_expense/models/__init__.py
addons/project_sale_expense/models/account_move_line.py
addons/project_sale_expense/models/hr_expense.py
addons/project_sale_expense/models/project_project.py
addons/project_sale_expense/tests/__init__.py
addons/project_sale_expense/tests/test_project_profitability.py
addons/project_sale_expense/tests/test_project_sale_expense.py
addons/project_sms/__init__.py
addons/project_sms/__manifest__.py
addons/project_sms/models/__init__.py
addons/project_sms/models/project_project.py
addons/project_sms/models/project_stage.py
addons/project_sms/models/project_task.py
addons/project_sms/models/project_task_type.py
addons/project_sms/tests/__init__.py
addons/project_sms/tests/test_project_sharing.py
addons/project_stock/__init__.py
addons/project_stock/__manifest__.py
addons/project_stock/models/__init__.py
addons/project_stock/models/project_project.py
addons/project_stock/models/stock_picking.py
addons/project_stock_account/__init__.py
addons/project_stock_account/__manifest__.py
addons/project_stock_account/models/__init__.py
addons/project_stock_account/models/account_analytic_line.py
addons/project_stock_account/models/analytic_applicability.py
addons/project_stock_account/models/project_project.py
addons/project_stock_account/models/stock_move.py
addons/project_stock_account/models/stock_picking_type.py
addons/project_stock_account/tests/__init__.py
addons/project_stock_account/tests/test_analytics.py
addons/project_stock_landed_costs/__init__.py
addons/project_stock_landed_costs/__manifest__.py
addons/project_stock_landed_costs/models/__init__.py
addons/project_stock_landed_costs/models/stock_landed_costs.py
addons/project_timesheet_holidays/__init__.py
addons/project_timesheet_holidays/__manifest__.py
addons/project_timesheet_holidays/models/__init__.py
addons/project_timesheet_holidays/models/account_analytic.py
addons/project_timesheet_holidays/models/hr_employee.py
addons/project_timesheet_holidays/models/hr_leave.py
addons/project_timesheet_holidays/models/project_task.py
addons/project_timesheet_holidays/models/res_company.py
addons/project_timesheet_holidays/models/res_config_settings.py
addons/project_timesheet_holidays/models/resource_calendar_leaves.py
addons/project_timesheet_holidays/tests/__init__.py
addons/project_timesheet_holidays/tests/test_cancel_time_off.py
addons/project_timesheet_holidays/tests/test_employee.py
addons/project_timesheet_holidays/tests/test_timesheet_global_time_off.py
addons/project_timesheet_holidays/tests/test_timesheet_holidays.py
addons/project_todo/__init__.py
addons/project_todo/__manifest__.py
addons/project_todo/models/__init__.py
addons/project_todo/models/project_task.py
addons/project_todo/models/res_users.py
addons/project_todo/tests/__init__.py
addons/project_todo/tests/test_access_rights.py
addons/project_todo/tests/test_mail_activity_todo_create.py
addons/project_todo/tests/test_todo_onboarding_for_users.py
addons/project_todo/tests/test_todo_quick_create.py
addons/project_todo/tests/test_todo_ui.py
addons/project_todo/wizard/__init__.py
addons/project_todo/wizard/mail_activity_todo_create.py
addons/purchase/__init__.py
addons/purchase/__manifest__.py
addons/purchase/controllers/__init__.py
addons/purchase/controllers/portal.py
addons/purchase/models/__init__.py
addons/purchase/models/account_invoice.py
addons/purchase/models/account_tax.py
addons/purchase/models/analytic_account.py
addons/purchase/models/analytic_applicability.py
addons/purchase/models/ir_actions_report.py
addons/purchase/models/product.py
addons/purchase/models/purchase_bill_line_match.py
addons/purchase/models/purchase_order.py
addons/purchase/models/purchase_order_line.py
addons/purchase/models/res_company.py
addons/purchase/models/res_config_settings.py
addons/purchase/models/res_partner.py
addons/purchase/report/__init__.py
addons/purchase/report/purchase_bill.py
addons/purchase/report/purchase_report.py
addons/purchase/tests/__init__.py
addons/purchase/tests/test_access_rights.py
addons/purchase/tests/test_accrued_purchase_orders.py
addons/purchase/tests/test_import_files.py
addons/purchase/tests/test_purchase.py
addons/purchase/tests/test_purchase_dashboard.py
addons/purchase/tests/test_purchase_downpayment.py
addons/purchase/tests/test_purchase_invoice.py
addons/purchase/tests/test_purchase_order_product_catalog.py
addons/purchase/tests/test_purchase_order_report.py
addons/purchase/tests/test_purchase_product_catalog.py
addons/purchase/wizard/__init__.py
addons/purchase/wizard/bill_to_po_wizard.py
addons/purchase_edi_ubl_bis3/__init__.py
addons/purchase_edi_ubl_bis3/__manifest__.py
addons/purchase_edi_ubl_bis3/models/__init__.py
addons/purchase_edi_ubl_bis3/models/purchase_edi_xml_ubl_bis3.py
addons/purchase_edi_ubl_bis3/models/purchase_order.py
addons/purchase_edi_ubl_bis3/tests/__init__.py
addons/purchase_edi_ubl_bis3/tests/test_account_move_import.py
addons/purchase_edi_ubl_bis3/tests/test_purchase_order_edi_gen.py
addons/purchase_mrp/__init__.py
addons/purchase_mrp/__manifest__.py
addons/purchase_mrp/models/__init__.py
addons/purchase_mrp/models/mrp_bom.py
addons/purchase_mrp/models/mrp_production.py
addons/purchase_mrp/models/purchase.py
addons/purchase_mrp/models/stock_move.py
addons/purchase_mrp/models/stock_rule.py
addons/purchase_mrp/report/__init__.py
addons/purchase_mrp/report/mrp_report_bom_structure.py
addons/purchase_mrp/report/mrp_report_mo_overview.py
addons/purchase_mrp/tests/__init__.py
addons/purchase_mrp/tests/test_anglo_saxon_valuation.py
addons/purchase_mrp/tests/test_purchase_mrp_flow.py
addons/purchase_mrp/tests/test_replenishment.py
addons/purchase_product_matrix/__init__.py
addons/purchase_product_matrix/__manifest__.py
addons/purchase_product_matrix/models/__init__.py
addons/purchase_product_matrix/models/purchase.py
addons/purchase_product_matrix/tests/__init__.py
addons/purchase_product_matrix/tests/test_purchase_matrix.py
addons/purchase_repair/__init__.py
addons/purchase_repair/__manifest__.py
addons/purchase_repair/models/__init__.py
addons/purchase_repair/models/purchase_order.py
addons/purchase_repair/models/repair_order.py
addons/purchase_repair/tests/__init__.py
addons/purchase_repair/tests/test_repair_purchase_flow.py
addons/purchase_requisition/__init__.py
addons/purchase_requisition/__manifest__.py
addons/purchase_requisition/models/__init__.py
addons/purchase_requisition/models/product.py
addons/purchase_requisition/models/purchase.py
addons/purchase_requisition/models/purchase_requisition.py
addons/purchase_requisition/models/res_config_settings.py
addons/purchase_requisition/tests/__init__.py
addons/purchase_requisition/tests/common.py
addons/purchase_requisition/tests/test_purchase_requisition.py
addons/purchase_requisition/wizard/__init__.py
addons/purchase_requisition/wizard/purchase_requisition_alternative_warning.py
addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py
addons/purchase_requisition_sale/__init__.py
addons/purchase_requisition_sale/__manifest__.py
addons/purchase_requisition_sale/tests/__init__.py
addons/purchase_requisition_sale/tests/test_purchase_requisition_sale.py
addons/purchase_requisition_sale/wizard/__init__.py
addons/purchase_requisition_sale/wizard/purchase_requisition_create_alternative.py
addons/purchase_requisition_stock/__init__.py
addons/purchase_requisition_stock/__manifest__.py
addons/purchase_requisition_stock/models/__init__.py
addons/purchase_requisition_stock/models/purchase.py
addons/purchase_requisition_stock/models/purchase_requisition.py
addons/purchase_requisition_stock/models/stock.py
addons/purchase_requisition_stock/tests/__init__.py
addons/purchase_requisition_stock/tests/test_purchase_requisition_stock.py
addons/purchase_requisition_stock/wizard/__init__.py
addons/purchase_requisition_stock/wizard/purchase_requisition_create_alternative.py
addons/purchase_stock/__init__.py
addons/purchase_stock/__manifest__.py
addons/purchase_stock/models/__init__.py
addons/purchase_stock/models/account_invoice.py
addons/purchase_stock/models/account_move_line.py
addons/purchase_stock/models/product.py
addons/purchase_stock/models/purchase_order.py
addons/purchase_stock/models/purchase_order_line.py
addons/purchase_stock/models/res_company.py
addons/purchase_stock/models/res_config_settings.py
addons/purchase_stock/models/res_partner.py
addons/purchase_stock/models/stock.py
addons/purchase_stock/models/stock_move.py
addons/purchase_stock/models/stock_reference.py
addons/purchase_stock/models/stock_replenish_mixin.py
addons/purchase_stock/models/stock_rule.py
addons/purchase_stock/report/__init__.py
addons/purchase_stock/report/purchase_report.py
addons/purchase_stock/report/report_stock_rule.py
addons/purchase_stock/report/stock_forecasted.py
addons/purchase_stock/report/stock_valuation_report.py
addons/purchase_stock/report/vendor_delay_report.py
addons/purchase_stock/tests/__init__.py
addons/purchase_stock/tests/common.py
addons/purchase_stock/tests/test_anglo_saxon_valuation_reconciliation.py
addons/purchase_stock/tests/test_average_price.py
addons/purchase_stock/tests/test_create_picking.py
addons/purchase_stock/tests/test_fifo_price.py
addons/purchase_stock/tests/test_fifo_returns.py
addons/purchase_stock/tests/test_lot_valuation.py
addons/purchase_stock/tests/test_move_cancel_propagation.py
addons/purchase_stock/tests/test_old_rules.py
addons/purchase_stock/tests/test_onchange_product.py
addons/purchase_stock/tests/test_product_template.py
addons/purchase_stock/tests/test_purchase_delete_order.py
addons/purchase_stock/tests/test_purchase_lead_time.py
addons/purchase_stock/tests/test_purchase_method.py
addons/purchase_stock/tests/test_purchase_order.py
addons/purchase_stock/tests/test_purchase_order_process.py
addons/purchase_stock/tests/test_purchase_order_suggest.py
addons/purchase_stock/tests/test_purchase_stock_accrued_entries.py
addons/purchase_stock/tests/test_purchase_stock_report.py
addons/purchase_stock/tests/test_reordering_rule.py
addons/purchase_stock/tests/test_replenish_wizard.py
addons/purchase_stock/tests/test_routes.py
addons/purchase_stock/tests/test_stockvaluation.py
addons/purchase_stock/tests/test_supplier.py
addons/purchase_stock/tests/test_uninstall.py
addons/purchase_stock/wizard/__init__.py
addons/purchase_stock/wizard/product_replenish.py
addons/purchase_stock/wizard/stock_replenishment_info.py
addons/rating/__init__.py
addons/rating/__manifest__.py
addons/rating/controllers/__init__.py
addons/rating/controllers/main.py
addons/rating/models/__init__.py
addons/rating/models/mail_message.py
addons/rating/models/mail_thread.py
addons/rating/models/rating.py
addons/rating/models/rating_data.py
addons/rating/models/rating_mixin.py
addons/rating/models/rating_parent_mixin.py
addons/rating/tests/__init__.py
addons/rating/tests/test_controller.py
addons/rating/tests/test_security.py
addons/repair/__init__.py
addons/repair/__manifest__.py
addons/repair/models/__init__.py
addons/repair/models/product.py
addons/repair/models/repair.py
addons/repair/models/sale_order.py
addons/repair/models/stock_lot.py
addons/repair/models/stock_move.py
addons/repair/models/stock_move_line.py
addons/repair/models/stock_picking.py
addons/repair/models/stock_traceability.py
addons/repair/models/stock_warehouse.py
addons/repair/report/__init__.py
addons/repair/report/stock_forecasted.py
addons/repair/tests/__init__.py
addons/repair/tests/test_anglo_saxon_valuation.py
addons/repair/tests/test_repair.py
addons/repair/tests/test_rules_installation.py
addons/repair/wizard/__init__.py
addons/repair/wizard/stock_warn_insufficient_qty.py
addons/resource/__init__.py
addons/resource/__manifest__.py
addons/resource/models/__init__.py
addons/resource/models/res_company.py
addons/resource/models/res_users.py
addons/resource/models/resource_calendar.py
addons/resource/models/resource_calendar_attendance.py
addons/resource/models/resource_calendar_leaves.py
addons/resource/models/resource_mixin.py
addons/resource/models/resource_resource.py
addons/resource/models/utils.py
addons/resource/tests/__init__.py
addons/resource/tests/test_flexible_resource_calendar.py
addons/resource/tests/test_resource_calendar.py
addons/resource/tests/test_utils.py
addons/resource_mail/__init__.py
addons/resource_mail/__manifest__.py
addons/resource_mail/models/__init__.py
addons/resource_mail/models/resource_resource.py
addons/rpc/__init__.py
addons/rpc/__manifest__.py
addons/rpc/controllers/__init__.py
addons/rpc/controllers/json2.py
addons/rpc/controllers/jsonrpc.py
addons/rpc/controllers/xmlrpc.py
addons/rpc/tests/__init__.py
addons/rpc/tests/test_xmlrpc.py
addons/sale/__init__.py
addons/sale/__manifest__.py
addons/sale/const.py
addons/sale/controllers/__init__.py
addons/sale/controllers/combo_configurator.py
addons/sale/controllers/portal.py
addons/sale/controllers/product_configurator.py
addons/sale/models/__init__.py
addons/sale/models/account_move.py
addons/sale/models/account_move_line.py
addons/sale/models/analytic.py
addons/sale/models/chart_template.py
addons/sale/models/crm_team.py
addons/sale/models/ir_actions_report.py
addons/sale/models/ir_config_parameter.py
addons/sale/models/payment_provider.py
addons/sale/models/payment_transaction.py
addons/sale/models/product_document.py
addons/sale/models/product_pricelist_item.py
addons/sale/models/product_product.py
addons/sale/models/product_template.py
addons/sale/models/res_company.py
addons/sale/models/res_partner.py
addons/sale/models/sale_order.py
addons/sale/models/sale_order_line.py
addons/sale/models/utm_campaign.py
addons/sale/report/__init__.py
addons/sale/report/account_invoice_report.py
addons/sale/report/sale_report.py
addons/sale/tests/__init__.py
addons/sale/tests/common.py
addons/sale/tests/product_configurator_common.py
addons/sale/tests/test_access_rights.py
addons/sale/tests/test_accrued_sale_orders.py
addons/sale/tests/test_common.py
addons/sale/tests/test_controllers.py
addons/sale/tests/test_credit_limit.py
addons/sale/tests/test_import_files.py
addons/sale/tests/test_ir_config_parameter.py
addons/sale/tests/test_onchange.py
addons/sale/tests/test_payment_flow.py
addons/sale/tests/test_product_attribute_value.py
addons/sale/tests/test_product_configurator_data.py
addons/sale/tests/test_sale_combo_configurator.py
addons/sale/tests/test_sale_flow.py
addons/sale/tests/test_sale_order.py
addons/sale/tests/test_sale_order_product_catalog.py
addons/sale/tests/test_sale_prices.py
addons/sale/tests/test_sale_product_attribute_value_config.py
addons/sale/tests/test_sale_product_template.py
addons/sale/tests/test_sale_refund.py
addons/sale/tests/test_sale_report.py
addons/sale/tests/test_sale_to_invoice.py
addons/sale/tests/test_taxes_downpayment.py
addons/sale/tests/test_taxes_global_discount.py
addons/sale/tests/test_taxes_tax_totals_summary.py
addons/sale/wizard/__init__.py
addons/sale/wizard/base_document_layout.py
addons/sale/wizard/mass_cancel_orders.py
addons/sale/wizard/payment_link_wizard.py
addons/sale/wizard/res_config_settings.py
addons/sale/wizard/sale_make_invoice_advance.py
addons/sale/wizard/sale_order_discount.py
addons/sale_crm/__init__.py
addons/sale_crm/__manifest__.py
addons/sale_crm/models/__init__.py
addons/sale_crm/models/crm_lead.py
addons/sale_crm/models/crm_team.py
addons/sale_crm/models/sale_order.py
addons/sale_crm/tests/__init__.py
addons/sale_crm/tests/test_crm_lead_convert_quotation.py
addons/sale_crm/tests/test_crm_lead_merge.py
addons/sale_crm/tests/test_res_partner.py
addons/sale_crm/tests/test_sale_crm.py
addons/sale_crm/wizard/__init__.py
addons/sale_crm/wizard/crm_opportunity_to_quotation.py
addons/sale_edi_ubl/__init__.py
addons/sale_edi_ubl/__manifest__.py
addons/sale_edi_ubl/models/__init__.py
addons/sale_edi_ubl/models/product_product.py
addons/sale_edi_ubl/models/sale_edi_xml_ubl_bis3.py
addons/sale_edi_ubl/models/sale_order.py
addons/sale_edi_ubl/tests/__init__.py
addons/sale_edi_ubl/tests/test_sale_order_edi_gen.py
addons/sale_expense/__init__.py
addons/sale_expense/__manifest__.py
addons/sale_expense/models/__init__.py
addons/sale_expense/models/account_move.py
addons/sale_expense/models/account_move_line.py
addons/sale_expense/models/hr_expense.py
addons/sale_expense/models/hr_expense_split.py
addons/sale_expense/models/product_template.py
addons/sale_expense/models/sale_order.py
addons/sale_expense/models/sale_order_line.py
addons/sale_expense/tests/__init__.py
addons/sale_expense/tests/test_reinvoice.py
addons/sale_expense/tests/test_sale_expense.py
addons/sale_expense_margin/__init__.py
addons/sale_expense_margin/__manifest__.py
addons/sale_expense_margin/models/__init__.py
addons/sale_expense_margin/models/account_move_line.py
addons/sale_expense_margin/models/sale_order_line.py
addons/sale_expense_margin/tests/__init__.py
addons/sale_expense_margin/tests/test_so_expense_purchase_price.py
addons/sale_gelato/__init__.py
addons/sale_gelato/__manifest__.py
addons/sale_gelato/const.py
addons/sale_gelato/utils.py
addons/sale_gelato/controlers/__init__.py
addons/sale_gelato/controlers/main.py
addons/sale_gelato/models/__init__.py
addons/sale_gelato/models/delivery_carrier.py
addons/sale_gelato/models/product_document.py
addons/sale_gelato/models/product_product.py
addons/sale_gelato/models/product_template.py
addons/sale_gelato/models/res_company.py
addons/sale_gelato/models/res_partner.py
addons/sale_gelato/models/sale_order.py
addons/sale_gelato/models/sale_order_line.py
addons/sale_gelato/wizards/__init__.py
addons/sale_gelato/wizards/res_config_settings.py
addons/sale_gelato_stock/__init__.py
addons/sale_gelato_stock/__manifest__.py
addons/sale_gelato_stock/models/__init__.py
addons/sale_gelato_stock/models/sale_order_line.py
addons/sale_loyalty/__init__.py
addons/sale_loyalty/__manifest__.py
addons/sale_loyalty/models/__init__.py
addons/sale_loyalty/models/loyalty_card.py
addons/sale_loyalty/models/loyalty_history.py
addons/sale_loyalty/models/loyalty_program.py
addons/sale_loyalty/models/loyalty_reward.py
addons/sale_loyalty/models/sale_order.py
addons/sale_loyalty/models/sale_order_coupon_points.py
addons/sale_loyalty/models/sale_order_line.py
addons/sale_loyalty/tests/__init__.py
addons/sale_loyalty/tests/common.py
addons/sale_loyalty/tests/test_buy_gift_card.py
addons/sale_loyalty/tests/test_loyalty.py
addons/sale_loyalty/tests/test_loyalty_history.py
addons/sale_loyalty/tests/test_pay_with_gift_card.py
addons/sale_loyalty/tests/test_program_multi_company.py
addons/sale_loyalty/tests/test_program_numbers.py
addons/sale_loyalty/tests/test_program_rules.py
addons/sale_loyalty/tests/test_program_with_code_operations.py
addons/sale_loyalty/tests/test_program_without_code_operations.py
addons/sale_loyalty/tests/test_sale_auto_invoice.py
addons/sale_loyalty/tests/test_sale_invoicing.py
addons/sale_loyalty/tests/test_unlink_reward.py
addons/sale_loyalty/wizard/__init__.py
addons/sale_loyalty/wizard/sale_loyalty_coupon_wizard.py
addons/sale_loyalty/wizard/sale_loyalty_reward_wizard.py
addons/sale_loyalty_delivery/__init__.py
addons/sale_loyalty_delivery/__manifest__.py
addons/sale_loyalty_delivery/models/__init__.py
addons/sale_loyalty_delivery/models/loyalty_program.py
addons/sale_loyalty_delivery/models/loyalty_reward.py
addons/sale_loyalty_delivery/models/sale_order.py
addons/sale_loyalty_delivery/tests/__init__.py
addons/sale_loyalty_delivery/tests/test_free_shipping_reward.py
addons/sale_loyalty_delivery/tests/test_loyalty_delivery.py
addons/sale_management/__init__.py
addons/sale_management/__manifest__.py
addons/sale_management/controllers/__init__.py
addons/sale_management/controllers/portal.py
addons/sale_management/models/__init__.py
addons/sale_management/models/digest.py
addons/sale_management/models/res_company.py
addons/sale_management/models/res_config_settings.py
addons/sale_management/models/sale_order.py
addons/sale_management/models/sale_order_line.py
addons/sale_management/models/sale_order_template.py
addons/sale_management/models/sale_order_template_line.py
addons/sale_management/tests/__init__.py
addons/sale_management/tests/common.py
addons/sale_management/tests/test_sale_order.py
addons/sale_management/tests/test_sale_ui.py
addons/sale_margin/__init__.py
addons/sale_margin/__manifest__.py
addons/sale_margin/models/__init__.py
addons/sale_margin/models/sale_order.py
addons/sale_margin/models/sale_order_line.py
addons/sale_margin/report/__init__.py
addons/sale_margin/report/sale_report.py
addons/sale_margin/tests/__init__.py
addons/sale_margin/tests/test_sale_margin.py
addons/sale_mrp/__init__.py
addons/sale_mrp/__manifest__.py
addons/sale_mrp/models/__init__.py
addons/sale_mrp/models/account_move.py
addons/sale_mrp/models/mrp_bom.py
addons/sale_mrp/models/mrp_production.py
addons/sale_mrp/models/sale_order.py
addons/sale_mrp/models/sale_order_line.py
addons/sale_mrp/models/stock_move_line.py
addons/sale_mrp/models/stock_rule.py
addons/sale_mrp/tests/__init__.py
addons/sale_mrp/tests/test_multistep_manufacturing.py
addons/sale_mrp/tests/test_sale_mrp_anglo_saxon_valuation.py
addons/sale_mrp/tests/test_sale_mrp_flow.py
addons/sale_mrp/tests/test_sale_mrp_kit_bom.py
addons/sale_mrp/tests/test_sale_mrp_lead_time.py
addons/sale_mrp/tests/test_sale_mrp_procurement.py
addons/sale_mrp/tests/test_sale_mrp_report.py
addons/sale_mrp_margin/__init__.py
addons/sale_mrp_margin/__manifest__.py
addons/sale_mrp_margin/tests/__init__.py
addons/sale_mrp_margin/tests/test_sale_mrp_flow.py
addons/sale_pdf_quote_builder/__init__.py
addons/sale_pdf_quote_builder/__manifest__.py
addons/sale_pdf_quote_builder/utils.py
addons/sale_pdf_quote_builder/controllers/__init__.py
addons/sale_pdf_quote_builder/controllers/quotation_document.py
addons/sale_pdf_quote_builder/models/__init__.py
addons/sale_pdf_quote_builder/models/ir_actions_report.py
addons/sale_pdf_quote_builder/models/product_document.py
addons/sale_pdf_quote_builder/models/quotation_document.py
addons/sale_pdf_quote_builder/models/sale_order.py
addons/sale_pdf_quote_builder/models/sale_order_line.py
addons/sale_pdf_quote_builder/models/sale_order_template.py
addons/sale_pdf_quote_builder/models/sale_pdf_form_field.py
addons/sale_pdf_quote_builder/tests/__init__.py
addons/sale_pdf_quote_builder/tests/test_pdf_quote_builder.py
addons/sale_pdf_quote_builder/tests/files/__init__.py
addons/sale_product_matrix/__init__.py
addons/sale_product_matrix/__manifest__.py
addons/sale_product_matrix/models/__init__.py
addons/sale_product_matrix/models/product_template.py
addons/sale_product_matrix/models/sale_order.py
addons/sale_product_matrix/models/sale_order_line.py
addons/sale_project/__init__.py
addons/sale_project/__manifest__.py
addons/sale_project/controllers/__init__.py
addons/sale_project/controllers/portal.py
addons/sale_project/models/__init__.py
addons/sale_project/models/account_move.py
addons/sale_project/models/account_move_line.py
addons/sale_project/models/product_product.py
addons/sale_project/models/product_template.py
addons/sale_project/models/project_milestone.py
addons/sale_project/models/project_project.py
addons/sale_project/models/project_task.py
addons/sale_project/models/project_task_recurrence.py
addons/sale_project/models/project_task_type.py
addons/sale_project/models/project_update.py
addons/sale_project/models/sale_order.py
addons/sale_project/models/sale_order_line.py
addons/sale_project/models/sale_order_template_line.py
addons/sale_project/report/__init__.py
addons/sale_project/report/project_report.py
addons/sale_project/report/sale_report.py
addons/sale_project/tests/__init__.py
addons/sale_project/tests/common.py
addons/sale_project/tests/test_analytic_distribution.py
addons/sale_project/tests/test_child_tasks.py
addons/sale_project/tests/test_project_profitability.py
addons/sale_project/tests/test_project_project.py
addons/sale_project/tests/test_reinvoice.py
addons/sale_project/tests/test_sale_project.py
addons/sale_project/tests/test_sale_project_dashboard.py
addons/sale_project/tests/test_so_line_milestones.py
addons/sale_project/wizard/__init__.py
addons/sale_project/wizard/project_template_create_wizard.py
addons/sale_project_stock/__init__.py
addons/sale_project_stock/__manifest__.py
addons/sale_project_stock/models/__init__.py
addons/sale_project_stock/models/project_project.py
addons/sale_project_stock/models/sale_order_line.py
addons/sale_project_stock/models/stock_move.py
addons/sale_project_stock/models/stock_picking.py
addons/sale_project_stock/tests/__init__.py
addons/sale_project_stock/tests/test_reinvoice.py
addons/sale_project_stock/tests/test_sale_project_stock_profitability.py
addons/sale_project_stock_account/__init__.py
addons/sale_project_stock_account/__manifest__.py
addons/sale_project_stock_account/models/__init__.py
addons/sale_project_stock_account/models/stock_move.py
addons/sale_project_stock_account/tests/__init__.py
addons/sale_project_stock_account/tests/test_analytics_reinvoice.py
addons/sale_purchase/__init__.py
addons/sale_purchase/__manifest__.py
addons/sale_purchase/models/__init__.py
addons/sale_purchase/models/product_template.py
addons/sale_purchase/models/purchase_order.py
addons/sale_purchase/models/sale_order.py
addons/sale_purchase/models/sale_order_line.py
addons/sale_purchase/tests/__init__.py
addons/sale_purchase/tests/common.py
addons/sale_purchase/tests/test_access_rights.py
addons/sale_purchase/tests/test_sale_purchase.py
addons/sale_purchase_project/__init__.py
addons/sale_purchase_project/__manifest__.py
addons/sale_purchase_project/models/__init__.py
addons/sale_purchase_project/models/sale_order_line.py
addons/sale_purchase_project/tests/__init__.py
addons/sale_purchase_project/tests/test_sale_purchase_project.py
addons/sale_purchase_stock/__init__.py
addons/sale_purchase_stock/__manifest__.py
addons/sale_purchase_stock/models/__init__.py
addons/sale_purchase_stock/models/purchase_order.py
addons/sale_purchase_stock/models/sale_order.py
addons/sale_purchase_stock/models/stock_move.py
addons/sale_purchase_stock/models/stock_rule.py
addons/sale_purchase_stock/tests/__init__.py
addons/sale_purchase_stock/tests/test_access_rights.py
addons/sale_purchase_stock/tests/test_lead_time.py
addons/sale_purchase_stock/tests/test_sale_purchase_stock_flow.py
addons/sale_purchase_stock/tests/test_unwanted_replenish_flow.py
addons/sale_service/__init__.py
addons/sale_service/__manifest__.py
addons/sale_service/models/__init__.py
addons/sale_service/models/sale_order_line.py
addons/sale_sms/__init__.py
addons/sale_sms/__manifest__.py
addons/sale_stock/__init__.py
addons/sale_stock/__manifest__.py
addons/sale_stock/controllers/__init__.py
addons/sale_stock/controllers/portal.py
addons/sale_stock/models/__init__.py
addons/sale_stock/models/account_move.py
addons/sale_stock/models/product_template.py
addons/sale_stock/models/res_company.py
addons/sale_stock/models/res_config_settings.py
addons/sale_stock/models/res_users.py
addons/sale_stock/models/sale_order.py
addons/sale_stock/models/sale_order_line.py
addons/sale_stock/models/stock.py
addons/sale_stock/models/stock_reference.py
addons/sale_stock/report/__init__.py
addons/sale_stock/report/report_stock_rule.py
addons/sale_stock/report/sale_report.py
addons/sale_stock/report/stock_forecasted.py
addons/sale_stock/report/stock_valuation_report.py
addons/sale_stock/tests/__init__.py
addons/sale_stock/tests/common.py
addons/sale_stock/tests/test_anglo_saxon_valuation.py
addons/sale_stock/tests/test_anglo_saxon_valuation_reconciliation.py
addons/sale_stock/tests/test_anglosaxon_account.py
addons/sale_stock/tests/test_create_perf.py
addons/sale_stock/tests/test_packaging_tours.py
addons/sale_stock/tests/test_sale_order_dates.py
addons/sale_stock/tests/test_sale_stock.py
addons/sale_stock/tests/test_sale_stock_access_rights.py
addons/sale_stock/tests/test_sale_stock_accrued_entries.py
addons/sale_stock/tests/test_sale_stock_lead_time.py
addons/sale_stock/tests/test_sale_stock_multi_warehouse.py
addons/sale_stock/tests/test_sale_stock_multicompany.py
addons/sale_stock/tests/test_sale_stock_report.py
addons/sale_stock/wizard/__init__.py
addons/sale_stock/wizard/stock_picking_return.py
addons/sale_stock/wizard/stock_return_picking.py
addons/sale_stock/wizard/stock_rules_report.py
addons/sale_stock_margin/__init__.py
addons/sale_stock_margin/__manifest__.py
addons/sale_stock_margin/models/__init__.py
addons/sale_stock_margin/models/sale_order_line.py
addons/sale_stock_margin/tests/__init__.py
addons/sale_stock_margin/tests/test_sale_stock_margin.py
addons/sale_stock_product_expiry/__init__.py
addons/sale_stock_product_expiry/__manifest__.py
addons/sale_stock_product_expiry/models/__init__.py
addons/sale_stock_product_expiry/models/sale_order_line.py
addons/sale_timesheet/__init__.py
addons/sale_timesheet/__manifest__.py
addons/sale_timesheet/controllers/__init__.py
addons/sale_timesheet/controllers/portal.py
addons/sale_timesheet/models/__init__.py
addons/sale_timesheet/models/account_move.py
addons/sale_timesheet/models/account_move_line.py
addons/sale_timesheet/models/hr_employee.py
addons/sale_timesheet/models/hr_timesheet.py
addons/sale_timesheet/models/product_product.py
addons/sale_timesheet/models/product_template.py
addons/sale_timesheet/models/project_project.py
addons/sale_timesheet/models/project_sale_line_employee_map.py
addons/sale_timesheet/models/project_task.py
addons/sale_timesheet/models/res_config_settings.py
addons/sale_timesheet/models/sale_order.py
addons/sale_timesheet/models/sale_order_line.py
addons/sale_timesheet/report/__init__.py
addons/sale_timesheet/report/project_report.py
addons/sale_timesheet/report/timesheets_analysis_report.py
addons/sale_timesheet/tests/__init__.py
addons/sale_timesheet/tests/common.py
addons/sale_timesheet/tests/test_edit_so_line_timesheet.py
addons/sale_timesheet/tests/test_performance.py
addons/sale_timesheet/tests/test_project.py
addons/sale_timesheet/tests/test_project_billing.py
addons/sale_timesheet/tests/test_project_pricing_type.py
addons/sale_timesheet/tests/test_project_profitability.py
addons/sale_timesheet/tests/test_project_update.py
addons/sale_timesheet/tests/test_reinvoice.py
addons/sale_timesheet/tests/test_sale_service.py
addons/sale_timesheet/tests/test_sale_timesheet.py
addons/sale_timesheet/tests/test_sale_timesheet_accrued_entries.py
addons/sale_timesheet/tests/test_sale_timesheet_dashboard.py
addons/sale_timesheet/tests/test_sale_timesheet_product_product.py
addons/sale_timesheet/tests/test_sale_timesheet_product_template.py
addons/sale_timesheet/tests/test_sale_timesheet_report.py
addons/sale_timesheet/tests/test_sale_timesheet_ui.py
addons/sale_timesheet/tests/test_so_line_determined_in_timesheet.py
addons/sale_timesheet/tests/test_task_analysis.py
addons/sale_timesheet/tests/test_upsell_warning.py
addons/sale_timesheet/wizard/__init__.py
addons/sale_timesheet/wizard/sale_make_invoice_advance.py
addons/sale_timesheet_margin/__init__.py
addons/sale_timesheet_margin/__manifest__.py
addons/sale_timesheet_margin/models/__init__.py
addons/sale_timesheet_margin/models/sale_order_line.py
addons/sale_timesheet_margin/tests/__init__.py
addons/sale_timesheet_margin/tests/test_sale_timesheet_margin.py
addons/sales_team/__init__.py
addons/sales_team/__manifest__.py
addons/sales_team/models/__init__.py
addons/sales_team/models/crm_tag.py
addons/sales_team/models/crm_team.py
addons/sales_team/models/crm_team_member.py
addons/sales_team/models/res_users.py
addons/sales_team/tests/__init__.py
addons/sales_team/tests/common.py
addons/sales_team/tests/test_sales_team.py
addons/sales_team/tests/test_sales_team_internals.py
addons/sales_team/tests/test_sales_team_membership.py
addons/sms/__init__.py
addons/sms/__manifest__.py
addons/sms/controllers/__init__.py
addons/sms/controllers/main.py
addons/sms/models/__init__.py
addons/sms/models/iap_account.py
addons/sms/models/ir_actions_server.py
addons/sms/models/ir_model.py
addons/sms/models/mail_followers.py
addons/sms/models/mail_message.py
addons/sms/models/mail_notification.py
addons/sms/models/mail_thread.py
addons/sms/models/models.py
addons/sms/models/res_company.py
addons/sms/models/sms_sms.py
addons/sms/models/sms_template.py
addons/sms/models/sms_tracker.py
addons/sms/tests/__init__.py
addons/sms/tests/common.py
addons/sms/tests/test_sms_composer.py
addons/sms/tests/test_sms_template.py
addons/sms/tools/__init__.py
addons/sms/tools/sms_api.py
addons/sms/tools/sms_tools.py
addons/sms/wizard/__init__.py
addons/sms/wizard/sms_account_code.py
addons/sms/wizard/sms_account_phone.py
addons/sms/wizard/sms_account_sender.py
addons/sms/wizard/sms_composer.py
addons/sms/wizard/sms_template_preview.py
addons/sms/wizard/sms_template_reset.py
addons/sms_twilio/__init__.py
addons/sms_twilio/__manifest__.py
addons/sms_twilio/controllers/__init__.py
addons/sms_twilio/controllers/controllers.py
addons/sms_twilio/models/__init__.py
addons/sms_twilio/models/mail_notification.py
addons/sms_twilio/models/res_company.py
addons/sms_twilio/models/res_config_settings.py
addons/sms_twilio/models/sms_composer.py
addons/sms_twilio/models/sms_sms.py
addons/sms_twilio/models/sms_tracker.py
addons/sms_twilio/models/sms_twilio_number.py
addons/sms_twilio/tests/__init__.py
addons/sms_twilio/tests/common.py
addons/sms_twilio/tests/test_sms_twilio.py
addons/sms_twilio/tests/test_sms_twilio_controller.py
addons/sms_twilio/tests/test_twilio_account_manage.py
addons/sms_twilio/tools/__init__.py
addons/sms_twilio/tools/sms_api.py
addons/sms_twilio/tools/sms_twilio.py
addons/sms_twilio/wizard/__init__.py
addons/sms_twilio/wizard/sms_twilio_account_manage.py
addons/snailmail/__init__.py
addons/snailmail/__manifest__.py
addons/snailmail/country_utils.py
addons/snailmail/models/__init__.py
addons/snailmail/models/ir_actions_report.py
addons/snailmail/models/mail_message.py
addons/snailmail/models/mail_notification.py
addons/snailmail/models/mail_thread.py
addons/snailmail/models/res_company.py
addons/snailmail/models/res_config_settings.py
addons/snailmail/models/res_partner.py
addons/snailmail/models/snailmail_letter.py
addons/snailmail/tests/__init__.py
addons/snailmail/tests/test_attachment_access.py
addons/snailmail_account/__init__.py
addons/snailmail_account/__manifest__.py
addons/snailmail_account/controllers/__init__.py
addons/snailmail_account/controllers/portal.py
addons/snailmail_account/models/__init__.py
addons/snailmail_account/models/account_move.py
addons/snailmail_account/models/account_move_send.py
addons/snailmail_account/models/res_partner.py
addons/snailmail_account/tests/__init__.py
addons/snailmail_account/tests/test_pingen_send.py
addons/snailmail_account/tests/test_snailmail_on_invoice.py
addons/snailmail_account/wizard/__init__.py
addons/snailmail_account/wizard/account_move_send_batch_wizard.py
addons/social_media/__init__.py
addons/social_media/__manifest__.py
addons/social_media/models/__init__.py
addons/social_media/models/res_company.py
addons/spreadsheet/__init__.py
addons/spreadsheet/__manifest__.py
addons/spreadsheet/models/__init__.py
addons/spreadsheet/models/ir_http.py
addons/spreadsheet/models/res_currency.py
addons/spreadsheet/models/res_currency_rate.py
addons/spreadsheet/models/res_lang.py
addons/spreadsheet/models/spreadsheet_mixin.py
addons/spreadsheet/tests/__init__.py
addons/spreadsheet/tests/test_currency.py
addons/spreadsheet/tests/test_currency_rate.py
addons/spreadsheet/tests/test_display_names.py
addons/spreadsheet/tests/test_locale.py
addons/spreadsheet/tests/test_session_info.py
addons/spreadsheet/tests/test_utils.py
addons/spreadsheet/utils/__init__.py
addons/spreadsheet/utils/formatting.py
addons/spreadsheet/utils/json.py
addons/spreadsheet/utils/validate_data.py
addons/spreadsheet_account/__init__.py
addons/spreadsheet_account/__manifest__.py
addons/spreadsheet_account/models/__init__.py
addons/spreadsheet_account/models/account.py
addons/spreadsheet_account/models/res_company.py
addons/spreadsheet_account/tests/__init__.py
addons/spreadsheet_account/tests/test_account_group.py
addons/spreadsheet_account/tests/test_balance_tag.py
addons/spreadsheet_account/tests/test_company_fiscal_year.py
addons/spreadsheet_account/tests/test_debit_credit.py
addons/spreadsheet_account/tests/test_partner_balance.py
addons/spreadsheet_account/tests/test_residual_amount.py
addons/spreadsheet_dashboard/__init__.py
addons/spreadsheet_dashboard/__manifest__.py
addons/spreadsheet_dashboard/controllers/__init__.py
addons/spreadsheet_dashboard/controllers/dashboards_controllers.py
addons/spreadsheet_dashboard/controllers/share.py
addons/spreadsheet_dashboard/models/__init__.py
addons/spreadsheet_dashboard/models/spreadsheet_dashboard.py
addons/spreadsheet_dashboard/models/spreadsheet_dashboard_group.py
addons/spreadsheet_dashboard/models/spreadsheet_dashboard_share.py
addons/spreadsheet_dashboard/tests/__init__.py
addons/spreadsheet_dashboard/tests/common.py
addons/spreadsheet_dashboard/tests/test_dashboard_controllers.py
addons/spreadsheet_dashboard/tests/test_dashboard_share.py
addons/spreadsheet_dashboard/tests/test_share_controllers.py
addons/spreadsheet_dashboard/tests/test_share_dashboard_tour.py
addons/spreadsheet_dashboard/tests/test_spreadsheet_dashboard.py
addons/spreadsheet_dashboard_account/__init__.py
addons/spreadsheet_dashboard_account/__manifest__.py
addons/spreadsheet_dashboard_event_sale/__init__.py
addons/spreadsheet_dashboard_event_sale/__manifest__.py
addons/spreadsheet_dashboard_hr_expense/__init__.py
addons/spreadsheet_dashboard_hr_expense/__manifest__.py
addons/spreadsheet_dashboard_hr_timesheet/__init__.py
addons/spreadsheet_dashboard_hr_timesheet/__manifest__.py
addons/spreadsheet_dashboard_im_livechat/__init__.py
addons/spreadsheet_dashboard_im_livechat/__manifest__.py
addons/spreadsheet_dashboard_pos_hr/__init__.py
addons/spreadsheet_dashboard_pos_hr/__manifest__.py
addons/spreadsheet_dashboard_pos_restaurant/__init__.py
addons/spreadsheet_dashboard_pos_restaurant/__manifest__.py
addons/spreadsheet_dashboard_sale/__init__.py
addons/spreadsheet_dashboard_sale/__manifest__.py
addons/spreadsheet_dashboard_sale_timesheet/__init__.py
addons/spreadsheet_dashboard_sale_timesheet/__manifest__.py
addons/spreadsheet_dashboard_stock_account/__init__.py
addons/spreadsheet_dashboard_stock_account/__manifest__.py
addons/spreadsheet_dashboard_website_sale/__init__.py
addons/spreadsheet_dashboard_website_sale/__manifest__.py
addons/spreadsheet_dashboard_website_sale_slides/__init__.py
addons/spreadsheet_dashboard_website_sale_slides/__manifest__.py
addons/stock/__init__.py
addons/stock/__manifest__.py
addons/stock/controllers/__init__.py
addons/stock/controllers/main.py
addons/stock/models/__init__.py
addons/stock/models/barcode.py
addons/stock/models/ir_actions_report.py
addons/stock/models/product.py
addons/stock/models/product_catalog_mixin.py
addons/stock/models/product_strategy.py
addons/stock/models/res_company.py
addons/stock/models/res_config_settings.py
addons/stock/models/res_partner.py
addons/stock/models/res_users.py
addons/stock/models/stock_location.py
addons/stock/models/stock_lot.py
addons/stock/models/stock_move.py
addons/stock/models/stock_move_line.py
addons/stock/models/stock_orderpoint.py
addons/stock/models/stock_package.py
addons/stock/models/stock_package_history.py
addons/stock/models/stock_package_type.py
addons/stock/models/stock_picking.py
addons/stock/models/stock_quant.py
addons/stock/models/stock_reference.py
addons/stock/models/stock_replenish_mixin.py
addons/stock/models/stock_rule.py
addons/stock/models/stock_scrap.py
addons/stock/models/stock_storage_category.py
addons/stock/models/stock_warehouse.py
addons/stock/report/__init__.py
addons/stock/report/product_label_report.py
addons/stock/report/report_stock_quantity.py
addons/stock/report/report_stock_reception.py
addons/stock/report/report_stock_rule.py
addons/stock/report/stock_forecasted.py
addons/stock/report/stock_traceability.py
addons/stock/tests/__init__.py
addons/stock/tests/common.py
addons/stock/tests/test_generate_serial_numbers.py
addons/stock/tests/test_immediate.py
addons/stock/tests/test_inventory.py
addons/stock/tests/test_move.py
addons/stock/tests/test_move2.py
addons/stock/tests/test_move_lines.py
addons/stock/tests/test_multicompany.py
addons/stock/tests/test_old_rules.py
addons/stock/tests/test_overview_graph.py
addons/stock/tests/test_packing.py
addons/stock/tests/test_packing_neg.py
addons/stock/tests/test_picking_tours.py
addons/stock/tests/test_proc_rule.py
addons/stock/tests/test_product.py
addons/stock/tests/test_quant.py
addons/stock/tests/test_quant_inventory_mode.py
addons/stock/tests/test_replenish.py
addons/stock/tests/test_report.py
addons/stock/tests/test_report_stock_quantity.py
addons/stock/tests/test_report_tours.py
addons/stock/tests/test_robustness.py
addons/stock/tests/test_stock_flow.py
addons/stock/tests/test_stock_location_search.py
addons/stock/tests/test_stock_lot.py
addons/stock/tests/test_stock_return_picking.py
addons/stock/tests/test_warehouse.py
addons/stock/utils/overview_graph.py
addons/stock/wizard/__init__.py
addons/stock/wizard/product_label_layout.py
addons/stock/wizard/product_replenish.py
addons/stock/wizard/stock_backorder_confirmation.py
addons/stock/wizard/stock_inventory_adjustment_name.py
addons/stock/wizard/stock_inventory_conflict.py
addons/stock/wizard/stock_inventory_warning.py
addons/stock/wizard/stock_label_type.py
addons/stock/wizard/stock_lot_label_layout.py
addons/stock/wizard/stock_orderpoint_snooze.py
addons/stock/wizard/stock_package_destination.py
addons/stock/wizard/stock_picking_return.py
addons/stock/wizard/stock_put_in_pack.py
addons/stock/wizard/stock_quant_relocate.py
addons/stock/wizard/stock_quantity_history.py
addons/stock/wizard/stock_replenishment_info.py
addons/stock/wizard/stock_request_count.py
addons/stock/wizard/stock_rules_report.py
addons/stock/wizard/stock_warn_insufficient_qty.py
addons/stock_account/__init__.py
addons/stock_account/__manifest__.py
addons/stock_account/models/__init__.py
addons/stock_account/models/account_account.py
addons/stock_account/models/account_chart_template.py
addons/stock_account/models/account_move.py
addons/stock_account/models/account_move_line.py
addons/stock_account/models/analytic_account.py
addons/stock_account/models/product.py
addons/stock_account/models/product_value.py
addons/stock_account/models/res_company.py
addons/stock_account/models/res_config_settings.py
addons/stock_account/models/stock_location.py
addons/stock_account/models/stock_lot.py
addons/stock_account/models/stock_move.py
addons/stock_account/models/stock_move_line.py
addons/stock_account/models/stock_picking.py
addons/stock_account/models/stock_picking_type.py
addons/stock_account/models/stock_quant.py
addons/stock_account/models/template_generic_coa.py
addons/stock_account/report/__init__.py
addons/stock_account/report/stock_avco_audit_report.py
addons/stock_account/report/stock_forecasted.py
addons/stock_account/report/stock_valuation_report.py
addons/stock_account/tests/__init__.py
addons/stock_account/tests/test_account_move.py
addons/stock_account/tests/test_anglo_saxon_valuation_reconciliation_common.py
addons/stock_account/tests/test_lot_valuation.py
addons/stock_account/tests/test_product.py
addons/stock_account/tests/test_stock_valuation_layer_revaluation.py
addons/stock_account/tests/test_stockvaluation.py
addons/stock_account/tests/test_stockvaluationlayer.py
addons/stock_account/wizard/__init__.py
addons/stock_account/wizard/stock_inventory_adjustment_name.py
addons/stock_account/wizard/stock_picking_return.py
addons/stock_delivery/__init__.py
addons/stock_delivery/__manifest__.py
addons/stock_delivery/models/__init__.py
addons/stock_delivery/models/delivery_carrier.py
addons/stock_delivery/models/delivery_request_objects.py
addons/stock_delivery/models/product_template.py
addons/stock_delivery/models/sale_order.py
addons/stock_delivery/models/stock_move.py
addons/stock_delivery/models/stock_package.py
addons/stock_delivery/models/stock_package_type.py
addons/stock_delivery/models/stock_picking.py
addons/stock_delivery/tests/__init__.py
addons/stock_delivery/tests/test_carrier_propagation.py
addons/stock_delivery/tests/test_delivery_cost.py
addons/stock_delivery/tests/test_delivery_stock_move.py
addons/stock_delivery/tests/test_packing_delivery.py
addons/stock_delivery/wizard/__init__.py
addons/stock_delivery/wizard/choose_delivery_carrier.py
addons/stock_delivery/wizard/stock_put_in_pack.py
addons/stock_delivery/wizard/stock_return_picking.py
addons/stock_dropshipping/__init__.py
addons/stock_dropshipping/__manifest__.py
addons/stock_dropshipping/models/__init__.py
addons/stock_dropshipping/models/product.py
addons/stock_dropshipping/models/purchase.py
addons/stock_dropshipping/models/res_company.py
addons/stock_dropshipping/models/sale.py
addons/stock_dropshipping/models/stock.py
addons/stock_dropshipping/models/stock_replenish_mixin.py
addons/stock_dropshipping/tests/__init__.py
addons/stock_dropshipping/tests/test_dropship.py
addons/stock_dropshipping/tests/test_lifo_price.py
addons/stock_dropshipping/tests/test_procurement_exception.py
addons/stock_dropshipping/tests/test_purchase_order.py
addons/stock_dropshipping/tests/test_stockvaluation.py
addons/stock_fleet/__init__.py
addons/stock_fleet/__manifest__.py
addons/stock_fleet/models/__init__.py
addons/stock_fleet/models/fleet_vehicle_model.py
addons/stock_fleet/models/stock_picking.py
addons/stock_fleet/models/stock_picking_batch.py
addons/stock_fleet/models/stock_warehouse.py
addons/stock_landed_costs/__init__.py
addons/stock_landed_costs/__manifest__.py
addons/stock_landed_costs/models/__init__.py
addons/stock_landed_costs/models/account_move.py
addons/stock_landed_costs/models/product.py
addons/stock_landed_costs/models/purchase.py
addons/stock_landed_costs/models/res_company.py
addons/stock_landed_costs/models/res_config_settings.py
addons/stock_landed_costs/models/stock_landed_cost.py
addons/stock_landed_costs/models/stock_move.py
addons/stock_landed_costs/tests/__init__.py
addons/stock_landed_costs/tests/common.py
addons/stock_landed_costs/tests/test_stock_landed_costs.py
addons/stock_landed_costs/tests/test_stock_landed_costs_branches.py
addons/stock_landed_costs/tests/test_stock_landed_costs_lots.py
addons/stock_landed_costs/tests/test_stock_landed_costs_purchase.py
addons/stock_landed_costs/tests/test_stock_landed_costs_rounding.py
addons/stock_landed_costs/tests/test_stockvaluationlayer.py
addons/stock_maintenance/__init__.py
addons/stock_maintenance/__manifest__.py
addons/stock_maintenance/models/__init__.py
addons/stock_maintenance/models/maintenance.py
addons/stock_maintenance/models/stock_location.py
addons/stock_picking_batch/__init__.py
addons/stock_picking_batch/__manifest__.py
addons/stock_picking_batch/models/__init__.py
addons/stock_picking_batch/models/stock_move.py
addons/stock_picking_batch/models/stock_move_line.py
addons/stock_picking_batch/models/stock_picking.py
addons/stock_picking_batch/models/stock_picking_batch.py
addons/stock_picking_batch/models/stock_warehouse.py
addons/stock_picking_batch/tests/__init__.py
addons/stock_picking_batch/tests/test_auto_waving.py
addons/stock_picking_batch/tests/test_batch_picking.py
addons/stock_picking_batch/tests/test_wave_picking.py
addons/stock_picking_batch/wizard/__init__.py
addons/stock_picking_batch/wizard/stock_add_to_wave.py
addons/stock_picking_batch/wizard/stock_picking_to_batch.py
addons/stock_sms/__init__.py
addons/stock_sms/__manifest__.py
addons/stock_sms/models/__init__.py
addons/stock_sms/models/res_company.py
addons/stock_sms/models/res_config_settings.py
addons/stock_sms/models/stock_picking.py
addons/stock_sms/wizard/__init__.py
addons/stock_sms/wizard/confirm_stock_sms.py
addons/survey/__init__.py
addons/survey/__manifest__.py
addons/survey/controllers/__init__.py
addons/survey/controllers/main.py
addons/survey/controllers/survey_session_manage.py
addons/survey/models/__init__.py
addons/survey/models/badge.py
addons/survey/models/challenge.py
addons/survey/models/ir_http.py
addons/survey/models/res_lang.py
addons/survey/models/res_partner.py
addons/survey/models/survey_question.py
addons/survey/models/survey_survey.py
addons/survey/models/survey_user_input.py
addons/survey/models/templates/__init__.py
addons/survey/models/templates/survey_survey.py
addons/survey/tests/__init__.py
addons/survey/tests/common.py
addons/survey/tests/test_certification_badge.py
addons/survey/tests/test_certification_flow.py
addons/survey/tests/test_ir_http.py
addons/survey/tests/test_survey.py
addons/survey/tests/test_survey_compute_pages_questions.py
addons/survey/tests/test_survey_controller.py
addons/survey/tests/test_survey_flow.py
addons/survey/tests/test_survey_flow_with_conditions.py
addons/survey/tests/test_survey_invite.py
addons/survey/tests/test_survey_performance.py
addons/survey/tests/test_survey_randomize.py
addons/survey/tests/test_survey_results.py
addons/survey/tests/test_survey_security.py
addons/survey/tests/test_survey_ui_backend.py
addons/survey/tests/test_survey_ui_certification.py
addons/survey/tests/test_survey_ui_feedback.py
addons/survey/wizard/__init__.py
addons/survey/wizard/survey_invite.py
addons/survey_crm/__init__.py
addons/survey_crm/__manifest__.py
addons/survey_crm/models/__init__.py
addons/survey_crm/models/crm_lead.py
addons/survey_crm/models/crm_team.py
addons/survey_crm/models/survey_question.py
addons/survey_crm/models/survey_question_answer.py
addons/survey_crm/models/survey_survey.py
addons/survey_crm/models/survey_user_input.py
addons/survey_crm/models/templates/__init__.py
addons/survey_crm/models/templates/survey_survey.py
addons/survey_crm/tests/__init__.py
addons/survey_crm/tests/test_survey_crm.py
addons/test_base_automation/__init__.py
addons/test_base_automation/__manifest__.py
addons/test_base_automation/models/__init__.py
addons/test_base_automation/models/test_base_automation.py
addons/test_base_automation/tests/__init__.py
addons/test_base_automation/tests/test_flow.py
addons/test_base_automation/tests/test_server_actions.py
addons/test_base_automation/tests/test_tour.py
addons/test_crm_full/__init__.py
addons/test_crm_full/__manifest__.py
addons/test_crm_full/tests/__init__.py
addons/test_crm_full/tests/common.py
addons/test_crm_full/tests/test_performance.py
addons/test_discuss_full/__init__.py
addons/test_discuss_full/__manifest__.py
addons/test_discuss_full/tests/__init__.py
addons/test_discuss_full/tests/test_avatar_card_tour.py
addons/test_discuss_full/tests/test_im_livechat_portal.py
addons/test_discuss_full/tests/test_livechat_hr_holidays.py
addons/test_discuss_full/tests/test_livechat_session_open.py
addons/test_discuss_full/tests/test_performance.py
addons/test_discuss_full/tests/test_performance_inbox.py
addons/test_event_full/__init__.py
addons/test_event_full/__manifest__.py
addons/test_event_full/tests/__init__.py
addons/test_event_full/tests/common.py
addons/test_event_full/tests/test_event_crm.py
addons/test_event_full/tests/test_event_event.py
addons/test_event_full/tests/test_event_mail.py
addons/test_event_full/tests/test_event_security.py
addons/test_event_full/tests/test_performance.py
addons/test_event_full/tests/test_wevent_menu.py
addons/test_event_full/tests/test_wevent_register.py
addons/test_html_field_history/__init__.py
addons/test_html_field_history/__manifest__.py
addons/test_html_field_history/models/__init__.py
addons/test_html_field_history/models/model_html_field_history_test.py
addons/test_html_field_history/tests/__init__.py
addons/test_html_field_history/tests/test_model.py
addons/test_import_export/__init__.py
addons/test_import_export/__manifest__.py
addons/test_import_export/models/__init__.py
addons/test_import_export/models/models_export.py
addons/test_import_export/models/models_export_impex.py
addons/test_import_export/models/models_import.py
addons/test_import_export/tests/__init__.py
addons/test_import_export/tests/test_export.py
addons/test_import_export/tests/test_export_impex.py
addons/test_import_export/tests/test_import.py
addons/test_import_export/tests/test_import_csv_magic.py
addons/test_import_export/tests/test_load.py
addons/test_import_export/tests/test_properties.py
addons/test_mail/__init__.py
addons/test_mail/__manifest__.py
addons/test_mail/data/__init__.py
addons/test_mail/data/test_mail_data.py
addons/test_mail/models/__init__.py
addons/test_mail/models/mail_test_access.py
addons/test_mail/models/mail_test_lead.py
addons/test_mail/models/mail_test_ticket.py
addons/test_mail/models/test_mail_corner_case_models.py
addons/test_mail/models/test_mail_feature_models.py
addons/test_mail/models/test_mail_models.py
addons/test_mail/models/test_mail_thread_models.py
addons/test_mail/tests/__init__.py
addons/test_mail/tests/common.py
addons/test_mail/tests/test_controller_attachment.py
addons/test_mail/tests/test_controller_binary.py
addons/test_mail/tests/test_controller_thread.py
addons/test_mail/tests/test_invite.py
addons/test_mail/tests/test_ir_actions.py
addons/test_mail/tests/test_ir_attachment.py
addons/test_mail/tests/test_mail_activity.py
addons/test_mail/tests/test_mail_activity_mixin.py
addons/test_mail/tests/test_mail_activity_plan.py
addons/test_mail/tests/test_mail_alias.py
addons/test_mail/tests/test_mail_composer.py
addons/test_mail/tests/test_mail_composer_mixin.py
addons/test_mail/tests/test_mail_flow.py
addons/test_mail/tests/test_mail_followers.py
addons/test_mail/tests/test_mail_gateway.py
addons/test_mail/tests/test_mail_mail.py
addons/test_mail/tests/test_mail_management.py
addons/test_mail/tests/test_mail_message.py
addons/test_mail/tests/test_mail_message_security.py
addons/test_mail/tests/test_mail_multicompany.py
addons/test_mail/tests/test_mail_push.py
addons/test_mail/tests/test_mail_scheduled_message.py
addons/test_mail/tests/test_mail_security.py
addons/test_mail/tests/test_mail_template.py
addons/test_mail/tests/test_mail_template_preview.py
addons/test_mail/tests/test_mail_thread_internals.py
addons/test_mail/tests/test_mail_thread_mixins.py
addons/test_mail/tests/test_message_post.py
addons/test_mail/tests/test_message_track.py
addons/test_mail/tests/test_performance.py
addons/test_mail_full/__init__.py
addons/test_mail_full/__manifest__.py
addons/test_mail_full/controllers/__init__.py
addons/test_mail_full/controllers/portal.py
addons/test_mail_full/models/__init__.py
addons/test_mail_full/models/test_mail_models_mail.py
addons/test_mail_full/tests/__init__.py
addons/test_mail_full/tests/common.py
addons/test_mail_full/tests/test_controller_attachment.py
addons/test_mail_full/tests/test_controller_reaction.py
addons/test_mail_full/tests/test_controller_thread.py
addons/test_mail_full/tests/test_controller_update.py
addons/test_mail_full/tests/test_ir_mail_server.py
addons/test_mail_full/tests/test_mail_bot.py
addons/test_mail_full/tests/test_mail_performance.py
addons/test_mail_full/tests/test_mail_thread_internals.py
addons/test_mail_full/tests/test_mass_mailing.py
addons/test_mail_full/tests/test_portal.py
addons/test_mail_full/tests/test_rating.py
addons/test_mail_full/tests/test_res_users.py
addons/test_mail_full/tests/test_ui.py
addons/test_mail_sms/__init__.py
addons/test_mail_sms/__manifest__.py
addons/test_mail_sms/models/__init__.py
addons/test_mail_sms/models/test_mail_sms_models.py
addons/test_mail_sms/tests/__init__.py
addons/test_mail_sms/tests/common.py
addons/test_mail_sms/tests/test_mail_thread_phone.py
addons/test_mail_sms/tests/test_phone_blacklist.py
addons/test_mail_sms/tests/test_phone_format.py
addons/test_mail_sms/tests/test_sms_composer.py
addons/test_mail_sms/tests/test_sms_controller.py
addons/test_mail_sms/tests/test_sms_management.py
addons/test_mail_sms/tests/test_sms_mixin.py
addons/test_mail_sms/tests/test_sms_performance.py
addons/test_mail_sms/tests/test_sms_post.py
addons/test_mail_sms/tests/test_sms_server_actions.py
addons/test_mail_sms/tests/test_sms_sms.py
addons/test_mail_sms/tests/test_sms_template.py
addons/test_mass_mailing/__init__.py
addons/test_mass_mailing/__manifest__.py
addons/test_mass_mailing/data/__init__.py
addons/test_mass_mailing/data/mail_test_data.py
addons/test_mass_mailing/models/__init__.py
addons/test_mass_mailing/models/ir_qweb.py
addons/test_mass_mailing/models/mailing_models.py
addons/test_mass_mailing/models/mailing_models_cornercase.py
addons/test_mass_mailing/models/mailing_models_utm.py
addons/test_mass_mailing/tests/__init__.py
addons/test_mass_mailing/tests/common.py
addons/test_mass_mailing/tests/test_blacklist.py
addons/test_mass_mailing/tests/test_blacklist_behavior.py
addons/test_mass_mailing/tests/test_blacklist_mixin.py
addons/test_mass_mailing/tests/test_link_tracker.py
addons/test_mass_mailing/tests/test_link_tracker_sms.py
addons/test_mass_mailing/tests/test_mail_composer.py
addons/test_mass_mailing/tests/test_mailing.py
addons/test_mass_mailing/tests/test_mailing_server.py
addons/test_mass_mailing/tests/test_mailing_sms.py
addons/test_mass_mailing/tests/test_mailing_statistics.py
addons/test_mass_mailing/tests/test_mailing_statistics_sms.py
addons/test_mass_mailing/tests/test_mailing_test.py
addons/test_mass_mailing/tests/test_performance.py
addons/test_mass_mailing/tests/test_sms_controller.py
addons/test_mass_mailing/tests/test_utm.py
addons/test_resource/__init__.py
addons/test_resource/__manifest__.py
addons/test_resource/models/__init__.py
addons/test_resource/models/test_resource.py
addons/test_resource/tests/__init__.py
addons/test_resource/tests/common.py
addons/test_resource/tests/test_calendar.py
addons/test_resource/tests/test_mixin.py
addons/test_resource/tests/test_performance.py
addons/test_resource/tests/test_resource.py
addons/test_resource/tests/test_resource_errors.py
addons/test_resource/tests/test_timezones.py
addons/test_sale_product_configurators/__init__.py
addons/test_sale_product_configurators/__manifest__.py
addons/test_sale_product_configurators/tests/__init__.py
addons/test_sale_product_configurators/tests/test_event_sale_with_product_configurator.py
addons/test_sale_product_configurators/tests/test_sale_product_configurator.py
addons/test_sale_product_configurators/tests/test_sale_product_matrix.py
addons/test_sale_purchase_edi_ubl/__init__.py
addons/test_sale_purchase_edi_ubl/__manifest__.py
addons/test_sale_purchase_edi_ubl/tests/__init__.py
addons/test_sale_purchase_edi_ubl/tests/test_order_ubl_bis3.py
addons/test_spreadsheet/__init__.py
addons/test_spreadsheet/__manifest__.py
addons/test_spreadsheet/models/__init__.py
addons/test_spreadsheet/models/spreadsheet_mixin_test.py
addons/test_spreadsheet/tests/__init__.py
addons/test_spreadsheet/tests/test_spreadsheet.py
addons/test_website/__init__.py
addons/test_website/__manifest__.py
addons/test_website/controllers/__init__.py
addons/test_website/controllers/main.py
addons/test_website/models/__init__.py
addons/test_website/models/model.py
addons/test_website/models/website.py
addons/test_website/tests/__init__.py
addons/test_website/tests/test_controller_args.py
addons/test_website/tests/test_custom_snippet.py
addons/test_website/tests/test_error.py
addons/test_website/tests/test_form.py
addons/test_website/tests/test_fuzzy.py
addons/test_website/tests/test_image_upload_progress.py
addons/test_website/tests/test_is_multilang.py
addons/test_website/tests/test_media.py
addons/test_website/tests/test_menu.py
addons/test_website/tests/test_multi_company.py
addons/test_website/tests/test_page.py
addons/test_website/tests/test_page_manager.py
addons/test_website/tests/test_performance.py
addons/test_website/tests/test_qweb.py
addons/test_website/tests/test_redirect.py
addons/test_website/tests/test_reset_views.py
addons/test_website/tests/test_restricted_editor.py
addons/test_website/tests/test_session.py
addons/test_website/tests/test_settings.py
addons/test_website/tests/test_snippet_background_video.py
addons/test_website/tests/test_systray.py
addons/test_website/tests/test_translation.py
addons/test_website/tests/test_views_during_module_operation.py
addons/test_website/tests/test_website_controller_page.py
addons/test_website/tests/test_website_page_properties.py
addons/test_website_modules/__init__.py
addons/test_website_modules/__manifest__.py
addons/test_website_modules/tests/__init__.py
addons/test_website_modules/tests/test_configurator.py
addons/test_website_modules/tests/test_controllers.py
addons/test_website_modules/tests/test_performance.py
addons/test_website_slides_full/__init__.py
addons/test_website_slides_full/__manifest__.py
addons/test_website_slides_full/tests/__init__.py
addons/test_website_slides_full/tests/test_ui_wslides.py
addons/theme_default/__init__.py
addons/theme_default/__manifest__.py
addons/transifex/__init__.py
addons/transifex/__manifest__.py
addons/transifex/models/__init__.py
addons/transifex/models/models.py
addons/transifex/models/transifex_code_translation.py
addons/transifex/models/transifex_translation.py
addons/uom/__init__.py
addons/uom/__manifest__.py
addons/uom/models/__init__.py
addons/uom/models/uom_uom.py
addons/uom/tests/__init__.py
addons/uom/tests/common.py
addons/uom/tests/test_uom.py
addons/utm/__init__.py
addons/utm/__manifest__.py
addons/utm/models/__init__.py
addons/utm/models/ir_http.py
addons/utm/models/utm_campaign.py
addons/utm/models/utm_medium.py
addons/utm/models/utm_mixin.py
addons/utm/models/utm_source.py
addons/utm/models/utm_stage.py
addons/utm/models/utm_tag.py
addons/utm/tests/__init__.py
addons/utm/tests/common.py
addons/utm/tests/test_routes.py
addons/utm/tests/test_utm.py
addons/utm/tests/test_utm_consistency.py
addons/utm/tests/test_utm_security.py
addons/web/__init__.py
addons/web/__manifest__.py
addons/web/controllers/__init__.py
addons/web/controllers/action.py
addons/web/controllers/binary.py
addons/web/controllers/database.py
addons/web/controllers/dataset.py
addons/web/controllers/domain.py
addons/web/controllers/export.py
addons/web/controllers/home.py
addons/web/controllers/json.py
addons/web/controllers/main.py
addons/web/controllers/model.py
addons/web/controllers/pivot.py
addons/web/controllers/profiling.py
addons/web/controllers/report.py
addons/web/controllers/session.py
addons/web/controllers/utils.py
addons/web/controllers/vcard.py
addons/web/controllers/view.py
addons/web/controllers/webclient.py
addons/web/controllers/webmanifest.py
addons/web/models/__init__.py
addons/web/models/base_document_layout.py
addons/web/models/ir_http.py
addons/web/models/ir_model.py
addons/web/models/ir_qweb_fields.py
addons/web/models/ir_ui_menu.py
addons/web/models/ir_ui_view.py
addons/web/models/models.py
addons/web/models/properties_base_definition.py
addons/web/models/res_config_settings.py
addons/web/models/res_partner.py
addons/web/models/res_users.py
addons/web/models/res_users_settings.py
addons/web/models/res_users_settings_embedded_action.py
addons/web/tests/__init__.py
addons/web/tests/test_action.py
addons/web/tests/test_assets.py
addons/web/tests/test_base_document_layout.py
addons/web/tests/test_click_everywhere.py
addons/web/tests/test_db_manager.py
addons/web/tests/test_domain.py
addons/web/tests/test_favorite.py
addons/web/tests/test_health.py
addons/web/tests/test_image.py
addons/web/tests/test_ir_model.py
addons/web/tests/test_ir_qweb.py
addons/web/tests/test_js.py
addons/web/tests/test_load_menus.py
addons/web/tests/test_login.py
addons/web/tests/test_partner.py
addons/web/tests/test_perf_load_menu.py
addons/web/tests/test_pivot_export.py
addons/web/tests/test_profiler.py
addons/web/tests/test_reports.py
addons/web/tests/test_res_partner_properties.py
addons/web/tests/test_res_users.py
addons/web/tests/test_router.py
addons/web/tests/test_session_info.py
addons/web/tests/test_translate.py
addons/web/tests/test_web_redirect.py
addons/web/tests/test_web_search_read.py
addons/web/tests/test_webmanifest.py
addons/web_hierarchy/__init__.py
addons/web_hierarchy/__manifest__.py
addons/web_hierarchy/models/__init__.py
addons/web_hierarchy/models/ir_actions.py
addons/web_hierarchy/models/ir_ui_view.py
addons/web_hierarchy/models/models.py
addons/web_tour/__init__.py
addons/web_tour/__manifest__.py
addons/web_tour/models/__init__.py
addons/web_tour/models/ir_http.py
addons/web_tour/models/res_users.py
addons/web_tour/models/tour.py
addons/web_tour/tests/__init__.py
addons/web_tour/tests/test_tours.py
addons/web_unsplash/__init__.py
addons/web_unsplash/__manifest__.py
addons/web_unsplash/controllers/__init__.py
addons/web_unsplash/controllers/main.py
addons/web_unsplash/models/__init__.py
addons/web_unsplash/models/ir_attachment.py
addons/web_unsplash/models/ir_qweb_fields.py
addons/web_unsplash/models/res_config_settings.py
addons/web_unsplash/models/res_users.py
addons/website/__init__.py
addons/website/__manifest__.py
addons/website/tools.py
addons/website/controllers/__init__.py
addons/website/controllers/backend.py
addons/website/controllers/binary.py
addons/website/controllers/form.py
addons/website/controllers/main.py
addons/website/controllers/model_page.py
addons/website/controllers/webclient.py
addons/website/models/__init__.py
addons/website/models/assets.py
addons/website/models/base_partner_merge.py
addons/website/models/html_text_processor.py
addons/website/models/ir_actions_server.py
addons/website/models/ir_asset.py
addons/website/models/ir_attachment.py
addons/website/models/ir_binary.py
addons/website/models/ir_http.py
addons/website/models/ir_model.py
addons/website/models/ir_model_data.py
addons/website/models/ir_module_module.py
addons/website/models/ir_qweb.py
addons/website/models/ir_qweb_fields.py
addons/website/models/ir_rule.py
addons/website/models/ir_ui_menu.py
addons/website/models/ir_ui_view.py
addons/website/models/mixins.py
addons/website/models/models.py
addons/website/models/res_company.py
addons/website/models/res_config_settings.py
addons/website/models/res_lang.py
addons/website/models/res_partner.py
addons/website/models/res_users.py
addons/website/models/theme_models.py
addons/website/models/website.py
addons/website/models/website_configurator_feature.py
addons/website/models/website_controller_page.py
addons/website/models/website_form.py
addons/website/models/website_menu.py
addons/website/models/website_page.py
addons/website/models/website_page_properties.py
addons/website/models/website_rewrite.py
addons/website/models/website_snippet_filter.py
addons/website/models/website_technical_page.py
addons/website/models/website_visitor.py
addons/website/tests/__init__.py
addons/website/tests/common.py
addons/website/tests/test_assets.py
addons/website/tests/test_attachment.py
addons/website/tests/test_auth_signup_uninvited.py
addons/website/tests/test_base_url.py
addons/website/tests/test_client_action.py
addons/website/tests/test_configurator.py
addons/website/tests/test_controllers.py
addons/website/tests/test_converter.py
addons/website/tests/test_crawl.py
addons/website/tests/test_custom_snippets.py
addons/website/tests/test_disable_unused_snippets_assets.py
addons/website/tests/test_fuzzy.py
addons/website/tests/test_get_current_website.py
addons/website/tests/test_grid_layout.py
addons/website/tests/test_http_endpoint.py
addons/website/tests/test_iap.py
addons/website/tests/test_import_files.py
addons/website/tests/test_ir_asset.py
addons/website/tests/test_lang_url.py
addons/website/tests/test_menu.py
addons/website/tests/test_page.py
addons/website/tests/test_page_manager.py
addons/website/tests/test_performance.py
addons/website/tests/test_qweb.py
addons/website/tests/test_redirect.py
addons/website/tests/test_res_users.py
addons/website/tests/test_sitemap.py
addons/website/tests/test_skip_website_configurator.py
addons/website/tests/test_snippets.py
addons/website/tests/test_theme.py
addons/website/tests/test_ui.py
addons/website/tests/test_unsplash_beacon.py
addons/website/tests/test_views.py
addons/website/tests/test_views_inherit_module_update.py
addons/website/tests/test_website_favicon.py
addons/website/tests/test_website_form_editor.py
addons/website/tests/test_website_reset_password.py
addons/website/tests/test_website_technical_page.py
addons/website/tests/test_website_visitor.py
addons/website/tests/test_website_website_builder_assets_bundle.py
addons/website/wizard/__init__.py
addons/website/wizard/base_language_install.py
addons/website/wizard/blocked_third_party_domains.py
addons/website/wizard/portal_wizard.py
addons/website/wizard/website_robots.py
addons/website_blog/__init__.py
addons/website_blog/__manifest__.py
addons/website_blog/controllers/__init__.py
addons/website_blog/controllers/main.py
addons/website_blog/models/__init__.py
addons/website_blog/models/website.py
addons/website_blog/models/website_blog.py
addons/website_blog/models/website_snippet_filter.py
addons/website_blog/tests/__init__.py
addons/website_blog/tests/common.py
addons/website_blog/tests/test_performance.py
addons/website_blog/tests/test_sitemap.py
addons/website_blog/tests/test_ui.py
addons/website_blog/tests/test_website_blog_flow.py
addons/website_blog/tests/test_website_blog_technical_page.py
addons/website_cf_turnstile/__init__.py
addons/website_cf_turnstile/__manifest__.py
addons/website_cf_turnstile/models/__init__.py
addons/website_cf_turnstile/models/ir_http.py
addons/website_cf_turnstile/models/res_config_settings.py
addons/website_crm/__init__.py
addons/website_crm/__manifest__.py
addons/website_crm/controllers/__init__.py
addons/website_crm/controllers/website_form.py
addons/website_crm/models/__init__.py
addons/website_crm/models/crm_lead.py
addons/website_crm/models/website.py
addons/website_crm/models/website_visitor.py
addons/website_crm/tests/__init__.py
addons/website_crm/tests/test_crm_lead_merge.py
addons/website_crm/tests/test_website_crm.py
addons/website_crm/tests/test_website_visitor.py
addons/website_crm_iap_reveal/__init__.py
addons/website_crm_iap_reveal/__manifest__.py
addons/website_crm_iap_reveal/controllers/__init__.py
addons/website_crm_iap_reveal/controllers/website_form.py
addons/website_crm_iap_reveal/models/__init__.py
addons/website_crm_iap_reveal/models/crm_lead.py
addons/website_crm_iap_reveal/models/crm_reveal_rule.py
addons/website_crm_iap_reveal/models/crm_reveal_view.py
addons/website_crm_iap_reveal/models/ir_http.py
addons/website_crm_iap_reveal/tests/__init__.py
addons/website_crm_iap_reveal/tests/common.py
addons/website_crm_iap_reveal/tests/test_lead_reveal.py
addons/website_crm_livechat/__init__.py
addons/website_crm_livechat/__manifest__.py
addons/website_crm_livechat/models/__init__.py
addons/website_crm_livechat/models/chatbot_script_step.py
addons/website_crm_livechat/models/crm_lead.py
addons/website_crm_livechat/models/discuss_channel.py
addons/website_crm_partner_assign/__init__.py
addons/website_crm_partner_assign/__manifest__.py
addons/website_crm_partner_assign/controllers/__init__.py
addons/website_crm_partner_assign/controllers/main.py
addons/website_crm_partner_assign/models/__init__.py
addons/website_crm_partner_assign/models/crm_lead.py
addons/website_crm_partner_assign/models/res_partner.py
addons/website_crm_partner_assign/models/res_partner_activation.py
addons/website_crm_partner_assign/models/res_partner_grade.py
addons/website_crm_partner_assign/models/website.py
addons/website_crm_partner_assign/report/__init__.py
addons/website_crm_partner_assign/report/crm_partner_report.py
addons/website_crm_partner_assign/tests/__init__.py
addons/website_crm_partner_assign/tests/test_partner_assign.py
addons/website_crm_partner_assign/tests/test_res_partner.py
addons/website_crm_partner_assign/tests/test_website_crm_partner_technical_page.py
addons/website_crm_partner_assign/wizard/__init__.py
addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py
addons/website_crm_sms/__init__.py
addons/website_crm_sms/__manifest__.py
addons/website_crm_sms/models/__init__.py
addons/website_crm_sms/models/website_visitor.py
addons/website_customer/__init__.py
addons/website_customer/__manifest__.py
addons/website_customer/controllers/__init__.py
addons/website_customer/controllers/main.py
addons/website_customer/models/__init__.py
addons/website_customer/models/res_partner.py
addons/website_customer/models/website.py
addons/website_customer/tests/__init__.py
addons/website_customer/tests/test_website_customers_technical_page.py
addons/website_event/__init__.py
addons/website_event/__manifest__.py
addons/website_event/controllers/__init__.py
addons/website_event/controllers/community.py
addons/website_event/controllers/main.py
addons/website_event/models/__init__.py
addons/website_event/models/event_event.py
addons/website_event/models/event_registration.py
addons/website_event/models/event_tag.py
addons/website_event/models/event_tag_category.py
addons/website_event/models/event_type.py
addons/website_event/models/website.py
addons/website_event/models/website_event_menu.py
addons/website_event/models/website_menu.py
addons/website_event/models/website_snippet_filter.py
addons/website_event/models/website_visitor.py
addons/website_event/tests/__init__.py
addons/website_event/tests/common.py
addons/website_event/tests/test_event_internals.py
addons/website_event/tests/test_event_mail.py
addons/website_event/tests/test_event_menus.py
addons/website_event/tests/test_event_visitor.py
addons/website_event/tests/test_fuzzy.py
addons/website_event/tests/test_website_event.py
addons/website_event/tests/test_website_event_technical_page.py
addons/website_event_booth/__init__.py
addons/website_event_booth/__manifest__.py
addons/website_event_booth/controllers/__init__.py
addons/website_event_booth/controllers/event_booth.py
addons/website_event_booth/models/__init__.py
addons/website_event_booth/models/event_event.py
addons/website_event_booth/models/event_type.py
addons/website_event_booth/models/website_event_menu.py
addons/website_event_booth_exhibitor/__init__.py
addons/website_event_booth_exhibitor/__manifest__.py
addons/website_event_booth_exhibitor/controllers/__init__.py
addons/website_event_booth_exhibitor/controllers/event_booth.py
addons/website_event_booth_exhibitor/models/__init__.py
addons/website_event_booth_exhibitor/models/event_booth.py
addons/website_event_booth_exhibitor/models/event_booth_category.py
addons/website_event_booth_exhibitor/tests/__init__.py
addons/website_event_booth_exhibitor/tests/test_wevent_booth_exhibitor.py
addons/website_event_booth_sale/__init__.py
addons/website_event_booth_sale/__manifest__.py
addons/website_event_booth_sale/controllers/__init__.py
addons/website_event_booth_sale/controllers/event_booth.py
addons/website_event_booth_sale/models/__init__.py
addons/website_event_booth_sale/models/product_template.py
addons/website_event_booth_sale/models/sale_order.py
addons/website_event_booth_sale/models/sale_order_line.py
addons/website_event_booth_sale/tests/__init__.py
addons/website_event_booth_sale/tests/test_event_booth_sale.py
addons/website_event_booth_sale/tests/test_website_event_booth_sale_pricelist.py
addons/website_event_booth_sale_exhibitor/__init__.py
addons/website_event_booth_sale_exhibitor/__manifest__.py
addons/website_event_booth_sale_exhibitor/models/__init__.py
addons/website_event_booth_sale_exhibitor/models/event_booth_registration.py
addons/website_event_crm/__init__.py
addons/website_event_crm/__manifest__.py
addons/website_event_crm/models/__init__.py
addons/website_event_crm/models/event_registration.py
addons/website_event_crm/tests/__init__.py
addons/website_event_crm/tests/test_event_registration.py
addons/website_event_crm/tests/test_visitor_propagation.py
addons/website_event_exhibitor/__init__.py
addons/website_event_exhibitor/__manifest__.py
addons/website_event_exhibitor/controllers/__init__.py
addons/website_event_exhibitor/controllers/exhibitor.py
addons/website_event_exhibitor/models/__init__.py
addons/website_event_exhibitor/models/event_event.py
addons/website_event_exhibitor/models/event_sponsor.py
addons/website_event_exhibitor/models/event_sponsor_type.py
addons/website_event_exhibitor/models/event_type.py
addons/website_event_exhibitor/models/website.py
addons/website_event_exhibitor/models/website_event_menu.py
addons/website_event_exhibitor/tests/__init__.py
addons/website_event_exhibitor/tests/common.py
addons/website_event_exhibitor/tests/test_sponsor_internals.py
addons/website_event_sale/__init__.py
addons/website_event_sale/__manifest__.py
addons/website_event_sale/controllers/__init__.py
addons/website_event_sale/controllers/main.py
addons/website_event_sale/controllers/payment.py
addons/website_event_sale/controllers/sale.py
addons/website_event_sale/models/__init__.py
addons/website_event_sale/models/product.py
addons/website_event_sale/models/product_pricelist.py
addons/website_event_sale/models/sale_order.py
addons/website_event_sale/models/sale_order_line.py
addons/website_event_sale/report/__init__.py
addons/website_event_sale/report/event_sale_report.py
addons/website_event_sale/tests/__init__.py
addons/website_event_sale/tests/common.py
addons/website_event_sale/tests/test_frontend_buy_tickets.py
addons/website_event_sale/tests/test_website_event_sale.py
addons/website_event_sale/tests/test_website_event_sale_cart.py
addons/website_event_sale/tests/test_website_event_sale_pricelist.py
addons/website_event_track/__init__.py
addons/website_event_track/__manifest__.py
addons/website_event_track/controllers/__init__.py
addons/website_event_track/controllers/event.py
addons/website_event_track/controllers/event_track.py
addons/website_event_track/controllers/webmanifest.py
addons/website_event_track/models/__init__.py
addons/website_event_track/models/event_event.py
addons/website_event_track/models/event_track.py
addons/website_event_track/models/event_track_location.py
addons/website_event_track/models/event_track_stage.py
addons/website_event_track/models/event_track_tag.py
addons/website_event_track/models/event_track_tag_category.py
addons/website_event_track/models/event_track_visitor.py
addons/website_event_track/models/event_type.py
addons/website_event_track/models/res_config_settings.py
addons/website_event_track/models/website.py
addons/website_event_track/models/website_event_menu.py
addons/website_event_track/models/website_menu.py
addons/website_event_track/models/website_visitor.py
addons/website_event_track/tests/__init__.py
addons/website_event_track/tests/test_event_menus.py
addons/website_event_track/tests/test_mail_features.py
addons/website_event_track/tests/test_track_internals.py
addons/website_event_track/tests/test_website_event.py
addons/website_event_track/tests/test_website_event_track.py
addons/website_event_track/tests/test_website_visitor.py
addons/website_event_track_live/__init__.py
addons/website_event_track_live/__manifest__.py
addons/website_event_track_live/controllers/__init__.py
addons/website_event_track_live/controllers/session.py
addons/website_event_track_live/controllers/track_live.py
addons/website_event_track_live/models/__init__.py
addons/website_event_track_live/models/event_track.py
addons/website_event_track_live_quiz/__init__.py
addons/website_event_track_live_quiz/__manifest__.py
addons/website_event_track_live_quiz/controllers/__init__.py
addons/website_event_track_live_quiz/controllers/track_live_quiz.py
addons/website_event_track_quiz/__init__.py
addons/website_event_track_quiz/__manifest__.py
addons/website_event_track_quiz/controllers/__init__.py
addons/website_event_track_quiz/controllers/community.py
addons/website_event_track_quiz/controllers/event_track_quiz.py
addons/website_event_track_quiz/models/__init__.py
addons/website_event_track_quiz/models/event_event.py
addons/website_event_track_quiz/models/event_quiz.py
addons/website_event_track_quiz/models/event_track.py
addons/website_event_track_quiz/models/event_track_visitor.py
addons/website_forum/__init__.py
addons/website_forum/__manifest__.py
addons/website_forum/controllers/__init__.py
addons/website_forum/controllers/legacy.py
addons/website_forum/controllers/website_forum.py
addons/website_forum/models/__init__.py
addons/website_forum/models/forum_forum.py
addons/website_forum/models/forum_post.py
addons/website_forum/models/forum_post_reason.py
addons/website_forum/models/forum_post_vote.py
addons/website_forum/models/forum_tag.py
addons/website_forum/models/gamification_challenge.py
addons/website_forum/models/gamification_karma_tracking.py
addons/website_forum/models/ir_attachment.py
addons/website_forum/models/res_users.py
addons/website_forum/models/website.py
addons/website_forum/tests/__init__.py
addons/website_forum/tests/common.py
addons/website_forum/tests/test_forum_controller.py
addons/website_forum/tests/test_forum_internals.py
addons/website_forum/tests/test_forum_karma_access.py
addons/website_forum/tests/test_forum_post.py
addons/website_forum/tests/test_forum_tag.py
addons/website_forum/tests/test_forum_tours.py
addons/website_forum/tests/test_performance.py
addons/website_forum/tests/test_sitemap.py
addons/website_forum/tests/test_web_editor.py
addons/website_forum/tests/test_website_forum_technical_page.py
addons/website_google_map/__init__.py
addons/website_google_map/__manifest__.py
addons/website_google_map/controllers/__init__.py
addons/website_google_map/controllers/main.py
addons/website_hr_recruitment/__init__.py
addons/website_hr_recruitment/__manifest__.py
addons/website_hr_recruitment/controllers/__init__.py
addons/website_hr_recruitment/controllers/main.py
addons/website_hr_recruitment/models/__init__.py
addons/website_hr_recruitment/models/hr_applicant.py
addons/website_hr_recruitment/models/hr_department.py
addons/website_hr_recruitment/models/hr_job.py
addons/website_hr_recruitment/models/hr_recruitment_source.py
addons/website_hr_recruitment/models/website.py
addons/website_hr_recruitment/models/website_page.py
addons/website_hr_recruitment/tests/__init__.py
addons/website_hr_recruitment/tests/test_website_hr_recruitment.py
addons/website_hr_recruitment/tests/test_website_hr_recruitment_technical_page.py
addons/website_hr_recruitment_livechat/__init__.py
addons/website_hr_recruitment_livechat/__manifest__.py
addons/website_links/__init__.py
addons/website_links/__manifest__.py
addons/website_links/controller/__init__.py
addons/website_links/controller/main.py
addons/website_links/models/__init__.py
addons/website_links/models/link_tracker.py
addons/website_links/tests/__init__.py
addons/website_links/tests/test_controller.py
addons/website_links/tests/test_link_tracker.py
addons/website_links/tests/test_ui.py
addons/website_livechat/__init__.py
addons/website_livechat/__manifest__.py
addons/website_livechat/controllers/__init__.py
addons/website_livechat/controllers/chatbot.py
addons/website_livechat/controllers/main.py
addons/website_livechat/controllers/webclient.py
addons/website_livechat/models/__init__.py
addons/website_livechat/models/chatbot_script.py
addons/website_livechat/models/chatbot_script_step.py
addons/website_livechat/models/discuss_channel.py
addons/website_livechat/models/im_livechat_channel.py
addons/website_livechat/models/ir_http.py
addons/website_livechat/models/res_config_settings.py
addons/website_livechat/models/website.py
addons/website_livechat/models/website_page.py
addons/website_livechat/models/website_visitor.py
addons/website_livechat/tests/__init__.py
addons/website_livechat/tests/common.py
addons/website_livechat/tests/test_chatbot_ui.py
addons/website_livechat/tests/test_fw_operator.py
addons/website_livechat/tests/test_lazy_frontend_bus.py
addons/website_livechat/tests/test_livechat_basic_flow.py
addons/website_livechat/tests/test_livechat_request.py
addons/website_livechat/tests/test_livechat_session_user_changes.py
addons/website_livechat/tests/test_ui.py
addons/website_livechat/tests/test_website_visitor.py
addons/website_mail/__init__.py
addons/website_mail/__manifest__.py
addons/website_mail/controllers/__init__.py
addons/website_mail/controllers/main.py
addons/website_mail/models/__init__.py
addons/website_mail/models/ir_http.py
addons/website_mail/models/update.py
addons/website_mail_group/__init__.py
addons/website_mail_group/__manifest__.py
addons/website_mail_group/controllers/__init__.py
addons/website_mail_group/controllers/main.py
addons/website_mail_group/models/__init__.py
addons/website_mail_group/models/mail_group.py
addons/website_mail_group/tests/__init__.py
addons/website_mail_group/tests/test_website_groups_technical_page.py
addons/website_mass_mailing/__init__.py
addons/website_mass_mailing/__manifest__.py
addons/website_mass_mailing/controllers/__init__.py
addons/website_mass_mailing/controllers/main.py
addons/website_mass_mailing/controllers/website_form.py
addons/website_mass_mailing/models/__init__.py
addons/website_mass_mailing/models/res_company.py
addons/website_mass_mailing/tests/__init__.py
addons/website_mass_mailing/tests/test_snippets.py
addons/website_mass_mailing_sms/__init__.py
addons/website_mass_mailing_sms/__manifest__.py
addons/website_mass_mailing_sms/controllers/__init__.py
addons/website_mass_mailing_sms/controllers/main.py
addons/website_partner/__init__.py
addons/website_partner/__manifest__.py
addons/website_partner/controllers/__init__.py
addons/website_partner/controllers/main.py
addons/website_partner/models/__init__.py
addons/website_partner/models/res_partner.py
addons/website_payment/__init__.py
addons/website_payment/__manifest__.py
addons/website_payment/controllers/__init__.py
addons/website_payment/controllers/payment.py
addons/website_payment/controllers/portal.py
addons/website_payment/models/__init__.py
addons/website_payment/models/account_payment.py
addons/website_payment/models/payment_provider.py
addons/website_payment/models/payment_transaction.py
addons/website_payment/models/res_config_settings.py
addons/website_payment/tests/__init__.py
addons/website_payment/tests/test_mailing.py
addons/website_payment/tests/test_snippets.py
addons/website_payment/tests/test_website_payment_technical_page.py
addons/website_profile/__init__.py
addons/website_profile/__manifest__.py
addons/website_profile/controllers/__init__.py
addons/website_profile/controllers/main.py
addons/website_profile/controllers/portal.py
addons/website_profile/models/__init__.py
addons/website_profile/models/gamification_badge.py
addons/website_profile/models/res_users.py
addons/website_profile/models/website.py
addons/website_profile/tests/__init__.py
addons/website_profile/tests/test_website_profile.py
addons/website_profile/tests/test_website_profile_technical_page.py
addons/website_project/__init__.py
addons/website_project/__manifest__.py
addons/website_project/controllers/__init__.py
addons/website_project/controllers/main.py
addons/website_project/models/__init__.py
addons/website_project/models/project_task.py
addons/website_project/models/website_page.py
addons/website_project/tests/__init__.py
addons/website_project/tests/test_project_portal_access.py
addons/website_sale/__init__.py
addons/website_sale/__manifest__.py
addons/website_sale/const.py
addons/website_sale/utils.py
addons/website_sale/controllers/__init__.py
addons/website_sale/controllers/cart.py
addons/website_sale/controllers/combo_configurator.py
addons/website_sale/controllers/delivery.py
addons/website_sale/controllers/main.py
addons/website_sale/controllers/payment.py
addons/website_sale/controllers/product_configurator.py
addons/website_sale/controllers/product_feed.py
addons/website_sale/controllers/reorder.py
addons/website_sale/controllers/sale.py
addons/website_sale/controllers/variant.py
addons/website_sale/controllers/website.py
addons/website_sale/models/__init__.py
addons/website_sale/models/account_move.py
addons/website_sale/models/crm_team.py
addons/website_sale/models/delivery_carrier.py
addons/website_sale/models/digest.py
addons/website_sale/models/ir_http.py
addons/website_sale/models/payment_token.py
addons/website_sale/models/product_attribute.py
addons/website_sale/models/product_document.py
addons/website_sale/models/product_feed.py
addons/website_sale/models/product_image.py
addons/website_sale/models/product_pricelist.py
addons/website_sale/models/product_pricelist_item.py
addons/website_sale/models/product_product.py
addons/website_sale/models/product_public_category.py
addons/website_sale/models/product_ribbon.py
addons/website_sale/models/product_tag.py
addons/website_sale/models/product_template.py
addons/website_sale/models/product_template_attribute_line.py
addons/website_sale/models/product_template_attribute_value.py
addons/website_sale/models/res_company.py
addons/website_sale/models/res_config_settings.py
addons/website_sale/models/res_partner.py
addons/website_sale/models/sale_order.py
addons/website_sale/models/sale_order_line.py
addons/website_sale/models/theme_utils.py
addons/website_sale/models/website.py
addons/website_sale/models/website_base_unit.py
addons/website_sale/models/website_checkout_step.py
addons/website_sale/models/website_menu.py
addons/website_sale/models/website_page.py
addons/website_sale/models/website_sale_extra_field.py
addons/website_sale/models/website_snippet_filter.py
addons/website_sale/models/website_track.py
addons/website_sale/models/website_visitor.py
addons/website_sale/report/__init__.py
addons/website_sale/report/sale_report.py
addons/website_sale/tests/__init__.py
addons/website_sale/tests/common.py
addons/website_sale/tests/common_gmc.py
addons/website_sale/tests/test_address.py
addons/website_sale/tests/test_common.py
addons/website_sale/tests/test_customize.py
addons/website_sale/tests/test_delivery_controller.py
addons/website_sale/tests/test_delivery_ui.py
addons/website_sale/tests/test_dynamic_snippet_category.py
addons/website_sale/tests/test_ecommerce_access.py
addons/website_sale/tests/test_express_checkout_flows.py
addons/website_sale/tests/test_fuzzy.py
addons/website_sale/tests/test_product_public_category.py
addons/website_sale/tests/test_sale_order.py
addons/website_sale/tests/test_sale_process.py
addons/website_sale/tests/test_sitemap.py
addons/website_sale/tests/test_website_editor.py
addons/website_sale/tests/test_website_sale_add_to_cart_snippet.py
addons/website_sale/tests/test_website_sale_cart.py
addons/website_sale/tests/test_website_sale_cart_abandoned.py
addons/website_sale/tests/test_website_sale_cart_notification.py
addons/website_sale/tests/test_website_sale_cart_payment.py
addons/website_sale/tests/test_website_sale_cart_recovery.py
addons/website_sale/tests/test_website_sale_checkout_steps.py
addons/website_sale/tests/test_website_sale_combo_configurator.py
addons/website_sale/tests/test_website_sale_gmc.py
addons/website_sale/tests/test_website_sale_image.py
addons/website_sale/tests/test_website_sale_invoice.py
addons/website_sale/tests/test_website_sale_mail.py
addons/website_sale/tests/test_website_sale_pricelist.py
addons/website_sale/tests/test_website_sale_product_attribute_value_config.py
addons/website_sale/tests/test_website_sale_product_configurator.py
addons/website_sale/tests/test_website_sale_product_filters.py
addons/website_sale/tests/test_website_sale_product_page.py
addons/website_sale/tests/test_website_sale_product_ribbon.py
addons/website_sale/tests/test_website_sale_product_template.py
addons/website_sale/tests/test_website_sale_reorder_from_portal.py
addons/website_sale/tests/test_website_sale_settings.py
addons/website_sale/tests/test_website_sale_shop_redirects.py
addons/website_sale/tests/test_website_sale_show_compare_list_price.py
addons/website_sale/tests/test_website_sale_snippets.py
addons/website_sale/tests/test_website_sale_technical_page.py
addons/website_sale/tests/test_website_sale_visitor.py
addons/website_sale/tests/test_website_sequence.py
addons/website_sale_autocomplete/__init__.py
addons/website_sale_autocomplete/__manifest__.py
addons/website_sale_autocomplete/controllers/__init__.py
addons/website_sale_autocomplete/controllers/main.py
addons/website_sale_autocomplete/models/__init__.py
addons/website_sale_autocomplete/models/res_config_settings.py
addons/website_sale_autocomplete/models/website.py
addons/website_sale_autocomplete/tests/__init__.py
addons/website_sale_autocomplete/tests/test_ui.py
addons/website_sale_collect/__init__.py
addons/website_sale_collect/__manifest__.py
addons/website_sale_collect/const.py
addons/website_sale_collect/utils.py
addons/website_sale_collect/controllers/__init__.py
addons/website_sale_collect/controllers/delivery.py
addons/website_sale_collect/controllers/main.py
addons/website_sale_collect/controllers/payment.py
addons/website_sale_collect/models/__init__.py
addons/website_sale_collect/models/delivery_carrier.py
addons/website_sale_collect/models/payment_provider.py
addons/website_sale_collect/models/payment_transaction.py
addons/website_sale_collect/models/product_template.py
addons/website_sale_collect/models/res_config_settings.py
addons/website_sale_collect/models/sale_order.py
addons/website_sale_collect/models/stock_warehouse.py
addons/website_sale_collect/models/website.py
addons/website_sale_collect/tests/__init__.py
addons/website_sale_collect/tests/common.py
addons/website_sale_collect/tests/test_click_and_collect_express_checkout.py
addons/website_sale_collect/tests/test_click_and_collect_flow.py
addons/website_sale_collect/tests/test_delivery_carrier.py
addons/website_sale_collect/tests/test_in_store_delivery.py
addons/website_sale_collect/tests/test_payment_provider.py
addons/website_sale_collect/tests/test_payment_transaction.py
addons/website_sale_collect/tests/test_product_template.py
addons/website_sale_collect/tests/test_sale_order.py
addons/website_sale_collect/tests/test_website.py
addons/website_sale_collect_wishlist/__init__.py
addons/website_sale_collect_wishlist/__manifest__.py
addons/website_sale_comparison/__init__.py
addons/website_sale_comparison/__manifest__.py
addons/website_sale_comparison/controllers/__init__.py
addons/website_sale_comparison/controllers/main.py
addons/website_sale_comparison/models/__init__.py
addons/website_sale_comparison/models/product_attribute.py
addons/website_sale_comparison/models/product_attribute_category.py
addons/website_sale_comparison/models/product_product.py
addons/website_sale_comparison/models/product_template_attribute_line.py
addons/website_sale_comparison/tests/__init__.py
addons/website_sale_comparison/tests/test_website_sale_comparison.py
addons/website_sale_comparison_wishlist/__init__.py
addons/website_sale_comparison_wishlist/__manifest__.py
addons/website_sale_gelato/__init__.py
addons/website_sale_gelato/__manifest__.py
addons/website_sale_gelato/models/__init__.py
addons/website_sale_gelato/models/product_document.py
addons/website_sale_gelato/models/product_template.py
addons/website_sale_gelato/models/sale_order.py
addons/website_sale_loyalty/__init__.py
addons/website_sale_loyalty/__manifest__.py
addons/website_sale_loyalty/controllers/__init__.py
addons/website_sale_loyalty/controllers/cart.py
addons/website_sale_loyalty/controllers/delivery.py
addons/website_sale_loyalty/controllers/main.py
addons/website_sale_loyalty/controllers/payment.py
addons/website_sale_loyalty/controllers/portal.py
addons/website_sale_loyalty/models/__init__.py
addons/website_sale_loyalty/models/loyalty_card.py
addons/website_sale_loyalty/models/loyalty_program.py
addons/website_sale_loyalty/models/loyalty_rule.py
addons/website_sale_loyalty/models/product_product.py
addons/website_sale_loyalty/models/sale_order.py
addons/website_sale_loyalty/models/sale_order_line.py
addons/website_sale_loyalty/tests/__init__.py
addons/website_sale_loyalty/tests/test_apply_pending_coupon.py
addons/website_sale_loyalty/tests/test_ewallet.py
addons/website_sale_loyalty/tests/test_free_product_reward.py
addons/website_sale_loyalty/tests/test_sale_coupon_multiwebsite.py
addons/website_sale_loyalty/tests/test_shop_loyalty_payment.py
addons/website_sale_loyalty/tests/test_shop_multi_reward.py
addons/website_sale_loyalty/tests/test_shop_sale_coupon.py
addons/website_sale_loyalty/tests/test_website_sale_auto_invoice.py
addons/website_sale_loyalty/tests/test_website_sale_loyalty_delivery.py
addons/website_sale_loyalty/wizard/__init__.py
addons/website_sale_loyalty/wizard/coupon_share.py
addons/website_sale_mass_mailing/__init__.py
addons/website_sale_mass_mailing/__manifest__.py
addons/website_sale_mass_mailing/controllers/__init__.py
addons/website_sale_mass_mailing/controllers/main.py
addons/website_sale_mass_mailing/models/__init__.py
addons/website_sale_mass_mailing/models/res_config_settings.py
addons/website_sale_mass_mailing/models/website.py
addons/website_sale_mass_mailing/tests/__init__.py
addons/website_sale_mass_mailing/tests/test_res_config_settings.py
addons/website_sale_mondialrelay/__init__.py
addons/website_sale_mondialrelay/__manifest__.py
addons/website_sale_mondialrelay/controllers/__init__.py
addons/website_sale_mondialrelay/controllers/controllers.py
addons/website_sale_mondialrelay/models/__init__.py
addons/website_sale_mondialrelay/models/sale_order.py
addons/website_sale_mrp/__init__.py
addons/website_sale_mrp/__manifest__.py
addons/website_sale_mrp/controllers/__init__.py
addons/website_sale_mrp/controllers/variant.py
addons/website_sale_mrp/models/__init__.py
addons/website_sale_mrp/models/sale_order.py
addons/website_sale_mrp/tests/__init__.py
addons/website_sale_mrp/tests/test_website_sale_product_availability.py
addons/website_sale_slides/__init__.py
addons/website_sale_slides/__manifest__.py
addons/website_sale_slides/controllers/__init__.py
addons/website_sale_slides/controllers/sale.py
addons/website_sale_slides/controllers/slides.py
addons/website_sale_slides/models/__init__.py
addons/website_sale_slides/models/product_product.py
addons/website_sale_slides/models/product_template.py
addons/website_sale_slides/models/sale_order.py
addons/website_sale_slides/models/sale_order_line.py
addons/website_sale_slides/models/slide_channel.py
addons/website_sale_slides/tests/__init__.py
addons/website_sale_slides/tests/test_course_purchase_flow.py
addons/website_sale_slides/tests/test_ui_website_sale_slides.py
addons/website_sale_stock/__init__.py
addons/website_sale_stock/__manifest__.py
addons/website_sale_stock/controllers/__init__.py
addons/website_sale_stock/controllers/main.py
addons/website_sale_stock/controllers/variant.py
addons/website_sale_stock/controllers/website_sale.py
addons/website_sale_stock/models/__init__.py
addons/website_sale_stock/models/product_combo.py
addons/website_sale_stock/models/product_feed.py
addons/website_sale_stock/models/product_product.py
addons/website_sale_stock/models/product_ribbon.py
addons/website_sale_stock/models/product_template.py
addons/website_sale_stock/models/res_config_settings.py
addons/website_sale_stock/models/sale_order.py
addons/website_sale_stock/models/sale_order_line.py
addons/website_sale_stock/models/stock_picking.py
addons/website_sale_stock/models/website.py
addons/website_sale_stock/tests/__init__.py
addons/website_sale_stock/tests/common.py
addons/website_sale_stock/tests/test_website_sale_stock_abandoned_cart_email.py
addons/website_sale_stock/tests/test_website_sale_stock_configurators.py
addons/website_sale_stock/tests/test_website_sale_stock_delivery.py
addons/website_sale_stock/tests/test_website_sale_stock_gmc.py
addons/website_sale_stock/tests/test_website_sale_stock_multilang.py
addons/website_sale_stock/tests/test_website_sale_stock_product_combo.py
addons/website_sale_stock/tests/test_website_sale_stock_product_product.py
addons/website_sale_stock/tests/test_website_sale_stock_product_template.py
addons/website_sale_stock/tests/test_website_sale_stock_product_warehouse.py
addons/website_sale_stock/tests/test_website_sale_stock_reorder_from_portal.py
addons/website_sale_stock/tests/test_website_sale_stock_sale_order_line.py
addons/website_sale_stock/tests/test_website_sale_stock_stock_message.py
addons/website_sale_stock/tests/test_website_sale_stock_stock_notification.py
addons/website_sale_stock_wishlist/__init__.py
addons/website_sale_stock_wishlist/__manifest__.py
addons/website_sale_stock_wishlist/controllers/__init__.py
addons/website_sale_stock_wishlist/controllers/variant.py
addons/website_sale_stock_wishlist/models/__init__.py
addons/website_sale_stock_wishlist/models/product_template.py
addons/website_sale_stock_wishlist/models/product_wishlist.py
addons/website_sale_wishlist/__init__.py
addons/website_sale_wishlist/__manifest__.py
addons/website_sale_wishlist/controllers/__init__.py
addons/website_sale_wishlist/controllers/main.py
addons/website_sale_wishlist/controllers/website_sale.py
addons/website_sale_wishlist/models/__init__.py
addons/website_sale_wishlist/models/product_wishlist.py
addons/website_sale_wishlist/models/res_users.py
addons/website_sale_wishlist/models/website.py
addons/website_sale_wishlist/tests/__init__.py
addons/website_sale_wishlist/tests/test_wishlist_process.py
addons/website_slides/__init__.py
addons/website_slides/__manifest__.py
addons/website_slides/controllers/__init__.py
addons/website_slides/controllers/legacy.py
addons/website_slides/controllers/main.py
addons/website_slides/models/__init__.py
addons/website_slides/models/gamification_challenge.py
addons/website_slides/models/gamification_karma_tracking.py
addons/website_slides/models/mail_activity.py
addons/website_slides/models/res_config_settings.py
addons/website_slides/models/res_groups.py
addons/website_slides/models/res_partner.py
addons/website_slides/models/res_users.py
addons/website_slides/models/slide_channel.py
addons/website_slides/models/slide_channel_partner.py
addons/website_slides/models/slide_channel_tag.py
addons/website_slides/models/slide_embed.py
addons/website_slides/models/slide_question.py
addons/website_slides/models/slide_slide.py
addons/website_slides/models/slide_slide_partner.py
addons/website_slides/models/slide_slide_resource.py
addons/website_slides/models/slide_tag.py
addons/website_slides/models/website.py
addons/website_slides/tests/__init__.py
addons/website_slides/tests/common.py
addons/website_slides/tests/test_attendee.py
addons/website_slides/tests/test_embed_detection.py
addons/website_slides/tests/test_gamification_karma.py
addons/website_slides/tests/test_load_chatter_bundle.py
addons/website_slides/tests/test_mail.py
addons/website_slides/tests/test_security.py
addons/website_slides/tests/test_slide_channel.py
addons/website_slides/tests/test_slide_question.py
addons/website_slides/tests/test_slide_resource.py
addons/website_slides/tests/test_slide_slide.py
addons/website_slides/tests/test_statistics.py
addons/website_slides/tests/test_ui_wslides.py
addons/website_slides/tests/test_website_slides_technical_page.py
addons/website_slides/wizard/__init__.py
addons/website_slides/wizard/slide_channel_invite.py
addons/website_slides_forum/__init__.py
addons/website_slides_forum/__manifest__.py
addons/website_slides_forum/controllers/__init__.py
addons/website_slides_forum/controllers/main.py
addons/website_slides_forum/models/__init__.py
addons/website_slides_forum/models/forum_forum.py
addons/website_slides_forum/models/slide_channel.py
addons/website_slides_survey/__init__.py
addons/website_slides_survey/__manifest__.py
addons/website_slides_survey/controllers/__init__.py
addons/website_slides_survey/controllers/slides.py
addons/website_slides_survey/controllers/survey.py
addons/website_slides_survey/controllers/website_profile.py
addons/website_slides_survey/models/__init__.py
addons/website_slides_survey/models/slide_channel.py
addons/website_slides_survey/models/slide_slide.py
addons/website_slides_survey/models/survey_survey.py
addons/website_slides_survey/models/survey_user.py
addons/website_slides_survey/tests/__init__.py
addons/website_slides_survey/tests/test_course_certification_failure.py
addons/website_slides_survey/tests/test_course_certification_stats.py
addons/website_slides_survey/tests/test_course_certification_unlink.py
addons/website_sms/__init__.py
addons/website_sms/__manifest__.py
addons/website_sms/models/__init__.py
addons/website_sms/models/website_visitor.py
addons/website_timesheet/__init__.py
addons/website_timesheet/__manifest__.py
addons/website_timesheet/models/__init__.py
addons/website_timesheet/models/account_analytic_line.py
doc/cla/stats.py
odoo/__main__.py
odoo/exceptions.py
odoo/http.py
odoo/import_xml.rng
odoo/init.py
odoo/loglevels.py
odoo/netsvc.py
odoo/release.py
odoo/sql_db.py
odoo.egg-info/PKG-INFO
odoo.egg-info/SOURCES.txt
odoo.egg-info/dependency_links.txt
odoo.egg-info/requires.txt
odoo.egg-info/top_level.txt
odoo/_monkeypatches/__init__.py
odoo/_monkeypatches/ast.py
odoo/_monkeypatches/bs4.py
odoo/_monkeypatches/csv.py
odoo/_monkeypatches/docutils.py
odoo/_monkeypatches/email.py
odoo/_monkeypatches/locale.py
odoo/_monkeypatches/lxml.py
odoo/_monkeypatches/mimetypes.py
odoo/_monkeypatches/num2words.py
odoo/_monkeypatches/pytz.py
odoo/_monkeypatches/re.py
odoo/_monkeypatches/site.py
odoo/_monkeypatches/stdnum.py
odoo/_monkeypatches/urllib3.py
odoo/_monkeypatches/werkzeug.py
odoo/_monkeypatches/xlrd.py
odoo/_monkeypatches/xlsxwriter.py
odoo/_monkeypatches/xlwt.py
odoo/_monkeypatches/zeep.py
odoo/addons/.gitkeep
odoo/addons/base/__init__.py
odoo/addons/base/__manifest__.py
odoo/addons/base/data/base_data.sql
odoo/addons/base/data/ir_config_parameter_data.xml
odoo/addons/base/data/ir_cron_data.xml
odoo/addons/base/data/ir_demo_data.xml
odoo/addons/base/data/ir_demo_failure_data.xml
odoo/addons/base/data/ir_module_category_data.xml
odoo/addons/base/data/ir_module_module.xml
odoo/addons/base/data/neutralize.sql
odoo/addons/base/data/report_paperformat_data.xml
odoo/addons/base/data/res.country.state.csv
odoo/addons/base/data/res.lang.csv
odoo/addons/base/data/res_bank.xml
odoo/addons/base/data/res_bank_demo.xml
odoo/addons/base/data/res_company_data.xml
odoo/addons/base/data/res_country_data.xml
odoo/addons/base/data/res_currency_data.xml
odoo/addons/base/data/res_currency_demo.xml
odoo/addons/base/data/res_currency_rate_demo.xml
odoo/addons/base/data/res_lang_data.xml
odoo/addons/base/data/res_partner_bank_demo.xml
odoo/addons/base/data/res_partner_data.xml
odoo/addons/base/data/res_partner_demo.xml
odoo/addons/base/data/res_partner_image_demo.xml
odoo/addons/base/data/res_users_data.xml
odoo/addons/base/data/res_users_demo.xml
odoo/addons/base/data/rtlcss.json
odoo/addons/base/i18n/af.po
odoo/addons/base/i18n/am.po
odoo/addons/base/i18n/ar.po
odoo/addons/base/i18n/az.po
odoo/addons/base/i18n/base.pot
odoo/addons/base/i18n/bg.po
odoo/addons/base/i18n/bs.po
odoo/addons/base/i18n/ca.po
odoo/addons/base/i18n/cs.po
odoo/addons/base/i18n/da.po
odoo/addons/base/i18n/de.po
odoo/addons/base/i18n/el.po
odoo/addons/base/i18n/es.po
odoo/addons/base/i18n/es_419.po
odoo/addons/base/i18n/es_CL.po
odoo/addons/base/i18n/et.po
odoo/addons/base/i18n/eu.po
odoo/addons/base/i18n/fa.po
odoo/addons/base/i18n/fi.po
odoo/addons/base/i18n/fo.po
odoo/addons/base/i18n/fr.po
odoo/addons/base/i18n/fr_BE.po
odoo/addons/base/i18n/fr_CA.po
odoo/addons/base/i18n/gl.po
odoo/addons/base/i18n/gu.po
odoo/addons/base/i18n/he.po
odoo/addons/base/i18n/hi.po
odoo/addons/base/i18n/hr.po
odoo/addons/base/i18n/hu.po
odoo/addons/base/i18n/id.po
odoo/addons/base/i18n/is.po
odoo/addons/base/i18n/it.po
odoo/addons/base/i18n/ja.po
odoo/addons/base/i18n/ka.po
odoo/addons/base/i18n/kab.po
odoo/addons/base/i18n/km.po
odoo/addons/base/i18n/ko.po
odoo/addons/base/i18n/ku.po
odoo/addons/base/i18n/lb.po
odoo/addons/base/i18n/lo.po
odoo/addons/base/i18n/lt.po
odoo/addons/base/i18n/lv.po
odoo/addons/base/i18n/mk.po
odoo/addons/base/i18n/mn.po
odoo/addons/base/i18n/my.po
odoo/addons/base/i18n/nb.po
odoo/addons/base/i18n/nl.po
odoo/addons/base/i18n/pl.po
odoo/addons/base/i18n/pt.po
odoo/addons/base/i18n/pt_BR.po
odoo/addons/base/i18n/ro.po
odoo/addons/base/i18n/ru.po
odoo/addons/base/i18n/sk.po
odoo/addons/base/i18n/sl.po
odoo/addons/base/i18n/sq.po
odoo/addons/base/i18n/<EMAIL>
odoo/addons/base/i18n/sv.po
odoo/addons/base/i18n/th.po
odoo/addons/base/i18n/tr.po
odoo/addons/base/i18n/uk.po
odoo/addons/base/i18n/vi.po
odoo/addons/base/i18n/zh_CN.po
odoo/addons/base/i18n/zh_TW.po
odoo/addons/base/models/__init__.py
odoo/addons/base/models/assetsbundle.py
odoo/addons/base/models/avatar_mixin.py
odoo/addons/base/models/decimal_precision.py
odoo/addons/base/models/image_mixin.py
odoo/addons/base/models/ir_actions.py
odoo/addons/base/models/ir_actions_report.py
odoo/addons/base/models/ir_asset.py
odoo/addons/base/models/ir_attachment.py
odoo/addons/base/models/ir_autovacuum.py
odoo/addons/base/models/ir_binary.py
odoo/addons/base/models/ir_config_parameter.py
odoo/addons/base/models/ir_cron.py
odoo/addons/base/models/ir_default.py
odoo/addons/base/models/ir_demo.py
odoo/addons/base/models/ir_demo_failure.py
odoo/addons/base/models/ir_embedded_actions.py
odoo/addons/base/models/ir_exports.py
odoo/addons/base/models/ir_fields.py
odoo/addons/base/models/ir_filters.py
odoo/addons/base/models/ir_http.py
odoo/addons/base/models/ir_logging.py
odoo/addons/base/models/ir_mail_server.py
odoo/addons/base/models/ir_model.py
odoo/addons/base/models/ir_module.py
odoo/addons/base/models/ir_profile.py
odoo/addons/base/models/ir_qweb.py
odoo/addons/base/models/ir_qweb_fields.py
odoo/addons/base/models/ir_rule.py
odoo/addons/base/models/ir_sequence.py
odoo/addons/base/models/ir_ui_menu.py
odoo/addons/base/models/ir_ui_view.py
odoo/addons/base/models/properties_base_definition.py
odoo/addons/base/models/properties_base_definition_mixin.py
odoo/addons/base/models/report_layout.py
odoo/addons/base/models/report_paperformat.py
odoo/addons/base/models/res_bank.py
odoo/addons/base/models/res_company.py
odoo/addons/base/models/res_config.py
odoo/addons/base/models/res_country.py
odoo/addons/base/models/res_currency.py
odoo/addons/base/models/res_device.py
odoo/addons/base/models/res_groups.py
odoo/addons/base/models/res_groups_privilege.py
odoo/addons/base/models/res_lang.py
odoo/addons/base/models/res_partner.py
odoo/addons/base/models/res_users.py
odoo/addons/base/models/res_users_deletion.py
odoo/addons/base/models/res_users_settings.py
odoo/addons/base/report/__init__.py
odoo/addons/base/report/corporate_defaults.xsl
odoo/addons/base/report/custom_default_printscreen.xsl
odoo/addons/base/report/custom_new.xsl
odoo/addons/base/report/custom_rml_printscreen.xsl
odoo/addons/base/report/ir_model_report.xml
odoo/addons/base/report/ir_model_templates.xml
odoo/addons/base/report/ir_module_report_templates.xml
odoo/addons/base/report/ir_module_reports.xml
odoo/addons/base/report/mako_footer.html
odoo/addons/base/report/mako_header.html
odoo/addons/base/report/mako_template.css
odoo/addons/base/report/report_base_report_irmodulereference.py
odoo/addons/base/rng/activity_view.rng
odoo/addons/base/rng/calendar_view.rng
odoo/addons/base/rng/common.rng
odoo/addons/base/rng/graph_view.rng
odoo/addons/base/rng/list_view.rng
odoo/addons/base/rng/pivot_view.rng
odoo/addons/base/rng/search_view.rng
odoo/addons/base/security/base_groups.xml
odoo/addons/base/security/base_security.xml
odoo/addons/base/security/ir.model.access.csv
odoo/addons/base/static/description/board.png
odoo/addons/base/static/description/board.svg
odoo/addons/base/static/description/exception.png
odoo/addons/base/static/description/exception.svg
odoo/addons/base/static/description/icon.png
odoo/addons/base/static/description/icon.svg
odoo/addons/base/static/description/modules.png
odoo/addons/base/static/description/modules.svg
odoo/addons/base/static/description/settings.png
odoo/addons/base/static/description/settings.svg
odoo/addons/base/static/img/avatar.png
odoo/addons/base/static/img/avatar_grey.png
odoo/addons/base/static/img/bill.png
odoo/addons/base/static/img/company_image.png
odoo/addons/base/static/img/demo_logo_report.png
odoo/addons/base/static/img/logo_sample.png
odoo/addons/base/static/img/logo_white.png
odoo/addons/base/static/img/main_partner-image.png
odoo/addons/base/static/img/money.png
odoo/addons/base/static/img/onboarding_accounting-periods.png
odoo/addons/base/static/img/onboarding_calendar.png
odoo/addons/base/static/img/onboarding_chart-of-accounts.png
odoo/addons/base/static/img/onboarding_cog.png
odoo/addons/base/static/img/onboarding_company-data.png
odoo/addons/base/static/img/onboarding_confetti.svg
odoo/addons/base/static/img/onboarding_default.png
odoo/addons/base/static/img/onboarding_looking_glass.png
odoo/addons/base/static/img/onboarding_puzzle.png
odoo/addons/base/static/img/onboarding_quotation-layout.png
odoo/addons/base/static/img/onboarding_sample-quotation.png
odoo/addons/base/static/img/onboarding_taxes.png
odoo/addons/base/static/img/partner_demo_portal.png
odoo/addons/base/static/img/partner_lightsup.png
odoo/addons/base/static/img/partner_open_wood.png
odoo/addons/base/static/img/partner_root-image.png
odoo/addons/base/static/img/public_user-image.png
odoo/addons/base/static/img/puzzle.png
odoo/addons/base/static/img/res_company_logo.png
odoo/addons/base/static/img/res_partner_1-image.png
odoo/addons/base/static/img/res_partner_10-image.jpg
odoo/addons/base/static/img/res_partner_12-image.png
odoo/addons/base/static/img/res_partner_18-image.png
odoo/addons/base/static/img/res_partner_2-image.png
odoo/addons/base/static/img/res_partner_3-image.png
odoo/addons/base/static/img/res_partner_4-image.png
odoo/addons/base/static/img/res_partner_address_1.jpg
odoo/addons/base/static/img/res_partner_address_10.jpg
odoo/addons/base/static/img/res_partner_address_11.jpg
odoo/addons/base/static/img/res_partner_address_13.jpg
odoo/addons/base/static/img/res_partner_address_14.jpg
odoo/addons/base/static/img/res_partner_address_15.jpg
odoo/addons/base/static/img/res_partner_address_16.jpg
odoo/addons/base/static/img/res_partner_address_17.jpg
odoo/addons/base/static/img/res_partner_address_18.jpg
odoo/addons/base/static/img/res_partner_address_2.jpg
odoo/addons/base/static/img/res_partner_address_24.jpg
odoo/addons/base/static/img/res_partner_address_25.jpg
odoo/addons/base/static/img/res_partner_address_27.jpg
odoo/addons/base/static/img/res_partner_address_28.jpg
odoo/addons/base/static/img/res_partner_address_3.jpg
odoo/addons/base/static/img/res_partner_address_30.jpg
odoo/addons/base/static/img/res_partner_address_31.jpg
odoo/addons/base/static/img/res_partner_address_32.jpg
odoo/addons/base/static/img/res_partner_address_33.jpg
odoo/addons/base/static/img/res_partner_address_34.jpg
odoo/addons/base/static/img/res_partner_address_4.jpg
odoo/addons/base/static/img/res_partner_address_5.jpg
odoo/addons/base/static/img/res_partner_address_7.jpg
odoo/addons/base/static/img/res_partner_main1.jpg
odoo/addons/base/static/img/res_partner_main2.jpg
odoo/addons/base/static/img/truck.png
odoo/addons/base/static/img/user-slash.png
odoo/addons/base/static/img/user_demo-image.png
odoo/addons/base/static/img/country_flags/419.png
odoo/addons/base/static/img/country_flags/ad.png
odoo/addons/base/static/img/country_flags/ae.png
odoo/addons/base/static/img/country_flags/af.png
odoo/addons/base/static/img/country_flags/ag.png
odoo/addons/base/static/img/country_flags/ai.png
odoo/addons/base/static/img/country_flags/al.png
odoo/addons/base/static/img/country_flags/am.png
odoo/addons/base/static/img/country_flags/an.png
odoo/addons/base/static/img/country_flags/ao.png
odoo/addons/base/static/img/country_flags/ar.png
odoo/addons/base/static/img/country_flags/as.png
odoo/addons/base/static/img/country_flags/at.png
odoo/addons/base/static/img/country_flags/au.png
odoo/addons/base/static/img/country_flags/aw.png
odoo/addons/base/static/img/country_flags/ax.png
odoo/addons/base/static/img/country_flags/az.png
odoo/addons/base/static/img/country_flags/ba.png
odoo/addons/base/static/img/country_flags/bb.png
odoo/addons/base/static/img/country_flags/bd.png
odoo/addons/base/static/img/country_flags/be.png
odoo/addons/base/static/img/country_flags/bf.png
odoo/addons/base/static/img/country_flags/bg.png
odoo/addons/base/static/img/country_flags/bh.png
odoo/addons/base/static/img/country_flags/bi.png
odoo/addons/base/static/img/country_flags/bj.png
odoo/addons/base/static/img/country_flags/bl.png
odoo/addons/base/static/img/country_flags/bm.png
odoo/addons/base/static/img/country_flags/bn.png
odoo/addons/base/static/img/country_flags/bo.png
odoo/addons/base/static/img/country_flags/br.png
odoo/addons/base/static/img/country_flags/bs.png
odoo/addons/base/static/img/country_flags/bt.png
odoo/addons/base/static/img/country_flags/bw.png
odoo/addons/base/static/img/country_flags/by.png
odoo/addons/base/static/img/country_flags/bz.png
odoo/addons/base/static/img/country_flags/ca.png
odoo/addons/base/static/img/country_flags/cc.png
odoo/addons/base/static/img/country_flags/cd.png
odoo/addons/base/static/img/country_flags/cf.png
odoo/addons/base/static/img/country_flags/cg.png
odoo/addons/base/static/img/country_flags/ch.png
odoo/addons/base/static/img/country_flags/ci.png
odoo/addons/base/static/img/country_flags/ck.png
odoo/addons/base/static/img/country_flags/cl.png
odoo/addons/base/static/img/country_flags/cm.png
odoo/addons/base/static/img/country_flags/cn.png
odoo/addons/base/static/img/country_flags/co.png
odoo/addons/base/static/img/country_flags/cr.png
odoo/addons/base/static/img/country_flags/cu.png
odoo/addons/base/static/img/country_flags/cv.png
odoo/addons/base/static/img/country_flags/cw.png
odoo/addons/base/static/img/country_flags/cx.png
odoo/addons/base/static/img/country_flags/cy.png
odoo/addons/base/static/img/country_flags/cz.png
odoo/addons/base/static/img/country_flags/de.png
odoo/addons/base/static/img/country_flags/dj.png
odoo/addons/base/static/img/country_flags/dk.png
odoo/addons/base/static/img/country_flags/dm.png
odoo/addons/base/static/img/country_flags/do.png
odoo/addons/base/static/img/country_flags/dz.png
odoo/addons/base/static/img/country_flags/ec.png
odoo/addons/base/static/img/country_flags/ee.png
odoo/addons/base/static/img/country_flags/eg.png
odoo/addons/base/static/img/country_flags/eh.png
odoo/addons/base/static/img/country_flags/er.png
odoo/addons/base/static/img/country_flags/es.png
odoo/addons/base/static/img/country_flags/et.png
odoo/addons/base/static/img/country_flags/fi.png
odoo/addons/base/static/img/country_flags/fj.png
odoo/addons/base/static/img/country_flags/fk.png
odoo/addons/base/static/img/country_flags/fm.png
odoo/addons/base/static/img/country_flags/fo.png
odoo/addons/base/static/img/country_flags/fr.png
odoo/addons/base/static/img/country_flags/ga.png
odoo/addons/base/static/img/country_flags/gb.png
odoo/addons/base/static/img/country_flags/gd.png
odoo/addons/base/static/img/country_flags/ge.png
odoo/addons/base/static/img/country_flags/gg.png
odoo/addons/base/static/img/country_flags/gh.png
odoo/addons/base/static/img/country_flags/gi.png
odoo/addons/base/static/img/country_flags/gl.png
odoo/addons/base/static/img/country_flags/gm.png
odoo/addons/base/static/img/country_flags/gn.png
odoo/addons/base/static/img/country_flags/gq.png
odoo/addons/base/static/img/country_flags/gr.png
odoo/addons/base/static/img/country_flags/gs.png
odoo/addons/base/static/img/country_flags/gt.png
odoo/addons/base/static/img/country_flags/gu.png
odoo/addons/base/static/img/country_flags/gw.png
odoo/addons/base/static/img/country_flags/gy.png
odoo/addons/base/static/img/country_flags/hk.png
odoo/addons/base/static/img/country_flags/hn.png
odoo/addons/base/static/img/country_flags/hr.png
odoo/addons/base/static/img/country_flags/ht.png
odoo/addons/base/static/img/country_flags/hu.png
odoo/addons/base/static/img/country_flags/id.png
odoo/addons/base/static/img/country_flags/ie.png
odoo/addons/base/static/img/country_flags/il.png
odoo/addons/base/static/img/country_flags/im.png
odoo/addons/base/static/img/country_flags/in.png
odoo/addons/base/static/img/country_flags/io.png
odoo/addons/base/static/img/country_flags/iq.png
odoo/addons/base/static/img/country_flags/ir.png
odoo/addons/base/static/img/country_flags/iran.png
odoo/addons/base/static/img/country_flags/is.png
odoo/addons/base/static/img/country_flags/it.png
odoo/addons/base/static/img/country_flags/je.png
odoo/addons/base/static/img/country_flags/jm.png
odoo/addons/base/static/img/country_flags/jo.png
odoo/addons/base/static/img/country_flags/jp.png
odoo/addons/base/static/img/country_flags/ke.png
odoo/addons/base/static/img/country_flags/kg.png
odoo/addons/base/static/img/country_flags/kh.png
odoo/addons/base/static/img/country_flags/ki.png
odoo/addons/base/static/img/country_flags/km.png
odoo/addons/base/static/img/country_flags/kn.png
odoo/addons/base/static/img/country_flags/kp.png
odoo/addons/base/static/img/country_flags/kr.png
odoo/addons/base/static/img/country_flags/kw.png
odoo/addons/base/static/img/country_flags/ky.png
odoo/addons/base/static/img/country_flags/kz.png
odoo/addons/base/static/img/country_flags/la.png
odoo/addons/base/static/img/country_flags/lb.png
odoo/addons/base/static/img/country_flags/lc.png
odoo/addons/base/static/img/country_flags/li.png
odoo/addons/base/static/img/country_flags/lk.png
odoo/addons/base/static/img/country_flags/lr.png
odoo/addons/base/static/img/country_flags/ls.png
odoo/addons/base/static/img/country_flags/lt.png
odoo/addons/base/static/img/country_flags/lu.png
odoo/addons/base/static/img/country_flags/lv.png
odoo/addons/base/static/img/country_flags/ly.png
odoo/addons/base/static/img/country_flags/ma.png
odoo/addons/base/static/img/country_flags/mc.png
odoo/addons/base/static/img/country_flags/md.png
odoo/addons/base/static/img/country_flags/me.png
odoo/addons/base/static/img/country_flags/mg.png
odoo/addons/base/static/img/country_flags/mh.png
odoo/addons/base/static/img/country_flags/mk.png
odoo/addons/base/static/img/country_flags/ml.png
odoo/addons/base/static/img/country_flags/mm.png
odoo/addons/base/static/img/country_flags/mn.png
odoo/addons/base/static/img/country_flags/mo.png
odoo/addons/base/static/img/country_flags/mp.png
odoo/addons/base/static/img/country_flags/mq.png
odoo/addons/base/static/img/country_flags/mr.png
odoo/addons/base/static/img/country_flags/ms.png
odoo/addons/base/static/img/country_flags/mt.png
odoo/addons/base/static/img/country_flags/mu.png
odoo/addons/base/static/img/country_flags/mv.png
odoo/addons/base/static/img/country_flags/mw.png
odoo/addons/base/static/img/country_flags/mx.png
odoo/addons/base/static/img/country_flags/my.png
odoo/addons/base/static/img/country_flags/mz.png
odoo/addons/base/static/img/country_flags/na.png
odoo/addons/base/static/img/country_flags/nc.png
odoo/addons/base/static/img/country_flags/ne.png
odoo/addons/base/static/img/country_flags/nf.png
odoo/addons/base/static/img/country_flags/ng.png
odoo/addons/base/static/img/country_flags/ni.png
odoo/addons/base/static/img/country_flags/nl.png
odoo/addons/base/static/img/country_flags/no.png
odoo/addons/base/static/img/country_flags/np.png
odoo/addons/base/static/img/country_flags/nr.png
odoo/addons/base/static/img/country_flags/nu.png
odoo/addons/base/static/img/country_flags/nz.png
odoo/addons/base/static/img/country_flags/om.png
odoo/addons/base/static/img/country_flags/pa.png
odoo/addons/base/static/img/country_flags/pe.png
odoo/addons/base/static/img/country_flags/pf.png
odoo/addons/base/static/img/country_flags/pg.png
odoo/addons/base/static/img/country_flags/ph.png
odoo/addons/base/static/img/country_flags/pk.png
odoo/addons/base/static/img/country_flags/pl.png
odoo/addons/base/static/img/country_flags/pm.png
odoo/addons/base/static/img/country_flags/pn.png
odoo/addons/base/static/img/country_flags/pr.png
odoo/addons/base/static/img/country_flags/ps.png
odoo/addons/base/static/img/country_flags/pt.png
odoo/addons/base/static/img/country_flags/pw.png
odoo/addons/base/static/img/country_flags/py.png
odoo/addons/base/static/img/country_flags/qa.png
odoo/addons/base/static/img/country_flags/ro.png
odoo/addons/base/static/img/country_flags/rs.png
odoo/addons/base/static/img/country_flags/ru.png
odoo/addons/base/static/img/country_flags/rw.png
odoo/addons/base/static/img/country_flags/sa.png
odoo/addons/base/static/img/country_flags/sb.png
odoo/addons/base/static/img/country_flags/sc.png
odoo/addons/base/static/img/country_flags/sd.png
odoo/addons/base/static/img/country_flags/se.png
odoo/addons/base/static/img/country_flags/sg.png
odoo/addons/base/static/img/country_flags/sh.png
odoo/addons/base/static/img/country_flags/si.png
odoo/addons/base/static/img/country_flags/sk.png
odoo/addons/base/static/img/country_flags/sl.png
odoo/addons/base/static/img/country_flags/sm.png
odoo/addons/base/static/img/country_flags/sn.png
odoo/addons/base/static/img/country_flags/so.png
odoo/addons/base/static/img/country_flags/sr.png
odoo/addons/base/static/img/country_flags/ss.png
odoo/addons/base/static/img/country_flags/st.png
odoo/addons/base/static/img/country_flags/sv.png
odoo/addons/base/static/img/country_flags/sx.png
odoo/addons/base/static/img/country_flags/sy.png
odoo/addons/base/static/img/country_flags/sz.png
odoo/addons/base/static/img/country_flags/tc.png
odoo/addons/base/static/img/country_flags/td.png
odoo/addons/base/static/img/country_flags/tf.png
odoo/addons/base/static/img/country_flags/tg.png
odoo/addons/base/static/img/country_flags/th.png
odoo/addons/base/static/img/country_flags/tj.png
odoo/addons/base/static/img/country_flags/tk.png
odoo/addons/base/static/img/country_flags/tl.png
odoo/addons/base/static/img/country_flags/tm.png
odoo/addons/base/static/img/country_flags/tn.png
odoo/addons/base/static/img/country_flags/to.png
odoo/addons/base/static/img/country_flags/tr.png
odoo/addons/base/static/img/country_flags/tt.png
odoo/addons/base/static/img/country_flags/tv.png
odoo/addons/base/static/img/country_flags/tw.png
odoo/addons/base/static/img/country_flags/tz.png
odoo/addons/base/static/img/country_flags/ua.png
odoo/addons/base/static/img/country_flags/ug.png
odoo/addons/base/static/img/country_flags/us.png
odoo/addons/base/static/img/country_flags/uy.png
odoo/addons/base/static/img/country_flags/uz.png
odoo/addons/base/static/img/country_flags/va.png
odoo/addons/base/static/img/country_flags/vc.png
odoo/addons/base/static/img/country_flags/ve.png
odoo/addons/base/static/img/country_flags/vg.png
odoo/addons/base/static/img/country_flags/vi.png
odoo/addons/base/static/img/country_flags/vn.png
odoo/addons/base/static/img/country_flags/vu.png
odoo/addons/base/static/img/country_flags/wf.png
odoo/addons/base/static/img/country_flags/ws.png
odoo/addons/base/static/img/country_flags/xk.png
odoo/addons/base/static/img/country_flags/ye.png
odoo/addons/base/static/img/country_flags/za.png
odoo/addons/base/static/img/country_flags/zm.png
odoo/addons/base/static/img/country_flags/zw.png
odoo/addons/base/static/img/icons/account_accountant.png
odoo/addons/base/static/img/icons/appointment.png
odoo/addons/base/static/img/icons/helpdesk.png
odoo/addons/base/static/img/icons/hr_appraisal.png
odoo/addons/base/static/img/icons/industry_fsm.png
odoo/addons/base/static/img/icons/knowledge.png
odoo/addons/base/static/img/icons/marketing_automation.png
odoo/addons/base/static/img/icons/mrp_plm.png
odoo/addons/base/static/img/icons/mrp_workorder.png
odoo/addons/base/static/img/icons/payment_sepa_direct_debit.png
odoo/addons/base/static/img/icons/planning.png
odoo/addons/base/static/img/icons/quality_control.png
odoo/addons/base/static/img/icons/sale_amazon.png
odoo/addons/base/static/img/icons/sale_ebay.png
odoo/addons/base/static/img/icons/sale_subscription.png
odoo/addons/base/static/img/icons/sign.png
odoo/addons/base/static/img/icons/social.png
odoo/addons/base/static/img/icons/stock_barcode.png
odoo/addons/base/static/img/icons/timesheet_grid.png
odoo/addons/base/static/img/icons/voip.png
odoo/addons/base/static/img/icons/web_mobile.png
odoo/addons/base/static/img/icons/web_studio.png
odoo/addons/base/static/img/icons/website_form_editor.png
odoo/addons/base/static/img/icons/website_version.png
odoo/addons/base/static/img/lang_flags/lang_ar.png
odoo/addons/base/static/img/lang_flags/lang_ca.png
odoo/addons/base/static/src/css/description.css
odoo/addons/base/static/src/css/description.sass
odoo/addons/base/static/src/css/modules.css
odoo/addons/base/static/src/scss/res_partner.scss
odoo/addons/base/static/src/scss/res_users.scss
odoo/addons/base/static/tests/test_ir_model_fields_translation.js
odoo/addons/base/static/xls/contacts_import_template.xlsx
odoo/addons/base/tests/__init__.py
odoo/addons/base/tests/common.py
odoo/addons/base/tests/fire_small.jpeg
odoo/addons/base/tests/mail_examples.py
odoo/addons/base/tests/minimal.pdf
odoo/addons/base/tests/odoo.jpg
odoo/addons/base/tests/shell_file.txt
odoo/addons/base/tests/test_acl.py
odoo/addons/base/tests/test_api.py
odoo/addons/base/tests/test_avatar_mixin.py
odoo/addons/base/tests/test_barcode.py
odoo/addons/base/tests/test_base.py
odoo/addons/base/tests/test_basecase.py
odoo/addons/base/tests/test_cache.py
odoo/addons/base/tests/test_cli.py
odoo/addons/base/tests/test_cloc.py
odoo/addons/base/tests/test_config_parameter.py
odoo/addons/base/tests/test_configmanager.py
odoo/addons/base/tests/test_date_utils.py
odoo/addons/base/tests/test_db_cursor.py
odoo/addons/base/tests/test_deprecation.py
odoo/addons/base/tests/test_display_name.py
odoo/addons/base/tests/test_expression.py
odoo/addons/base/tests/test_float.py
odoo/addons/base/tests/test_form_create.py
odoo/addons/base/tests/test_format_address_mixin.py
odoo/addons/base/tests/test_func.py
odoo/addons/base/tests/test_groups.py
odoo/addons/base/tests/test_http_case.py
odoo/addons/base/tests/test_i18n.py
odoo/addons/base/tests/test_image.py
odoo/addons/base/tests/test_import_files.py
odoo/addons/base/tests/test_init.py
odoo/addons/base/tests/test_install.py
odoo/addons/base/tests/test_intervals.py
odoo/addons/base/tests/test_ir_actions.py
odoo/addons/base/tests/test_ir_attachment.py
odoo/addons/base/tests/test_ir_cron.py
odoo/addons/base/tests/test_ir_default.py
odoo/addons/base/tests/test_ir_embedded_actions.py
odoo/addons/base/tests/test_ir_filters.py
odoo/addons/base/tests/test_ir_http.py
odoo/addons/base/tests/test_ir_mail_server.py
odoo/addons/base/tests/test_ir_mail_server_smtpd.py
odoo/addons/base/tests/test_ir_model.py
odoo/addons/base/tests/test_ir_module.py
odoo/addons/base/tests/test_ir_module_category.py
odoo/addons/base/tests/test_ir_sequence.py
odoo/addons/base/tests/test_ir_sequence_date_range.py
odoo/addons/base/tests/test_mail.py
odoo/addons/base/tests/test_menu.py
odoo/addons/base/tests/test_mimetypes.py
odoo/addons/base/tests/test_misc.py
odoo/addons/base/tests/test_module.py
odoo/addons/base/tests/test_module_graph.py
odoo/addons/base/tests/test_neutralize.py
odoo/addons/base/tests/test_num2words_ar.py
odoo/addons/base/tests/test_orm.py
odoo/addons/base/tests/test_ormcache.py
odoo/addons/base/tests/test_overrides.py
odoo/addons/base/tests/test_pdf.py
odoo/addons/base/tests/test_profiler.py
odoo/addons/base/tests/test_query.py
odoo/addons/base/tests/test_qweb.py
odoo/addons/base/tests/test_qweb_field.py
odoo/addons/base/tests/test_reports.py
odoo/addons/base/tests/test_res_company.py
odoo/addons/base/tests/test_res_config.py
odoo/addons/base/tests/test_res_country.py
odoo/addons/base/tests/test_res_currency.py
odoo/addons/base/tests/test_res_lang.py
odoo/addons/base/tests/test_res_partner.py
odoo/addons/base/tests/test_res_partner_bank.py
odoo/addons/base/tests/test_res_partner_merge.py
odoo/addons/base/tests/test_res_users.py
odoo/addons/base/tests/test_search.py
odoo/addons/base/tests/test_signature.py
odoo/addons/base/tests/test_split_table.py
odoo/addons/base/tests/test_sql.py
odoo/addons/base/tests/test_test_retry.py
odoo/addons/base/tests/test_test_suite.py
odoo/addons/base/tests/test_tests_tags.py
odoo/addons/base/tests/test_transactions.py
odoo/addons/base/tests/test_translate.py
odoo/addons/base/tests/test_tz.py
odoo/addons/base/tests/test_uninstall.py
odoo/addons/base/tests/test_user_has_group.py
odoo/addons/base/tests/test_views.py
odoo/addons/base/tests/tommy_small.jpeg
odoo/addons/base/tests/config/16.0.conf
odoo/addons/base/tests/config/cli
odoo/addons/base/tests/config/empty.conf
odoo/addons/base/tests/config/environ
odoo/addons/base/tests/config/multidb.conf
odoo/addons/base/tests/config/non_default.conf
odoo/addons/base/tests/config/save_posix.conf
odoo/addons/base/tests/config/sysloglogfile.conf
odoo/addons/base/tests/file_template/file_expected_render.xml
odoo/addons/base/tests/file_template/unreadable_file_template.xml
odoo/addons/base/tests/file_template/templates/file_template.xml
odoo/addons/base/tests/file_template/templates/subdir/file_subtemplate.xml
odoo/addons/base/tests/split_table/copy_attributes.split1.xml
odoo/addons/base/tests/split_table/copy_attributes.xml
odoo/addons/base/tests/split_table/first_nested.split2.xml
odoo/addons/base/tests/split_table/first_nested.xml
odoo/addons/base/tests/split_table/nested.split2.xml
odoo/addons/base/tests/split_table/nested.xml
odoo/addons/base/tests/split_table/simple.split1.xml
odoo/addons/base/tests/split_table/simple.split2.xml
odoo/addons/base/tests/split_table/simple.xml
odoo/addons/base/tests/ssl/ca.cert.pem
odoo/addons/base/tests/ssl/client.cert.pem
odoo/addons/base/tests/ssl/client.key.pem
odoo/addons/base/tests/ssl/self_signed.cert.pem
odoo/addons/base/tests/ssl/self_signed.key.pem
odoo/addons/base/tests/ssl/server.cert.pem
odoo/addons/base/tests/ssl/server.key.pem
odoo/addons/base/tests/test_install_addons/test_isolated_install.md
odoo/addons/base/tests/test_install_addons/test_install_auto/__init__.py
odoo/addons/base/tests/test_install_addons/test_install_auto/__manifest__.py
odoo/addons/base/tests/test_install_addons/test_install_auto/data/ir_cron.xml
odoo/addons/base/tests/test_install_addons/test_install_auto/models/__init__.py
odoo/addons/base/tests/test_install_addons/test_install_auto/models/res_currency.py
odoo/addons/base/tests/test_install_addons/test_install_base/__init__.py
odoo/addons/base/tests/test_install_addons/test_install_base/__manifest__.py
odoo/addons/base/tests/test_install_addons/test_install_fail/__init__.py
odoo/addons/base/tests/test_install_addons/test_install_fail/__manifest__.py
odoo/addons/base/tests/test_install_addons/test_install_fail/views/res_currency_views.xml
odoo/addons/base/views/base_menus.xml
odoo/addons/base/views/decimal_precision_views.xml
odoo/addons/base/views/ir_actions_views.xml
odoo/addons/base/views/ir_asset_views.xml
odoo/addons/base/views/ir_attachment_views.xml
odoo/addons/base/views/ir_config_parameter_views.xml
odoo/addons/base/views/ir_cron_trigger_views.xml
odoo/addons/base/views/ir_cron_views.xml
odoo/addons/base/views/ir_default_views.xml
odoo/addons/base/views/ir_filters_views.xml
odoo/addons/base/views/ir_logging_views.xml
odoo/addons/base/views/ir_mail_server_views.xml
odoo/addons/base/views/ir_model_views.xml
odoo/addons/base/views/ir_module_views.xml
odoo/addons/base/views/ir_profile_views.xml
odoo/addons/base/views/ir_qweb_widget_templates.xml
odoo/addons/base/views/ir_rule_views.xml
odoo/addons/base/views/ir_sequence_views.xml
odoo/addons/base/views/ir_ui_menu_views.xml
odoo/addons/base/views/ir_ui_view_views.xml
odoo/addons/base/views/report_paperformat_views.xml
odoo/addons/base/views/res_bank_views.xml
odoo/addons/base/views/res_company_views.xml
odoo/addons/base/views/res_config_settings_views.xml
odoo/addons/base/views/res_config_views.xml
odoo/addons/base/views/res_country_views.xml
odoo/addons/base/views/res_currency_views.xml
odoo/addons/base/views/res_device_views.xml
odoo/addons/base/views/res_groups_views.xml
odoo/addons/base/views/res_lang_views.xml
odoo/addons/base/views/res_partner_views.xml
odoo/addons/base/views/res_users_apikeys_views.xml
odoo/addons/base/views/res_users_identitycheck_views.xml
odoo/addons/base/views/res_users_views.xml
odoo/addons/base/wizard/__init__.py
odoo/addons/base/wizard/base_export_language.py
odoo/addons/base/wizard/base_export_language_views.xml
odoo/addons/base/wizard/base_import_language.py
odoo/addons/base/wizard/base_import_language_views.xml
odoo/addons/base/wizard/base_language_install.py
odoo/addons/base/wizard/base_language_install_views.xml
odoo/addons/base/wizard/base_module_uninstall.py
odoo/addons/base/wizard/base_module_uninstall_views.xml
odoo/addons/base/wizard/base_module_update.py
odoo/addons/base/wizard/base_module_update_views.xml
odoo/addons/base/wizard/base_module_upgrade.py
odoo/addons/base/wizard/base_module_upgrade_views.xml
odoo/addons/base/wizard/base_partner_merge.py
odoo/addons/base/wizard/base_partner_merge_views.xml
odoo/addons/base/wizard/wizard_ir_model_menu_create.py
odoo/addons/base/wizard/wizard_ir_model_menu_create_views.xml
odoo/addons/test_access_rights/__init__.py
odoo/addons/test_access_rights/__manifest__.py
odoo/addons/test_access_rights/data.xml
odoo/addons/test_access_rights/ir.model.access.csv
odoo/addons/test_access_rights/models.py
odoo/addons/test_access_rights/security.xml
odoo/addons/test_access_rights/tests/__init__.py
odoo/addons/test_access_rights/tests/test_access_monetary_related.py
odoo/addons/test_access_rights/tests/test_check_access.py
odoo/addons/test_access_rights/tests/test_feedback.py
odoo/addons/test_access_rights/tests/test_ir_rules.py
odoo/addons/test_action_bindings/__init__.py
odoo/addons/test_action_bindings/__manifest__.py
odoo/addons/test_action_bindings/ir.model.access.csv
odoo/addons/test_action_bindings/models.py
odoo/addons/test_action_bindings/test_data.xml
odoo/addons/test_action_bindings/tests/__init__.py
odoo/addons/test_action_bindings/tests/test_bindings.py
odoo/addons/test_assetsbundle/__init__.py
odoo/addons/test_assetsbundle/__manifest__.py
odoo/addons/test_assetsbundle/controllers/__init__.py
odoo/addons/test_assetsbundle/controllers/main.py
odoo/addons/test_assetsbundle/data/ir_asset.xml
odoo/addons/test_assetsbundle/models/__init__.py
odoo/addons/test_assetsbundle/models/ir_qweb.py
odoo/addons/test_assetsbundle/static/accessible.xml
odoo/addons/test_assetsbundle/static/invalid_src/css/invalid_css.css
odoo/addons/test_assetsbundle/static/invalid_src/xml/invalid_xml.xml
odoo/addons/test_assetsbundle/static/invalid_src/xml/multiple_same_name.xml
odoo/addons/test_assetsbundle/static/invalid_src/xml/second_invalid_xml.xml
odoo/addons/test_assetsbundle/static/invalid_src/xml/template_wo_name.xml
odoo/addons/test_assetsbundle/static/src/css/test_cssfile1.css
odoo/addons/test_assetsbundle/static/src/css/test_cssfile2.css
odoo/addons/test_assetsbundle/static/src/css/test_cssfile3.css
odoo/addons/test_assetsbundle/static/src/css/test_error.scss
odoo/addons/test_assetsbundle/static/src/js/test_jsfile1.js
odoo/addons/test_assetsbundle/static/src/js/test_jsfile2.js
odoo/addons/test_assetsbundle/static/src/js/test_jsfile3.js
odoo/addons/test_assetsbundle/static/src/js/test_jsfile4.js
odoo/addons/test_assetsbundle/static/src/scss/test_file1.scss
odoo/addons/test_assetsbundle/static/src/scss/test_prefix.scss
odoo/addons/test_assetsbundle/static/tests/lazy_component.test.js
odoo/addons/test_assetsbundle/static/tests/test_css_error.js
odoo/addons/test_assetsbundle/static/tests/lazy_test_component/lazy_test_component.js
odoo/addons/test_assetsbundle/static/tests/lazy_test_component/lazy_test_component.scss
odoo/addons/test_assetsbundle/static/tests/lazy_test_component/lazy_test_component.xml
odoo/addons/test_assetsbundle/tests/__init__.py
odoo/addons/test_assetsbundle/tests/test_assetsbundle.py
odoo/addons/test_assetsbundle/tests/test_js_transpiler.py
odoo/addons/test_assetsbundle/tests/test_js_transpiler_regex.py
odoo/addons/test_assetsbundle/views/views.xml
odoo/addons/test_auth_custom/__init__.py
odoo/addons/test_auth_custom/__manifest__.py
odoo/addons/test_auth_custom/tests/__init__.py
odoo/addons/test_auth_custom/tests/test_endpoints.py
odoo/addons/test_convert/__init__.py
odoo/addons/test_convert/__manifest__.py
odoo/addons/test_convert/ir.model.access.csv
odoo/addons/test_convert/models.py
odoo/addons/test_convert/test_file.txt
odoo/addons/test_convert/data/test_translated_field/test_convert.test_model.csv
odoo/addons/test_convert/data/test_translated_field/test_model_data.xml
odoo/addons/test_convert/tests/__init__.py
odoo/addons/test_convert/tests/test_convert.py
odoo/addons/test_convert/tests/test_env.py
odoo/addons/test_converter/__init__.py
odoo/addons/test_converter/__manifest__.py
odoo/addons/test_converter/ir.model.access.csv
odoo/addons/test_converter/models.py
odoo/addons/test_converter/tests/__init__.py
odoo/addons/test_converter/tests/test_html.py
odoo/addons/test_converter/tests/test_vectors/image
odoo/addons/test_converter/tests/test_vectors/pdf
odoo/addons/test_converter/tests/test_vectors/pptx
odoo/addons/test_data_module/__manifest__.py
odoo/addons/test_data_module_install/__init__.py
odoo/addons/test_data_module_install/__manifest__.py
odoo/addons/test_data_module_install/tests/__init__.py
odoo/addons/test_data_module_install/tests/test_data_module_installed.py
odoo/addons/test_http/__init__.py
odoo/addons/test_http/__manifest__.py
odoo/addons/test_http/controllers.py
odoo/addons/test_http/data.xml
odoo/addons/test_http/ir.model.access.csv
odoo/addons/test_http/models.py
odoo/addons/test_http/utils.py
odoo/addons/test_http/views.xml
odoo/addons/test_http/static/src/img/gizeh.png
odoo/addons/test_http/static/src/img/milky_way.jpg
odoo/addons/test_http/static/src/img/pegasus.jpg
odoo/addons/test_http/static/src/img/xss.svg
odoo/addons/test_http/tests/__init__.py
odoo/addons/test_http/tests/test_captcha.py
odoo/addons/test_http/tests/test_common.py
odoo/addons/test_http/tests/test_device.py
odoo/addons/test_http/tests/test_echo_reply.py
odoo/addons/test_http/tests/test_error.py
odoo/addons/test_http/tests/test_greeting.py
odoo/addons/test_http/tests/test_misc.py
odoo/addons/test_http/tests/test_models.py
odoo/addons/test_http/tests/test_registry.py
odoo/addons/test_http/tests/test_security.py
odoo/addons/test_http/tests/test_session.py
odoo/addons/test_http/tests/test_static.py
odoo/addons/test_http/tests/test_web_server.py
odoo/addons/test_http/tests/test_webjson.py
odoo/addons/test_http/tests/test_webjson2.py
odoo/addons/test_http/tests/test_xss.py
odoo/addons/test_inherit/__init__.py
odoo/addons/test_inherit/__manifest__.py
odoo/addons/test_inherit/demo_data.xml
odoo/addons/test_inherit/ir.model.access.csv
odoo/addons/test_inherit/models/__init__.py
odoo/addons/test_inherit/models/mother_base.py
odoo/addons/test_inherit/models/mother_inherit_1.py
odoo/addons/test_inherit/models/mother_inherit_2.py
odoo/addons/test_inherit/models/mother_inherit_3.py
odoo/addons/test_inherit/models/mother_inherit_4.py
odoo/addons/test_inherit/models/test_models.py
odoo/addons/test_inherit/tests/__init__.py
odoo/addons/test_inherit/tests/test_inherit.py
odoo/addons/test_inherit_depends/__init__.py
odoo/addons/test_inherit_depends/__manifest__.py
odoo/addons/test_inherit_depends/models.py
odoo/addons/test_inherit_depends/tests/__init__.py
odoo/addons/test_inherit_depends/tests/test_inherit_depends.py
odoo/addons/test_inherits/__init__.py
odoo/addons/test_inherits/__manifest__.py
odoo/addons/test_inherits/demo_data.xml
odoo/addons/test_inherits/ir.model.access.csv
odoo/addons/test_inherits/models.py
odoo/addons/test_inherits/tests/__init__.py
odoo/addons/test_inherits/tests/test_inherits.py
odoo/addons/test_inherits_depends/__init__.py
odoo/addons/test_inherits_depends/__manifest__.py
odoo/addons/test_inherits_depends/models.py
odoo/addons/test_inherits_depends/tests/__init__.py
odoo/addons/test_inherits_depends/tests/test_inherits.py
odoo/addons/test_lint/__init__.py
odoo/addons/test_lint/__manifest__.py
odoo/addons/test_lint/tests/__init__.py
odoo/addons/test_lint/tests/_odoo_checker_gettext.py
odoo/addons/test_lint/tests/_odoo_checker_sql_injection.py
odoo/addons/test_lint/tests/_odoo_checker_unlink_override.py
odoo/addons/test_lint/tests/_pylint_path_setup.py
odoo/addons/test_lint/tests/eslintrc
odoo/addons/test_lint/tests/lint_case.py
odoo/addons/test_lint/tests/test_checkers.py
odoo/addons/test_lint/tests/test_docstring.py
odoo/addons/test_lint/tests/test_dunderinit.py
odoo/addons/test_lint/tests/test_eslint.py
odoo/addons/test_lint/tests/test_i18n.py
odoo/addons/test_lint/tests/test_index.py
odoo/addons/test_lint/tests/test_jstranslate.py
odoo/addons/test_lint/tests/test_l10n.py
odoo/addons/test_lint/tests/test_manifests.py
odoo/addons/test_lint/tests/test_markers.py
odoo/addons/test_lint/tests/test_naming.py
odoo/addons/test_lint/tests/test_onchange_domains.py
odoo/addons/test_lint/tests/test_orm_import.py
odoo/addons/test_lint/tests/test_override_signatures.py
odoo/addons/test_lint/tests/test_pofile.py
odoo/addons/test_lint/tests/test_pylint.py
odoo/addons/test_lint/tests/test_routes.py
odoo/addons/test_lint/tests/test_test_holes.py
odoo/addons/test_main_flows/__init__.py
odoo/addons/test_main_flows/__manifest__.py
odoo/addons/test_main_flows/models/ir.model.access.csv
odoo/addons/test_main_flows/models/model_multicompany.py
odoo/addons/test_main_flows/static/tests/tours/main_flow.js
odoo/addons/test_main_flows/static/tests/tours/switch_company_access_error_tour.js
odoo/addons/test_main_flows/static/tests/tours/test_company_access_error_redirect_tour.js
odoo/addons/test_main_flows/tests/__init__.py
odoo/addons/test_main_flows/tests/test_flow.py
odoo/addons/test_mimetypes/__init__.py
odoo/addons/test_mimetypes/__manifest__.py
odoo/addons/test_mimetypes/tests/__init__.py
odoo/addons/test_mimetypes/tests/test_guess_mimetypes.py
odoo/addons/test_mimetypes/tests/testfiles/case.2025.xlsx
odoo/addons/test_mimetypes/tests/testfiles/case.csv
odoo/addons/test_mimetypes/tests/testfiles/case.doc
odoo/addons/test_mimetypes/tests/testfiles/case.docx
odoo/addons/test_mimetypes/tests/testfiles/case.gif
odoo/addons/test_mimetypes/tests/testfiles/case.jpg
odoo/addons/test_mimetypes/tests/testfiles/case.ods
odoo/addons/test_mimetypes/tests/testfiles/case.odt
odoo/addons/test_mimetypes/tests/testfiles/case.xls
odoo/addons/test_mimetypes/tests/testfiles/case.xlsx
odoo/addons/test_mimetypes/tests/testfiles/case.zip
odoo/addons/test_orm/__init__.py
odoo/addons/test_orm/__manifest__.py
odoo/addons/test_orm/data/test_orm_data.xml
odoo/addons/test_orm/i18n/fr.po
odoo/addons/test_orm/i18n/test_orm.pot
odoo/addons/test_orm/models/__init__.py
odoo/addons/test_orm/models/test_orm.py
odoo/addons/test_orm/models/test_performance.py
odoo/addons/test_orm/models/test_unity_read.py
odoo/addons/test_orm/security/ir.model.access.csv
odoo/addons/test_orm/security/test_orm_security.xml
odoo/addons/test_orm/static/tests/tours/constraint.js
odoo/addons/test_orm/static/tests/tours/x2many.js
odoo/addons/test_orm/tests/__init__.py
odoo/addons/test_orm/tests/test_attributes.py
odoo/addons/test_orm/tests/test_autovacuum.py
odoo/addons/test_orm/tests/test_company_checks.py
odoo/addons/test_orm/tests/test_domain.py
odoo/addons/test_orm/tests/test_fields.py
odoo/addons/test_orm/tests/test_indexed_translation.py
odoo/addons/test_orm/tests/test_json_field.py
odoo/addons/test_orm/tests/test_many2many.py
odoo/addons/test_orm/tests/test_onchange.py
odoo/addons/test_orm/tests/test_one2many.py
odoo/addons/test_orm/tests/test_performance.py
odoo/addons/test_orm/tests/test_properties.py
odoo/addons/test_orm/tests/test_qweb_float.py
odoo/addons/test_orm/tests/test_related_translation.py
odoo/addons/test_orm/tests/test_schema.py
odoo/addons/test_orm/tests/test_search.py
odoo/addons/test_orm/tests/test_sort.py
odoo/addons/test_orm/tests/test_timeit.py
odoo/addons/test_orm/tests/test_ui.py
odoo/addons/test_orm/tests/test_unity_read.py
odoo/addons/test_orm/tests/test_views.py
odoo/addons/test_orm/tests/test_web_save.py
odoo/addons/test_orm/views/test_orm_views.xml
odoo/addons/test_read_group/__init__.py
odoo/addons/test_read_group/__manifest__.py
odoo/addons/test_read_group/ir.model.access.csv
odoo/addons/test_read_group/models.py
odoo/addons/test_read_group/tests/__init__.py
odoo/addons/test_read_group/tests/test_formatted_read_group.py
odoo/addons/test_read_group/tests/test_override.py
odoo/addons/test_read_group/tests/test_private_read_group.py
odoo/addons/test_read_group/tests/test_read_grouping_sets.py
odoo/addons/test_read_group/tests/test_read_progress_bar.py
odoo/addons/test_read_group/tests/test_web_fill_temporal.py
odoo/addons/test_read_group/tests/test_web_group_expand.py
odoo/addons/test_read_group/tests/test_web_read_group.py
odoo/addons/test_rpc/__init__.py
odoo/addons/test_rpc/__manifest__.py
odoo/addons/test_rpc/ir.model.access.csv
odoo/addons/test_rpc/models.py
odoo/addons/test_rpc/tests/__init__.py
odoo/addons/test_rpc/tests/test_error.py
odoo/addons/test_rpc/tests/test_rpc_path.py
odoo/addons/test_search_panel/__init__.py
odoo/addons/test_search_panel/__manifest__.py
odoo/addons/test_search_panel/ir.model.access.csv
odoo/addons/test_search_panel/models/__init__.py
odoo/addons/test_search_panel/models/models.py
odoo/addons/test_search_panel/tests/__init__.py
odoo/addons/test_search_panel/tests/test_search_panel_select_multi_range.py
odoo/addons/test_search_panel/tests/test_search_panel_select_range.py
odoo/addons/test_testing_utilities/__init__.py
odoo/addons/test_testing_utilities/__manifest__.py
odoo/addons/test_testing_utilities/models/__init__.py
odoo/addons/test_testing_utilities/models/models.py
odoo/addons/test_testing_utilities/models/nested_o2m.py
odoo/addons/test_testing_utilities/security/ir.model.access.csv
odoo/addons/test_testing_utilities/tests/__init__.py
odoo/addons/test_testing_utilities/tests/test_env.py
odoo/addons/test_testing_utilities/tests/test_form_impl.py
odoo/addons/test_testing_utilities/tests/test_freeze_time.py
odoo/addons/test_testing_utilities/tests/test_lxml.py
odoo/addons/test_testing_utilities/tests/test_methods.py
odoo/addons/test_testing_utilities/tests/test_res_config.py
odoo/addons/test_testing_utilities/tests/test_xml_tools.py
odoo/addons/test_testing_utilities/views/menu.xml
odoo/addons/test_translation_import/__init__.py
odoo/addons/test_translation_import/__manifest__.py
odoo/addons/test_translation_import/view.xml
odoo/addons/test_translation_import/data/test.translation.import.model1-translated.csv
odoo/addons/test_translation_import/data/test.translation.import.model1.csv
odoo/addons/test_translation_import/data/test_translation_import_data.xml
odoo/addons/test_translation_import/data/files/test_spreadsheet_dashboard.json
odoo/addons/test_translation_import/data/files/test_spreadsheet_v16_dashboard.json
odoo/addons/test_translation_import/i18n/dot.csv
odoo/addons/test_translation_import/i18n/fr.po
odoo/addons/test_translation_import/i18n/fr_BE.po
odoo/addons/test_translation_import/i18n/fr_CA.po
odoo/addons/test_translation_import/i18n/test_translation_import.pot
odoo/addons/test_translation_import/i18n/tlh.po
odoo/addons/test_translation_import/models/__init__.py
odoo/addons/test_translation_import/models/models.py
odoo/addons/test_translation_import/security/ir.model.access.csv
odoo/addons/test_translation_import/static/src/js/js_codefile.js
odoo/addons/test_translation_import/static/src/xml/js_templates.xml
odoo/addons/test_translation_import/tests/__init__.py
odoo/addons/test_translation_import/tests/test_export_wizard.py
odoo/addons/test_translation_import/tests/test_term_count.py
odoo/addons/test_uninstall/__init__.py
odoo/addons/test_uninstall/__manifest__.py
odoo/addons/test_uninstall/ir.model.access.csv
odoo/addons/test_uninstall/models.py
odoo/api/__init__.py
odoo/cli/__init__.py
odoo/cli/cloc.py
odoo/cli/command.py
odoo/cli/db.py
odoo/cli/deploy.py
odoo/cli/help.py
odoo/cli/i18n.py
odoo/cli/module.py
odoo/cli/neutralize.py
odoo/cli/obfuscate.py
odoo/cli/populate.py
odoo/cli/scaffold.py
odoo/cli/server.py
odoo/cli/shell.py
odoo/cli/start.py
odoo/cli/upgrade_code.py
odoo/cli/templates/default/__init__.py.template
odoo/cli/templates/default/__manifest__.py.template
odoo/cli/templates/default/controllers/__init__.py.template
odoo/cli/templates/default/controllers/controllers.py.template
odoo/cli/templates/default/demo/demo.xml.template
odoo/cli/templates/default/models/__init__.py.template
odoo/cli/templates/default/models/models.py.template
odoo/cli/templates/default/security/ir.model.access.csv.template
odoo/cli/templates/default/views/templates.xml.template
odoo/cli/templates/default/views/views.xml.template
odoo/cli/templates/l10n_payroll/__init__.py.template
odoo/cli/templates/l10n_payroll/__manifest__.py.template
odoo/cli/templates/l10n_payroll/data/hr_payroll_structure_data.xml.template
odoo/cli/templates/l10n_payroll/data/hr_payroll_structure_type_data.xml.template
odoo/cli/templates/l10n_payroll/data/hr_rule_parameters_data.xml.template
odoo/cli/templates/l10n_payroll/data/hr_salary_rule_category_data.xml.template
odoo/cli/templates/l10n_payroll/data/hr_salary_rule_data.xml.template
odoo/cli/templates/l10n_payroll/data/l10n_{{code}}_hr_payroll_demo.xml.template
odoo/cli/templates/l10n_payroll/models/__init__.py.template
odoo/cli/templates/l10n_payroll/models/hr_payslip.py.template
odoo/cli/templates/l10n_payroll/models/hr_payslip_worked_days.py.template
odoo/cli/templates/l10n_payroll/models/hr_version.py.template
odoo/cli/templates/l10n_payroll/views/hr_payroll_report.xml.template
odoo/cli/templates/l10n_payroll/views/report_payslip_templates.xml.template
odoo/cli/templates/theme/__init__.py.template
odoo/cli/templates/theme/__manifest__.py.template
odoo/cli/templates/theme/demo/pages.xml.template
odoo/cli/templates/theme/static/src/scss/custom.scss.template
odoo/cli/templates/theme/views/options.xml.template
odoo/cli/templates/theme/views/snippets.xml.template
odoo/fields/__init__.py
odoo/models/__init__.py
odoo/modules/__init__.py
odoo/modules/db.py
odoo/modules/loading.py
odoo/modules/migration.py
odoo/modules/module.py
odoo/modules/module_graph.py
odoo/modules/neutralize.py
odoo/modules/registry/__init__.py
odoo/orm/__init__.py
odoo/orm/commands.py
odoo/orm/decorators.py
odoo/orm/domains.py
odoo/orm/environments.py
odoo/orm/fields.py
odoo/orm/fields_binary.py
odoo/orm/fields_misc.py
odoo/orm/fields_numeric.py
odoo/orm/fields_properties.py
odoo/orm/fields_reference.py
odoo/orm/fields_relational.py
odoo/orm/fields_selection.py
odoo/orm/fields_temporal.py
odoo/orm/fields_textual.py
odoo/orm/identifiers.py
odoo/orm/model_classes.py
odoo/orm/models.py
odoo/orm/models_transient.py
odoo/orm/registry.py
odoo/orm/table_objects.py
odoo/orm/types.py
odoo/orm/utils.py
odoo/osv/__init__.py
odoo/osv/expression.py
odoo/service/__init__.py
odoo/service/common.py
odoo/service/db.py
odoo/service/model.py
odoo/service/security.py
odoo/service/server.py
odoo/tests/__init__.py
odoo/tests/case.py
odoo/tests/common.py
odoo/tests/dummy.js
odoo/tests/dummy.xml
odoo/tests/form.py
odoo/tests/loader.py
odoo/tests/result.py
odoo/tests/shell.py
odoo/tests/suite.py
odoo/tests/tag_selector.py
odoo/tests/test_cursor.py
odoo/tests/test_module_operations.py
odoo/tools/__init__.py
odoo/tools/appdirs.py
odoo/tools/barcode.py
odoo/tools/cache.py
odoo/tools/cloc.py
odoo/tools/config.py
odoo/tools/constants.py
odoo/tools/convert.py
odoo/tools/date_utils.py
odoo/tools/facade.py
odoo/tools/float_utils.py
odoo/tools/func.py
odoo/tools/gc.py
odoo/tools/i18n.py
odoo/tools/image.py
odoo/tools/intervals.py
odoo/tools/js_transpiler.py
odoo/tools/json.py
odoo/tools/lru.py
odoo/tools/mail.py
odoo/tools/mimetypes.py
odoo/tools/misc.py
odoo/tools/osutil.py
odoo/tools/parse_version.py
odoo/tools/populate.py
odoo/tools/profiler.py
odoo/tools/pycompat.py
odoo/tools/query.py
odoo/tools/rendering_tools.py
odoo/tools/safe_eval.py
odoo/tools/set_expression.py
odoo/tools/sourcemap_generator.py
odoo/tools/speedscope.py
odoo/tools/sql.py
odoo/tools/template_inheritance.py
odoo/tools/test_reports.py
odoo/tools/translate.py
odoo/tools/urls.py
odoo/tools/view_validation.py
odoo/tools/which.py
odoo/tools/xml_utils.py
odoo/tools/_vendor/__init__.py
odoo/tools/_vendor/send_file.py
odoo/tools/_vendor/sessions.py
odoo/tools/_vendor/useragents.py
odoo/tools/arabic_reshaper/__init__.py
odoo/tools/arabic_reshaper/letters.py
odoo/tools/babel/__init__.py
odoo/tools/babel/javascript_extractor.py
odoo/tools/babel/python_extractor.py
odoo/tools/data/files/sRGB2014.icc
odoo/tools/data/files/sRGB2014.icc.LICENSE
odoo/tools/pdf/__init__.py
odoo/tools/pdf/_pypdf.py
odoo/tools/pdf/_pypdf2_1.py
odoo/tools/pdf/_pypdf2_2.py
odoo/tools/pdf/signature.py
odoo/tools/zeep/__init__.py
odoo/tools/zeep/client.py
odoo/tools/zeep/exceptions.py
odoo/tools/zeep/helpers.py
odoo/tools/zeep/ns.py
odoo/tools/zeep/wsa.py
odoo/tools/zeep/wsdl/__init__.py
odoo/tools/zeep/wsdl/utils.py
odoo/tools/zeep/wsse/__init__.py
odoo/tools/zeep/wsse/username/__init__.py
odoo/upgrade/.gitkeep
odoo/upgrade_code/17.5-00-example.py
odoo/upgrade_code/17.5-01-tree-to-list.py
odoo/upgrade_code/18.1-00-sql-constraint.py
odoo/upgrade_code/18.1-02-route-jsonrpc.py
odoo/upgrade_code/18.2-00-l10n-translate.py
odoo/upgrade_code/18.3-00-l10n-fiscal-position-taxes.py
odoo/upgrade_code/18.5-00-deprecated-properties.py
odoo/upgrade_code/18.5-00-domain-dynamic-dates.py
odoo/upgrade_code/18.5-00-no-tax-tag-invert.py
setup/odoo
setup/odoo-wsgi.example.py
setup/package.py
setup/requirements-check.py