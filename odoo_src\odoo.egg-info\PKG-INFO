Metadata-Version: 2.4
Name: odoo
Version: 19.0
Summary: Odoo Server
Home-page: https://www.odoo.com
Author: OpenERP S.A.
Author-email: <EMAIL>
License: LGPL-3
Classifier: Development Status :: 5 - Production/Stable
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3
Classifier: Programming Language :: Python
Requires-Python: >=3.10
License-File: LICENSE
Requires-Dist: asn1crypto
Requires-Dist: babel>=1.0
Requires-Dist: cbor2
Requires-Dist: chardet
Requires-Dist: cryptography
Requires-Dist: docutils
Requires-Dist: geoip2
Requires-Dist: gevent
Requires-Dist: greenlet
Requires-Dist: idna
Requires-Dist: Jinja2
Requires-Dist: lxml
Requires-Dist: lxml_html_clean
Requires-Dist: libsass
Requires-Dist: MarkupSafe
Requires-Dist: num2words
Requires-Dist: ofxparse
Requires-Dist: openpyxl
Requires-Dist: passlib
Requires-Dist: pillow
Requires-Dist: polib
Requires-Dist: psutil
Requires-Dist: psycopg2>=2.2
Requires-Dist: pyopenssl
Requires-Dist: PyPDF2
Requires-Dist: pyserial
Requires-Dist: python-dateutil
Requires-Dist: python-stdnum
Requires-Dist: pytz
Requires-Dist: pyusb>=1.0.0b1
Requires-Dist: qrcode
Requires-Dist: reportlab
Requires-Dist: rjsmin
Requires-Dist: requests
Requires-Dist: urllib3
Requires-Dist: vobject
Requires-Dist: werkzeug
Requires-Dist: xlrd
Requires-Dist: xlsxwriter
Requires-Dist: xlwt
Requires-Dist: zeep
Provides-Extra: ldap
Requires-Dist: python-ldap; extra == "ldap"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

Odoo is a complete ERP and CRM. The main features are accounting (analytic
and financial), stock management, sales and purchases management, tasks
automation, marketing campaigns, help desk, POS, etc. Technical features include
a distributed server, an object database, a dynamic GUI,
customizable reports, and XML-RPC interfaces.
